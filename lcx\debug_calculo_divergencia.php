<?php
session_start();
require_once '../config/database.php';
require_once 'funcoes_divergencia_saldos.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

echo "<h2>Debug - Cálculo de Divergência PA 09</h2>";

try {
    // Primeiro, vamos ver todos os PAs disponíveis
    $stmt = $pdo->query("SELECT id, nome, numero FROM pontos_atendimento ORDER BY numero");
    $pas = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h3>Pontos de Atendimento Disponíveis:</h3>";
    echo "<ul>";
    foreach ($pas as $pa) {
        echo "<li>ID: {$pa['id']}, Nome: {$pa['nome']}, Número: {$pa['numero']}</li>";
    }
    echo "</ul>";

    // Buscar todos os livros para ver qual PA tem livro
    $stmt = $pdo->query("SELECT lc.id, lc.ponto_atendimento_id, lc.tipo, lc.status, pa.nome, pa.numero
                         FROM lcx_livros_caixa lc
                         JOIN pontos_atendimento pa ON lc.ponto_atendimento_id = pa.id
                         WHERE lc.tipo = 'normal'
                         ORDER BY pa.numero");
    $livros = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h3>Livros Normais Existentes:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Livro ID</th><th>PA ID</th><th>PA Nome</th><th>PA Número</th><th>Status</th></tr>";
    foreach ($livros as $livro) {
        echo "<tr>";
        echo "<td>{$livro['id']}</td>";
        echo "<td>{$livro['ponto_atendimento_id']}</td>";
        echo "<td>{$livro['nome']}</td>";
        echo "<td>{$livro['numero']}</td>";
        echo "<td>{$livro['status']}</td>";
        echo "</tr>";
    }
    echo "</table>";

    // Buscar livro do PA com número 9 (não ID 9)
    $stmt = $pdo->prepare("
        SELECT lc.id, lc.ponto_atendimento_id
        FROM lcx_livros_caixa lc
        JOIN pontos_atendimento pa ON lc.ponto_atendimento_id = pa.id
        WHERE pa.numero = '9' AND lc.tipo = 'normal'
        ORDER BY lc.status = 'aberto' DESC, lc.id DESC
        LIMIT 1
    ");
    $stmt->execute();
    $livro_pa = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$livro_pa) {
        echo "<p style='color: red;'>Livro do PA número 9 não encontrado</p>";
        exit;
    }
    
    echo "<h3>Livro PA 09 ID: {$livro_pa['id']}, PA ID: {$livro_pa['ponto_atendimento_id']}</h3>";

    $pa_id = $livro_pa['ponto_atendimento_id'];

    // Buscar dados do livro PA
    $stmt = $pdo->prepare("SELECT * FROM lcx_livros_caixa WHERE id = ?");
    $stmt->execute([$livro_pa['id']]);
    $dados_livro_pa = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h4>Dados do Livro PA:</h4>";
    echo "<ul>";
    echo "<li>Saldo Inicial: R$ " . number_format($dados_livro_pa['saldo_inicial'], 2, ',', '.') . "</li>";
    echo "<li>Saldo Atual: R$ " . number_format($dados_livro_pa['saldo_atual'], 2, ',', '.') . "</li>";
    echo "</ul>";
    
    // Buscar livro master
    $stmt = $pdo->prepare("SELECT id, nome, saldo_inicial FROM lcx_livros_caixa WHERE tipo = 'master' AND status = 'aberto' ORDER BY created_at DESC LIMIT 1");
    $stmt->execute();
    $livro_master = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$livro_master) {
        echo "<p>Livro master não encontrado</p>";
        exit;
    }
    
    echo "<h4>Livro Master:</h4>";
    echo "<ul>";
    echo "<li>ID: {$livro_master['id']}</li>";
    echo "<li>Nome: {$livro_master['nome']}</li>";
    echo "<li>Saldo Inicial: R$ " . number_format($livro_master['saldo_inicial'], 2, ',', '.') . "</li>";
    echo "</ul>";
    
    // Buscar movimentações do PA 09 no master
    $stmt = $pdo->prepare("
        SELECT id, tipo, valor, categoria, data_competencia, data_movimentacao, saldo_posterior
        FROM lcx_movimentacoes
        WHERE livro_caixa_id = ?
        AND categoria LIKE '%PA: 9%'
        ORDER BY data_competencia DESC, data_movimentacao DESC, id DESC
    ");
    $stmt->execute([$livro_master['id']]);
    $movimentacoes_master = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h4>Movimentações PA 09 no Master:</h4>";
    if (empty($movimentacoes_master)) {
        echo "<p>Nenhuma movimentação encontrada</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Data</th><th>Tipo</th><th>Valor</th><th>Categoria</th><th>Saldo Posterior</th></tr>";
        foreach ($movimentacoes_master as $mov) {
            echo "<tr>";
            echo "<td>{$mov['id']}</td>";
            echo "<td>{$mov['data_competencia']}</td>";
            echo "<td>{$mov['tipo']}</td>";
            echo "<td>R$ " . number_format($mov['valor'], 2, ',', '.') . "</td>";
            echo "<td>{$mov['categoria']}</td>";
            echo "<td>R$ " . number_format($mov['saldo_posterior'], 2, ',', '.') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Última movimentação
        $ultima_mov = $movimentacoes_master[0];
        echo "<h4>Última Movimentação:</h4>";
        echo "<ul>";
        echo "<li>Saldo Posterior: R$ " . number_format($ultima_mov['saldo_posterior'], 2, ',', '.') . "</li>";
        echo "</ul>";
    }
    
    // Testar função de cálculo
    echo "<h4>Teste da Função calcularSaldoEsperadoAgenciaPorData:</h4>";
    $saldo_esperado = calcularSaldoEsperadoAgenciaPorData($pdo, $livro_master['id'], $pa_id, date('Y-m-d'));
    echo "<p>Saldo Esperado Calculado: R$ " . number_format($saldo_esperado, 2, ',', '.') . "</p>";

    // Testar função que está sendo usada na interface
    echo "<h4>Teste da Função verificarDivergenciaSaldo (usada na interface):</h4>";
    $divergencia_interface = verificarDivergenciaSaldo($pdo, $livro_pa['id'], $pa_id);

    if ($divergencia_interface) {
        echo "<ul>";
        echo "<li>Tem Divergência: " . ($divergencia_interface['tem_divergencia'] ? 'SIM' : 'NÃO') . "</li>";
        echo "<li>Saldo Agência: R$ " . number_format($divergencia_interface['saldo_agencia'], 2, ',', '.') . "</li>";
        echo "<li>Saldo Esperado: R$ " . number_format($divergencia_interface['saldo_esperado'], 2, ',', '.') . "</li>";
        echo "<li>Diferença: R$ " . number_format($divergencia_interface['diferenca'], 2, ',', '.') . "</li>";
        echo "</ul>";
    } else {
        echo "<p>Função retornou null</p>";
    }

    // Testar função completa
    echo "<h4>Teste da Função verificarDivergenciaSaldoPorData:</h4>";
    $divergencia = verificarDivergenciaSaldoPorData($pdo, $livro_pa['id'], $pa_id, date('Y-m-d'));
    
    if ($divergencia) {
        echo "<ul>";
        echo "<li>Tem Divergência: " . ($divergencia['tem_divergencia'] ? 'SIM' : 'NÃO') . "</li>";
        echo "<li>Saldo Agência: R$ " . number_format($divergencia['saldo_agencia'], 2, ',', '.') . "</li>";
        echo "<li>Saldo Esperado: R$ " . number_format($divergencia['saldo_esperado'], 2, ',', '.') . "</li>";
        echo "<li>Diferença: R$ " . number_format($divergencia['diferenca'], 2, ',', '.') . "</li>";
        echo "</ul>";
    } else {
        echo "<p>Função retornou null</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Erro: " . $e->getMessage() . "</p>";
}
?>
