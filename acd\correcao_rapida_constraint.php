<?php
/**
 * Correção rápida para o erro de constraint na tabela acd_usuario_pa
 * Remove apenas a constraint problemática e corrige dados órfãos
 */

require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

// Verificar permissões
session_start();
if (!isset($_SESSION['user_id'])) {
    die("Acesso negado. Faça login para continuar.");
}

if (!checkACDPermission($_SESSION['user_id'])) {
    die("Acesso negado. Você não tem permissão para acessar o sistema ACD.");
}

if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    die("Acesso negado. Apenas administradores e gestores podem executar esta correção.");
}

echo "<h2>⚡ Correção Rápida - Constraint acd_usuario_pa</h2>";
echo "<p>Esta correção remove apenas a constraint problemática e corrige dados órfãos.</p>";

try {
    echo "<h3>🔍 Diagnóstico</h3>";
    
    // Verificar se a tabela existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'acd_usuario_pa'");
    if ($stmt->rowCount() == 0) {
        echo "<p>❌ Tabela acd_usuario_pa não existe!</p>";
        exit;
    }
    
    echo "<p>✅ Tabela acd_usuario_pa encontrada</p>";
    
    // Verificar constraints
    $stmt = $pdo->query("
        SELECT CONSTRAINT_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'acd_usuario_pa' 
        AND CONSTRAINT_NAME = 'acd_usuario_pa_ibfk_3'
    ");
    
    $constraint_existe = $stmt->rowCount() > 0;
    
    if ($constraint_existe) {
        echo "<p>⚠️ Constraint problemática encontrada: acd_usuario_pa_ibfk_3</p>";
    } else {
        echo "<p>✅ Constraint problemática não encontrada</p>";
    }
    
    echo "<h3>🔧 Aplicando Correção</h3>";
    
    // 1. Remover constraint problemática
    if ($constraint_existe) {
        echo "<p>🗑️ Removendo constraint problemática...</p>";
        try {
            $pdo->exec("ALTER TABLE acd_usuario_pa DROP FOREIGN KEY acd_usuario_pa_ibfk_3");
            echo "<p>✅ Constraint removida com sucesso</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ Erro ao remover constraint: " . $e->getMessage() . "</p>";
        }
    }
    
    // 2. Verificar e corrigir dados órfãos
    echo "<p>🔍 Verificando dados órfãos...</p>";
    
    $stmt = $pdo->query("
        SELECT COUNT(*) 
        FROM acd_usuario_pa 
        WHERE usuario_vinculo IS NOT NULL 
        AND usuario_vinculo NOT IN (SELECT id FROM usuarios)
    ");
    $orfaos = $stmt->fetchColumn();
    
    if ($orfaos > 0) {
        echo "<p>⚠️ Encontrados $orfaos registros órfãos</p>";
        echo "<p>🔧 Corrigindo dados órfãos...</p>";
        
        // Definir usuario_vinculo como 1 (admin) para registros órfãos
        $stmt = $pdo->prepare("
            UPDATE acd_usuario_pa 
            SET usuario_vinculo = 1 
            WHERE usuario_vinculo IS NOT NULL 
            AND usuario_vinculo NOT IN (SELECT id FROM usuarios)
        ");
        $stmt->execute();
        $corrigidos = $stmt->rowCount();
        
        echo "<p>✅ $corrigidos registros corrigidos</p>";
    } else {
        echo "<p>✅ Nenhum dado órfão encontrado</p>";
    }
    
    // 3. Testar criação de vínculo
    echo "<h3>🧪 Teste de Funcionamento</h3>";
    
    $stmt = $pdo->query("SELECT id FROM usuarios LIMIT 1");
    $usuario_teste = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT id FROM pontos_atendimento LIMIT 1");
    $pa_teste = $stmt->fetchColumn();
    
    if ($usuario_teste && $pa_teste) {
        try {
            echo "<p>🧪 Testando inserção...</p>";
            
            $stmt = $pdo->prepare("
                INSERT INTO acd_usuario_pa (usuario_id, pa_id, data_vinculo, usuario_vinculo, status) 
                VALUES (?, ?, NOW(), ?, 1)
            ");
            $stmt->execute([$usuario_teste, $pa_teste, $_SESSION['user_id']]);
            
            $teste_id = $pdo->lastInsertId();
            echo "<p>✅ Teste bem-sucedido (ID: $teste_id)</p>";
            
            // Remover teste
            $pdo->prepare("DELETE FROM acd_usuario_pa WHERE id = ?")->execute([$teste_id]);
            echo "<p>🗑️ Registro de teste removido</p>";
            
        } catch (Exception $e) {
            echo "<p>❌ Erro no teste: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>⚠️ Não foi possível realizar teste (sem dados disponíveis)</p>";
    }
    
    echo "<h3>✅ Correção Concluída</h3>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4>🎉 Sucesso!</h4>";
    echo "<p>A correção rápida foi aplicada:</p>";
    echo "<ul>";
    echo "<li>✅ Constraint problemática removida</li>";
    echo "<li>✅ Dados órfãos corrigidos</li>";
    echo "<li>✅ Sistema testado</li>";
    echo "</ul>";
    echo "<p><strong>Agora você pode criar vínculos normalmente!</strong></p>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4>⚠️ Observação Importante</h4>";
    echo "<p>Esta foi uma correção rápida que mantém a estrutura atual.</p>";
    echo "<p>Para uma solução mais robusta com histórico completo, considere executar a reestruturação completa.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3>❌ Erro durante a correção:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "<br><a href='gerenciar_vinculos.php'>← Voltar para Gerenciar Vínculos</a>";
?>
