<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

$acordo_id = isset($_GET['acordo_id']) ? intval($_GET['acordo_id']) : 0;

if (!$acordo_id) {
    echo '<div class="alert alert-danger">ID do acordo não especificado.</div>';
    exit;
}

// Buscar informações do acordo
$stmt = $pdo->prepare("
    SELECT a.*, p.associado_nome as cliente_nome, p.numero_processo 
    FROM cbp_acordos a
    INNER JOIN cbp_processos_judiciais p ON a.processo_id = p.id
    WHERE a.id = ?
");
$stmt->execute([$acordo_id]);
$acordo = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$acordo) {
    echo '<div class="alert alert-danger">Acordo não encontrado.</div>';
    exit;
}

// Buscar as parcelas do acordo
$stmt = $pdo->prepare("
    SELECT * FROM cbp_parcelas_acordo 
    WHERE acordo_id = ? 
    ORDER BY numero_parcela
");
$stmt->execute([$acordo_id]);
$parcelas = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Verificar se o acordo tem personalização ativada
$personalizacao_ativa = isset($acordo['personalizar_parcelas']) && $acordo['personalizar_parcelas'] == 1;
$tipo_personalizacao = $personalizacao_ativa ? $acordo['tipo_personalizacao'] : '';

// Flag para saber o que será personalizado
$personalizar_valores = $personalizacao_ativa && ($tipo_personalizacao === 'valores' || $tipo_personalizacao === 'ambos');
$personalizar_datas = $personalizacao_ativa && ($tipo_personalizacao === 'datas' || $tipo_personalizacao === 'ambos');
?>

<div class="modal-header">
    <h5 class="modal-title">Personalizar Parcelas do Acordo</h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
</div>
<div class="modal-body">
    <div class="card mb-3">
        <div class="card-header">
            <h6 class="mb-0">Informações do Acordo</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Cliente:</strong> <?php echo $acordo['cliente_nome']; ?></p>
                    <p><strong>Nº do Processo:</strong> <?php echo $acordo['numero_processo']; ?></p>
                    <p><strong>Data do Acordo:</strong> <?php echo date('d/m/Y', strtotime($acordo['data_acordo'])); ?></p>
                </div>
                <div class="col-md-6">
                    <p><strong>Valor do Acordo:</strong> R$ <?php echo number_format($acordo['valor_acordo'], 2, ',', '.'); ?></p>
                    <p><strong>Nº de Parcelas:</strong> <?php echo $acordo['quantidade_parcelas']; ?></p>
                    <p><strong>Nº de Repactuação:</strong> <?php echo $acordo['numero_repactuacao']; ?></p>
                </div>
            </div>
        </div>
    </div>

    <?php if ($personalizacao_ativa): ?>
        <form id="formPersonalizarParcelas">
            <input type="hidden" name="acordo_id" value="<?php echo $acordo_id; ?>">
            
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Nº</th>
                            <?php if ($personalizar_datas): ?>
                                <th>Data de Vencimento</th>
                            <?php else: ?>
                                <th>Vencimento</th>
                            <?php endif; ?>
                            <?php if ($personalizar_valores): ?>
                                <th>Valor (R$)</th>
                            <?php else: ?>
                                <th>Valor</th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($parcelas as $parcela): ?>
                            <tr>
                                <td><?php echo $parcela['numero_parcela']; ?></td>
                                <?php if ($personalizar_datas): ?>
                                    <td>
                                        <input type="date" name="data_vencimento[<?php echo $parcela['id']; ?>]" 
                                               class="form-control form-control-sm" 
                                               value="<?php echo $parcela['data_vencimento']; ?>" required>
                                    </td>
                                <?php else: ?>
                                    <td><?php echo date('d/m/Y', strtotime($parcela['data_vencimento'])); ?></td>
                                <?php endif; ?>
                                
                                <?php if ($personalizar_valores): ?>
                                    <td>
                                        <input type="text" name="valor_parcela[<?php echo $parcela['id']; ?>]" 
                                               class="form-control form-control-sm money" 
                                               value="<?php echo number_format($parcela['valor_parcela'], 2, ',', '.'); ?>" required>
                                    </td>
                                <?php else: ?>
                                    <td>R$ <?php echo number_format($parcela['valor_parcela'], 2, ',', '.'); ?></td>
                                <?php endif; ?>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-end mt-3">
                <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">Cancelar</button>
                <button type="submit" class="btn btn-primary">Salvar Alterações</button>
            </div>
        </form>

        <script>
            $(document).ready(function() {
                // Aplicar máscara para valores monetários
                $('.money').inputmask({
                    alias: 'currency',
                    radixPoint: ',',
                    groupSeparator: '.',
                    allowMinus: false,
                    prefix: 'R$ ',
                    digits: 2,
                    digitsOptional: false,
                    rightAlign: false,
                    unmaskAsNumber: true
                });

                // Submeter formulário de personalização
                $('#formPersonalizarParcelas').on('submit', function(e) {
                    e.preventDefault();

                    const form = $(this);
                    const submitButton = form.find('button[type="submit"]');
                    const originalText = submitButton.html();

                    // Desabilitar botão e mostrar loading
                    submitButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Aguarde...');

                    // Criar array para armazenar as parcelas atualizadas
                    const parcelas = [];
                    
                    <?php if ($personalizar_valores): ?>
                    // Obter valores de parcelas sem máscara
                    $('input[name^="valor_parcela"]').each(function() {
                        const parcela_id = $(this).attr('name').match(/\[(\d+)\]/)[1];
                        const valor = $(this).inputmask('unmaskedvalue');
                        
                        parcelas.push({
                            id: parcela_id,
                            valor: valor
                            <?php if ($personalizar_datas): ?>
                            , data: $('input[name="data_vencimento[' + parcela_id + ']"]').val()
                            <?php endif; ?>
                        });
                    });
                    <?php elseif ($personalizar_datas): ?>
                    // Obter apenas datas de vencimento
                    $('input[name^="data_vencimento"]').each(function() {
                        const parcela_id = $(this).attr('name').match(/\[(\d+)\]/)[1];
                        parcelas.push({
                            id: parcela_id,
                            data: $(this).val()
                        });
                    });
                    <?php endif; ?>

                    // Enviar dados via AJAX
                    $.ajax({
                        url: 'ajax/gerenciar_parcelas.php',
                        type: 'POST',
                        data: {
                            acao: 'personalizar_parcelas',
                            acordo_id: <?php echo $acordo_id; ?>,
                            parcelas: JSON.stringify(parcelas)
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    icon: 'success',
                                    title: 'Sucesso!',
                                    text: response.message
                                }).then(() => {
                                    // Fechar modal
                                    $('.modal').modal('hide');
                                    // Recarregar a página se necessário
                                    if (response.reload) {
                                        window.location.reload();
                                    }
                                });
                            } else {
                                Swal.fire('Erro!', response.message, 'error');
                            }
                        },
                        error: function(xhr) {
                            let errorMessage = 'Erro ao processar requisição.';
                            try {
                                const response = JSON.parse(xhr.responseText);
                                if (response.message) {
                                    errorMessage = response.message;
                                }
                            } catch (e) {
                                console.error('Erro ao parsear resposta:', xhr.responseText);
                            }
                            Swal.fire('Erro!', errorMessage, 'error');
                        },
                        complete: function() {
                            // Restaurar botão
                            submitButton.prop('disabled', false).html(originalText);
                        }
                    });
                });
            });
        </script>
    <?php else: ?>
        <div class="alert alert-warning">
            Este acordo não está configurado para personalização de parcelas. Você pode ativar esta opção ao editar o acordo.
        </div>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Nº</th>
                        <th>Data de Vencimento</th>
                        <th>Valor (R$)</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($parcelas as $parcela): ?>
                        <tr>
                            <td><?php echo $parcela['numero_parcela']; ?></td>
                            <td><?php echo date('d/m/Y', strtotime($parcela['data_vencimento'])); ?></td>
                            <td>R$ <?php echo number_format($parcela['valor_parcela'], 2, ',', '.'); ?></td>
                            <td><?php echo $parcela['status']; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <div class="d-flex justify-content-end mt-3">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
        </div>
    <?php endif; ?>
</div> 