<?php
session_start();
require_once '../config/database.php';
require_once 'functions/logs.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user_id = $_SESSION['user_id'];

// Verificar permissões LCX do usuário
try {
    $stmt = $pdo->prepare("SELECT nivel_permissao FROM lcx_permissoes WHERE usuario_id = ? AND ativo = 1");
    $stmt->execute([$user_id]);
    $permissao_lcx = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$permissao_lcx) {
        header('Location: ../dashboard.php?erro=sem_permissao_lcx');
        exit;
    }
    
    $nivel_permissao = $permissao_lcx['nivel_permissao'];
    
    // Apenas gestores e administradores podem ver logs
    if (!in_array($nivel_permissao, ['gestor', 'gestor_master', 'admin'])) {
        header('Location: ../dashboard.php?erro=sem_permissao_admin');
        exit;
    }
    
} catch (Exception $e) {
    error_log("Erro ao verificar permissões LCX: " . $e->getMessage());
    header('Location: ../dashboard.php?erro=erro_sistema');
    exit;
}

// Parâmetros de filtro
$filtros = [
    'data_inicio' => $_GET['data_inicio'] ?? '',
    'data_fim' => $_GET['data_fim'] ?? '',
    'usuario_id' => $_GET['usuario_id'] ?? '',
    'acao' => $_GET['acao'] ?? '',
    'livro_id' => $_GET['livro_id'] ?? ''
];

// Paginação
$pagina_atual = max(1, intval($_GET['pagina'] ?? 1));
$registros_por_pagina = 50;
$offset = ($pagina_atual - 1) * $registros_por_pagina;

// Buscar logs
$logs = buscar_logs_lcx($pdo, $filtros, $registros_por_pagina, $offset);
$total_logs = contar_logs_lcx($pdo, $filtros);
$total_paginas = ceil($total_logs / $registros_por_pagina);

// Buscar usuários para filtro
$stmt = $pdo->query("
    SELECT DISTINCT u.id, u.nome_completo 
    FROM usuarios u 
    JOIN logs l ON u.id = l.usuario_id 
    WHERE l.acao LIKE 'LCX:%' 
    ORDER BY u.nome_completo
");
$usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Buscar livros para filtro
$stmt = $pdo->query("
    SELECT id, nome 
    FROM lcx_livros_caixa 
    ORDER BY nome
");
$livros = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Ações disponíveis para filtro
$acoes_disponiveis = [
    'criar_livro' => 'Criar Livro',
    'fechar_livro' => 'Fechar Livro',
    'nova_movimentacao' => 'Nova Movimentação',
    'editar_movimentacao' => 'Editar Movimentação',
    'edicao_retroativa' => 'Edição Retroativa',
    'excluir_movimentacao' => 'Excluir Movimentação',
    'designar_tesoureiro' => 'Designar Tesoureiro',
    'remover_tesoureiro' => 'Remover Tesoureiro',
    'alterar_permissao' => 'Alterar Permissão',
    'habilitar_edicao_geral' => 'Habilitar Edição Geral',
    'habilitar_edicao_especifica' => 'Habilitar Edição Específica',
    'desabilitar_edicao' => 'Desabilitar Edição',
    'imprimir_livro' => 'Imprimir Livro',
    'importar_dados' => 'Importar Dados',
    'acesso_sistema' => 'Acesso ao Sistema',
    'acesso_negado' => 'Acesso Negado'
];
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logs do Sistema LCX - Sicoob</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/Sicoob.ico">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --sicoob-turquesa: #00A091;
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #7DB61C;
            --sicoob-branco: #FFFFFF;
        }

        body {
            background: linear-gradient(135deg, #f8fffe 0%, rgba(0, 160, 145, 0.03) 100%);
            font-family: 'Open Sans', sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--sicoob-turquesa) 0%, var(--sicoob-verde-escuro) 100%);
            box-shadow: 0 4px 20px rgba(0, 54, 65, 0.2);
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 6px 20px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, var(--sicoob-turquesa) 0%, var(--sicoob-verde-escuro) 100%);
            color: white;
            border: none;
            padding: 20px;
        }

        .btn-primary {
            background: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
        }

        .btn-primary:hover {
            background: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
        }

        .table th {
            background: var(--sicoob-turquesa);
            color: white;
            border: none;
            font-weight: 600;
        }

        .log-entry {
            border-left: 4px solid var(--sicoob-turquesa);
            padding: 15px;
            margin-bottom: 10px;
            background: white;
            border-radius: 0 8px 8px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .log-action {
            font-weight: 600;
            color: var(--sicoob-verde-escuro);
        }

        .log-details {
            color: #666;
            margin: 8px 0;
            font-size: 14px;
        }

        .log-meta {
            font-size: 12px;
            color: #999;
        }

        .pagination .page-link {
            color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
        }

        .pagination .page-item.active .page-link {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
        }

        .filter-section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        /* Estilos específicos para logs de edição retroativa */
        .log-entry.border-warning {
            border-left-color: #ffc107 !important;
            background: linear-gradient(90deg, rgba(255, 193, 7, 0.05) 0%, white 10%);
        }

        .badge.bg-warning.text-dark {
            font-size: 0.7rem;
            font-weight: 600;
        }

        .log-entry.border-warning .log-action {
            color: #856404;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand d-flex align-items-center" href="index.php">
                <img src="../assets/images/logo-sicoob.png" alt="Sicoob" height="40" class="me-2">
                <span class="fw-bold">Sistema LCX - Logs</span>
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-arrow-left me-1"></i> Voltar ao Sistema
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid px-4 py-4">
        <!-- Filtros -->
        <div class="filter-section">
            <h5 class="mb-3"><i class="fas fa-filter me-2"></i>Filtros</h5>
            <form method="GET" class="row g-3">
                <div class="col-md-2">
                    <label for="data_inicio" class="form-label">Data Início</label>
                    <input type="date" class="form-control" id="data_inicio" name="data_inicio" 
                           value="<?php echo htmlspecialchars($filtros['data_inicio']); ?>">
                </div>
                
                <div class="col-md-2">
                    <label for="data_fim" class="form-label">Data Fim</label>
                    <input type="date" class="form-control" id="data_fim" name="data_fim" 
                           value="<?php echo htmlspecialchars($filtros['data_fim']); ?>">
                </div>
                
                <div class="col-md-3">
                    <label for="usuario_id" class="form-label">Usuário</label>
                    <select class="form-select" id="usuario_id" name="usuario_id">
                        <option value="">Todos os usuários</option>
                        <?php foreach ($usuarios as $usuario): ?>
                            <option value="<?php echo $usuario['id']; ?>" 
                                    <?php echo $filtros['usuario_id'] == $usuario['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($usuario['nome_completo']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="acao" class="form-label">Ação</label>
                    <select class="form-select" id="acao" name="acao">
                        <option value="">Todas as ações</option>
                        <?php foreach ($acoes_disponiveis as $valor => $nome): ?>
                            <option value="<?php echo $valor; ?>" 
                                    <?php echo $filtros['acao'] == $valor ? 'selected' : ''; ?>>
                                <?php echo $nome; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="livro_id" class="form-label">Livro</label>
                    <select class="form-select" id="livro_id" name="livro_id">
                        <option value="">Todos os livros</option>
                        <?php foreach ($livros as $livro): ?>
                            <option value="<?php echo $livro['id']; ?>" 
                                    <?php echo $filtros['livro_id'] == $livro['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($livro['nome']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>

        <!-- Resultados -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt me-2"></i>
                    Logs do Sistema LCX
                    <span class="badge bg-light text-dark ms-2"><?php echo number_format($total_logs); ?> registros</span>
                </h5>
            </div>
            <div class="card-body p-0">
                <?php if (empty($logs)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum log encontrado</h5>
                        <p class="text-muted">Tente ajustar os filtros para encontrar os registros desejados.</p>
                    </div>
                <?php else: ?>
                    <div class="p-3">
                        <?php foreach ($logs as $log): ?>
                            <?php
                            $acao_limpa = str_replace('LCX: ', '', $log['acao']);
                            $eh_edicao_retroativa = in_array($acao_limpa, ['habilitar_edicao_geral', 'habilitar_edicao_especifica', 'desabilitar_edicao', 'edicao_retroativa']);
                            $classe_especial = $eh_edicao_retroativa ? 'border-warning' : '';
                            ?>
                            <div class="log-entry <?php echo $classe_especial; ?>">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <div class="log-action">
                                            <?php if ($eh_edicao_retroativa): ?>
                                                <i class="fas fa-exclamation-triangle text-warning me-1"></i>
                                            <?php endif; ?>
                                            <?php
                                            echo $acoes_disponiveis[$acao_limpa] ?? ucwords(str_replace('_', ' ', $acao_limpa));
                                            ?>
                                            <?php if ($eh_edicao_retroativa): ?>
                                                <span class="badge bg-warning text-dark ms-2">EDIÇÃO RETROATIVA</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="log-details">
                                            <?php echo htmlspecialchars($log['detalhes']); ?>
                                        </div>
                                        <div class="log-meta">
                                            <i class="fas fa-user me-1"></i>
                                            <?php echo htmlspecialchars($log['usuario_nome']); ?>
                                            <?php if (strpos($log['detalhes'], 'IP:') !== false): ?>
                                                <?php
                                                preg_match('/IP: ([^\|]+)/', $log['detalhes'], $matches);
                                                if (isset($matches[1])) {
                                                    echo ' | <i class="fas fa-globe me-1"></i>' . trim($matches[1]);
                                                }
                                                ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            <?php echo date('d/m/Y H:i:s', strtotime($log['data_hora'])); ?>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Paginação -->
                    <?php if ($total_paginas > 1): ?>
                        <div class="d-flex justify-content-center p-3 border-top">
                            <nav aria-label="Navegação de logs">
                                <ul class="pagination mb-0">
                                    <?php if ($pagina_atual > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($filtros, ['pagina' => $pagina_atual - 1])); ?>">
                                                <i class="fas fa-chevron-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $pagina_atual - 2); $i <= min($total_paginas, $pagina_atual + 2); $i++): ?>
                                        <li class="page-item <?php echo $i == $pagina_atual ? 'active' : ''; ?>">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($filtros, ['pagina' => $i])); ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($pagina_atual < $total_paginas): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?<?php echo http_build_query(array_merge($filtros, ['pagina' => $pagina_atual + 1])); ?>">
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
