<?php
// Desabilitar exibição de erros
error_reporting(0);
ini_set('display_errors', 0);

require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    // Validar método
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido');
    }

    // Validar dados recebidos
    if (empty($_POST['processo_id']) || empty($_POST['contrato_id']) || empty($_POST['valor_contrato'])) {
        throw new Exception('Todos os campos obrigatórios devem ser preenchidos.');
    }

    // Obter e formatar os dados
    $processo_id = intval($_POST['processo_id']);
    $contrato_id = intval($_POST['contrato_id']);
    $valor_contrato = str_replace(['R$', '.', ','], ['', '', '.'], $_POST['valor_contrato']);

    // Validar valor
    if (!is_numeric($valor_contrato) || floatval($valor_contrato) <= 0) {
        throw new Exception('Valor do contrato inválido');
    }

    // Verificar se o processo existe
    $stmt = $pdo->prepare("
        SELECT p.id, p.numero_processo, p.valor_ajuizado
        FROM cbp_processos_judiciais p 
        WHERE p.id = ?
    ");
    $stmt->execute([$processo_id]);
    $processo = $stmt->fetch(PDO::FETCH_ASSOC);
    if (!$processo) {
        throw new Exception('Processo não encontrado');
    }

    // Verificar se o contrato existe
    $stmt = $pdo->prepare("
        SELECT c.id, c.numero_contrato
        FROM cbp_contratos c 
        WHERE c.id = ?
    ");
    $stmt->execute([$contrato_id]);
    $contrato = $stmt->fetch(PDO::FETCH_ASSOC);
    if (!$contrato) {
        throw new Exception('Contrato não encontrado');
    }

    // Verificar se o contrato já está vinculado ao processo
    $stmt = $pdo->prepare("SELECT id FROM cbp_processos_contratos WHERE processo_id = ? AND contrato_id = ?");
    $stmt->execute([$processo_id, $contrato_id]);
    if ($stmt->fetch()) {
        throw new Exception('Este contrato já está vinculado a este processo');
    }

    // Verificar o valor total dos contratos já vinculados
    $stmt = $pdo->prepare("
        SELECT COALESCE(SUM(valor_contrato), 0) as total_vinculado
        FROM cbp_processos_contratos 
        WHERE processo_id = ?
    ");
    $stmt->execute([$processo_id]);
    $total_vinculado = floatval($stmt->fetchColumn());

    // Calcular o valor disponível
    $valor_disponivel = $processo['valor_ajuizado'] - $total_vinculado;
    $TOLERANCIA = 0.01; // Tolerância de 1 centavo

    // Verificar se o valor do contrato excede o disponível (com tolerância)
    if (($valor_contrato - $valor_disponivel) > $TOLERANCIA) {
        throw new Exception(sprintf(
            'O valor do contrato (R$ %.2f) excede o valor disponível (R$ %.2f)',
            $valor_contrato,
            $valor_disponivel
        ));
    }

    // Iniciar transação
    $pdo->beginTransaction();

    // Inserir o vínculo entre processo e contrato
    $stmt = $pdo->prepare("
        INSERT INTO cbp_processos_contratos (
            processo_id,
            contrato_id,
            valor_contrato,
            created_at,
            updated_at
        ) VALUES (
            ?,
            ?,
            ?,
            NOW(),
            NOW()
        )
    ");

    $stmt->execute([
        $processo_id,
        $contrato_id,
        $valor_contrato
    ]);

    // Registrar no log
    $detalhes = "Vinculação de contrato - Processo ID: " . $processo_id . 
                " - Nº Processo: " . ($processo['numero_processo'] ?? 'N/A') . 
                " - Contrato ID: " . $contrato_id . 
                " - Nº Contrato: " . ($contrato['numero_contrato'] ?? 'N/A') . 
                " - Valor: R$ " . number_format($valor_contrato, 2, ',', '.');
    $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$_SESSION['user_id'], 'Vinculação de Contrato', $detalhes]);

    // Commit da transação
    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Contrato vinculado com sucesso!'
    ]);

} catch (Exception $e) {
    // Rollback em caso de erro
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    // Rollback em caso de erro
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao vincular contrato: ' . $e->getMessage()
    ]);
    error_log($e->getMessage());
} 