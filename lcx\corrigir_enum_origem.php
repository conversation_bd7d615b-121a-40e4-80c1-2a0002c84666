<?php
session_start();
require_once '../config/database.php';

// Verificar se o usuário está logado e é admin
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se é admin
try {
    $stmt = $pdo->prepare("SELECT nivel_permissao FROM lcx_permissoes WHERE usuario_id = ? AND ativo = 1");
    $stmt->execute([$_SESSION['user_id']]);
    $permissao = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$permissao || $permissao['nivel_permissao'] !== 'admin') {
        echo "Acesso negado. Apenas administradores podem executar esta correção.";
        exit;
    }
} catch (Exception $e) {
    echo "Erro ao verificar permissões: " . $e->getMessage();
    exit;
}

echo "<h2>Correção do ENUM do Campo 'origem' na Tabela lcx_movimentacoes</h2>";

try {
    // Verificar estrutura atual
    echo "<h3>1. Verificando estrutura atual:</h3>";
    $stmt = $pdo->query("SHOW COLUMNS FROM lcx_movimentacoes LIKE 'origem'");
    $coluna_atual = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($coluna_atual) {
        echo "<p><strong>Tipo atual:</strong> {$coluna_atual['Type']}</p>";
        echo "<p><strong>Null:</strong> {$coluna_atual['Null']}</p>";
        echo "<p><strong>Default:</strong> {$coluna_atual['Default']}</p>";
    } else {
        echo "<p style='color: red;'>Coluna 'origem' não encontrada!</p>";
        exit;
    }
    
    // Verificar valores existentes
    echo "<h3>2. Verificando valores existentes:</h3>";
    $stmt = $pdo->query("SELECT origem, COUNT(*) as quantidade FROM lcx_movimentacoes GROUP BY origem");
    $valores_existentes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Valor Origem</th><th>Quantidade</th></tr>";
    foreach ($valores_existentes as $valor) {
        echo "<tr>";
        echo "<td>" . ($valor['origem'] ?: 'NULL/VAZIO') . "</td>";
        echo "<td>{$valor['quantidade']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Executar correção
    echo "<h3>3. Executando correção do ENUM:</h3>";
    
    $sql_alter = "ALTER TABLE lcx_movimentacoes 
                  MODIFY COLUMN origem ENUM('caixa', 'atm', 'transportadora', 'saldo_tesouraria') 
                  NOT NULL DEFAULT 'caixa'";
    
    echo "<p><strong>SQL a ser executado:</strong></p>";
    echo "<code>{$sql_alter}</code>";
    
    if (isset($_GET['executar']) && $_GET['executar'] === 'sim') {
        $pdo->exec($sql_alter);
        echo "<p style='color: green;'><strong>✅ Correção executada com sucesso!</strong></p>";
        
        // Verificar estrutura após correção
        echo "<h3>4. Verificando estrutura após correção:</h3>";
        $stmt = $pdo->query("SHOW COLUMNS FROM lcx_movimentacoes LIKE 'origem'");
        $coluna_corrigida = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($coluna_corrigida) {
            echo "<p><strong>Novo tipo:</strong> {$coluna_corrigida['Type']}</p>";
            echo "<p><strong>Null:</strong> {$coluna_corrigida['Null']}</p>";
            echo "<p><strong>Default:</strong> {$coluna_corrigida['Default']}</p>";
        }
        
        echo "<h3>5. Teste de inserção:</h3>";
        echo "<p>Testando se todos os valores agora são aceitos:</p>";
        
        $valores_teste = ['caixa', 'atm', 'transportadora', 'saldo_tesouraria'];
        foreach ($valores_teste as $valor) {
            try {
                // Teste simples de validação (não insere dados reais)
                $stmt = $pdo->prepare("SELECT ? as teste_valor");
                $stmt->execute([$valor]);
                echo "<p style='color: green;'>✅ Valor '{$valor}' - OK</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Valor '{$valor}' - Erro: {$e->getMessage()}</p>";
            }
        }
        
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h4 style='color: #155724; margin-top: 0;'>🎉 Correção Concluída!</h4>";
        echo "<p style='color: #155724; margin-bottom: 0;'>O campo 'origem' agora aceita todos os valores necessários:</p>";
        echo "<ul style='color: #155724;'>";
        echo "<li><strong>caixa</strong> - Movimentações de caixa</li>";
        echo "<li><strong>atm</strong> - Movimentações de ATM/ATMR</li>";
        echo "<li><strong>transportadora</strong> - Movimentações de transportadora</li>";
        echo "<li><strong>saldo_tesouraria</strong> - Movimentações de saldo tesouraria (apenas livros master)</li>";
        echo "</ul>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h4 style='color: #856404; margin-top: 0;'>⚠️ Confirmação Necessária</h4>";
        echo "<p style='color: #856404;'>Esta operação irá modificar a estrutura da tabela. Certifique-se de que:</p>";
        echo "<ul style='color: #856404;'>";
        echo "<li>Você tem backup do banco de dados</li>";
        echo "<li>Não há operações críticas em andamento</li>";
        echo "<li>Todos os valores existentes são compatíveis</li>";
        echo "</ul>";
        echo "<p style='color: #856404; margin-bottom: 0;'>";
        echo "<a href='?executar=sim' class='btn btn-warning' style='background: #ffc107; border: 1px solid #ffc107; color: #000; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Executar Correção</a>";
        echo "</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Erro:</strong> " . $e->getMessage() . "</p>";
    echo "<p>Detalhes do erro:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px 12px; text-align: left; }
th { background: #f8f9fa; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
.btn { display: inline-block; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
.btn-warning { background: #ffc107; color: #000; border: 1px solid #ffc107; }
</style>
