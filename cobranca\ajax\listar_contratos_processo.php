<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    // Validar se recebeu os dados via GET
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Método não permitido');
    }

    // Validar processo_id
    if (!isset($_GET['processo_id']) || !is_numeric($_GET['processo_id'])) {
        throw new Exception('ID do processo não informado ou inválido');
    }

    $processo_id = intval($_GET['processo_id']);

    // Buscar contratos vinculados ao processo
    $query = "
        SELECT 
            c.*,
            m.nome as modalidade_nome,
            pc.created_at as data_vinculo,
            CASE 
                WHEN LENGTH(c.associado_documento) = 11 THEN 
                    CONCAT(
                        SUBSTRING(c.associado_documento, 1, 3), '.',
                        SUBSTRING(c.associado_documento, 4, 3), '.',
                        SUBSTRING(c.associado_documento, 7, 3), '-',
                        SUBSTRING(c.associado_documento, 10, 2)
                    )
                ELSE 
                    CONCAT(
                        SUBSTRING(c.associado_documento, 1, 2), '.',
                        SUBSTRING(c.associado_documento, 3, 3), '.',
                        SUBSTRING(c.associado_documento, 6, 3), '/',
                        SUBSTRING(c.associado_documento, 9, 4), '-',
                        SUBSTRING(c.associado_documento, 13, 2)
                    )
            END as associado_documento_formatado
        FROM cbp_contratos c
        INNER JOIN cbp_modalidades_processo m ON m.id = c.modalidade_id
        INNER JOIN cbp_processos_contratos pc ON pc.contrato_id = c.id
        WHERE pc.processo_id = ?
        ORDER BY c.associado_nome ASC
    ";

    $stmt = $pdo->prepare($query);
    $stmt->execute([$processo_id]);
    $contratos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Formatar data de vínculo
    foreach ($contratos as &$contrato) {
        $contrato['data_vinculo'] = date('d/m/Y H:i:s', strtotime($contrato['data_vinculo']));
    }

    echo json_encode([
        'success' => true,
        'data' => $contratos
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 