<?php
require_once '../auth_check.php';
require_once '../config/database.php';

// Verificar se o usuário é administrador ou forte gestor
$stmt_nivel = $pdo->prepare("SELECT nivel_acesso_id FROM usuarios WHERE id = ?");
$stmt_nivel->execute([$_SESSION['user_id']]);
$nivel_usuario = $stmt_nivel->fetchColumn();

// Verificar se é admin (1) ou forte gestor (2)
$is_admin = ($nivel_usuario == 1 || $nivel_usuario == 2);

// Redirecionar se não for admin ou forte gestor
if (!$is_admin) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Acesso não autorizado']);
    exit;
}

// Verificar se os dados necessários foram enviados
if (!isset($_POST['valor']) || !isset($_POST['chave'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Dados incompletos']);
    exit;
}

$valor = intval($_POST['valor']);
$chave = $_POST['chave'];

// Validar o valor mínimo
if ($valor < 1) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'O valor mínimo deve ser maior que zero']);
    exit;
}

try {
    // Atualizar a configuração
    $stmt = $pdo->prepare("UPDATE configuracoes SET valor = :valor WHERE chave = :chave");
    $stmt->execute([
        ':valor' => $valor,
        ':chave' => $chave
    ]);

    header('Content-Type: application/json');
    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Erro ao atualizar configuração']);
} 