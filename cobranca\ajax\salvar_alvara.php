<?php
// Desabilitar exibição de erros para debug
error_reporting(0);
ini_set('display_errors', 0);

require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    // Log para debug
    error_log('Iniciando salvar_alvara.php');
    error_log('POST data: ' . print_r($_POST, true));

    // Verificar se é uma requisição POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido');
    }

    // Validar campos obrigatórios
    $campos_obrigatorios = ['processo_id', 'data_recebimento', 'valor'];
    foreach ($campos_obrigatorios as $campo) {
        if (empty($_POST[$campo])) {
            throw new Exception("O campo {$campo} é obrigatório");
        }
    }

    // Formatar valor monetário
    $valor = str_replace(['R$', '.', ','], ['', '', '.'], $_POST['valor']);
    $valor = floatval($valor);

    // Verificar se o processo existe
    $stmt = $pdo->prepare("SELECT id FROM cbp_processos_judiciais WHERE id = ?");
    $stmt->execute([$_POST['processo_id']]);
    if (!$stmt->fetch()) {
        throw new Exception('Processo não encontrado');
    }

    // Iniciar transação
    $pdo->beginTransaction();

    // Buscar informações do processo
    $stmt = $pdo->prepare("SELECT numero_processo FROM cbp_processos_judiciais WHERE id = ?");
    $stmt->execute([$_POST['processo_id']]);
    $processo = $stmt->fetch();

    if (!$processo) {
        throw new Exception('Processo não encontrado');
    }

        // Inserir novo alvará
    $stmt = $pdo->prepare("
        INSERT INTO cbp_alvaras 
        (processo_id, valor, data_recebimento, observacoes, created_at, updated_at)
        VALUES (?, ?, ?, ?, NOW(), NOW())
    ");
    
    $stmt->execute([
        $_POST['processo_id'],
        $valor,
        $_POST['data_recebimento'],
        $_POST['observacoes'] ?? null
    ]);

    $alvara_id = $pdo->lastInsertId();

    // Registrar no log
    $detalhes = "Criação de alvará - ID: " . $alvara_id . 
                " - Processo: " . $processo['numero_processo'] . 
                " - Data: " . date('d/m/Y', strtotime($_POST['data_recebimento'])) . 
                " - Valor: R$ " . number_format($valor, 2, ',', '.');
    $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$_SESSION['user_id'], 'Criação de Alvará', $detalhes]);
    
    // Commit da transação
    $pdo->commit();

    // Log de sucesso
    error_log('Alvará salvo com sucesso');
    
    // Registrar honorário usando debug_alvaras.php
    try {
        error_log("Executando arquivo debug_alvaras.php para registrar honorário");
        $debug_alvaras_path = dirname(dirname(__FILE__)) . '/debug_alvaras.php';
        error_log("Caminho para debug_alvaras.php: " . $debug_alvaras_path);
        
        if (file_exists($debug_alvaras_path)) {
            include_once($debug_alvaras_path);
            error_log("Arquivo debug_alvaras.php executado com sucesso");
        } else {
            error_log("ERRO: Arquivo debug_alvaras.php não encontrado em: " . $debug_alvaras_path);
        }
    } catch (Exception $e) {
        error_log("Erro ao executar debug_alvaras.php: " . $e->getMessage());
        // Não interromper o fluxo principal
    }

    echo json_encode([
        'success' => true,
        'message' => 'Alvará cadastrado com sucesso'
    ]);

} catch (Exception $e) {
    // Rollback em caso de erro
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    error_log('Erro ao salvar alvará: ' . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao salvar alvará: ' . $e->getMessage()
    ]);
} catch (PDOException $e) {
    // Rollback em caso de erro
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    error_log('Erro PDO ao salvar alvará: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao salvar alvará no banco de dados.'
    ]);
} 