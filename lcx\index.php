<?php
session_start();
require_once '../config/database.php';
require_once 'functions/logs.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user_id = $_SESSION['user_id'];

// Buscar dados do usuário logado
try {
    $stmt = $pdo->prepare("SELECT nome_completo, email, username FROM usuarios WHERE id = ?");
    $stmt->execute([$user_id]);
    $usuario = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$usuario) {
        header('Location: ../login.php');
        exit;
    }
} catch (Exception $e) {
    error_log("Erro ao buscar dados do usuário: " . $e->getMessage());
    header('Location: ../login.php');
    exit;
}

// Verificar permissões LCX do usuário
try {
    $stmt = $pdo->prepare("SELECT nivel_permissao FROM lcx_permissoes WHERE usuario_id = ? AND ativo = 1");
    $stmt->execute([$user_id]);
    $permissao_lcx = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$permissao_lcx) {
        header('Location: ../dashboard.php?erro=sem_permissao_lcx');
        exit;
    }

    $nivel_permissao = $permissao_lcx['nivel_permissao'];

    // Registrar acesso ao sistema LCX
    log_acesso_lcx($pdo, 'index.php');

} catch (Exception $e) {
    error_log("Erro ao verificar permissões LCX: " . $e->getMessage());
    $nivel_permissao = null;
}

// Buscar pontos de atendimento para o select
try {
    $stmt = $pdo->query("SELECT id, nome, numero FROM pontos_atendimento WHERE ativo = 1 ORDER BY nome");
    $pontos_atendimento = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    error_log("Erro ao buscar pontos de atendimento: " . $e->getMessage());
    $pontos_atendimento = [];
}

// Determinar filtro de status
$filtro_status = $_GET['status'] ?? 'abertos';
$condicao_status = '';

switch ($filtro_status) {
    case 'fechados':
        $condicao_status = "lc.status = 'fechado'";
        break;
    case 'todos':
        $condicao_status = "1=1"; // Sem filtro de status
        break;
    case 'abertos':
    default:
        $condicao_status = "lc.status = 'aberto'";
        break;
}

// Buscar livros caixa do usuário (baseado em suas permissões)
try {
    if ($nivel_permissao === 'admin') {
        // Admin vê todos os livros conforme filtro selecionado
        $sql = "
            SELECT lc.*, pa.nome as pa_nome, pa.numero as pa_numero,
                   (SELECT COUNT(*) FROM lcx_movimentacoes WHERE livro_caixa_id = lc.id) as total_movimentacoes,
                   u.nome_completo as habilitado_por_nome
            FROM lcx_livros_caixa lc
            JOIN pontos_atendimento pa ON lc.ponto_atendimento_id = pa.id
            LEFT JOIN usuarios u ON lc.habilitado_por = u.id
            WHERE {$condicao_status}
            ORDER BY
                CASE WHEN lc.status = 'aberto' THEN 0 ELSE 1 END,
                lc.created_at DESC
        ";
        $stmt = $pdo->query($sql);
    } else {
        // Outros usuários veem apenas livros dos PAs onde são tesoureiros
        $sql = "
            SELECT lc.*, pa.nome as pa_nome, pa.numero as pa_numero,
                   (SELECT COUNT(*) FROM lcx_movimentacoes WHERE livro_caixa_id = lc.id) as total_movimentacoes,
                   u.nome_completo as habilitado_por_nome
            FROM lcx_livros_caixa lc
            JOIN pontos_atendimento pa ON lc.ponto_atendimento_id = pa.id
            JOIN lcx_tesoureiros_pa tp ON pa.id = tp.ponto_atendimento_id
            LEFT JOIN usuarios u ON lc.habilitado_por = u.id
            WHERE tp.usuario_id = ? AND tp.ativo = 1 AND {$condicao_status}
            ORDER BY
                CASE WHEN lc.status = 'aberto' THEN 0 ELSE 1 END,
                lc.created_at DESC
        ";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$user_id]);
    }

    $livros = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $total_livros = count($livros);

} catch (Exception $e) {
    error_log("Erro ao buscar livros caixa: " . $e->getMessage());
    $livros = [];
    $total_livros = 0;
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
        Livro Caixa
    </title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            /* Paleta de Cores Oficial Sicoob 2024 - Manual de Marca */
            --sicoob-turquesa: #00A091;        /* RGB: 0, 160, 145 - Cor principal */
            --sicoob-verde-escuro: #003641;    /* RGB: 0, 54, 65 - Verde escuro */
            --sicoob-verde-medio: #7DB61C;     /* RGB: 125, 182, 28 - Verde médio */
            --sicoob-verde-claro: #C9D200;     /* RGB: 201, 210, 0 - Verde claro */
            --sicoob-roxo: #49479D;            /* RGB: 73, 71, 157 - Roxo */
            --sicoob-branco: #FFFFFF;          /* RGB: 255, 255, 255 - Branco */

            /* Cores de Sistema baseadas na identidade oficial */
            --primary-color: var(--sicoob-turquesa);
            --secondary-color: var(--sicoob-verde-escuro);
            --accent-color: var(--sicoob-verde-medio);
            --accent-light: var(--sicoob-verde-claro);
            --accent-purple: var(--sicoob-roxo);
            --success-color: var(--sicoob-verde-medio);
            --warning-color: var(--sicoob-verde-claro);
            --danger-color: #D32F2F;
            --info-color: var(--sicoob-turquesa);
            --dark-color: var(--sicoob-verde-escuro);
            --light-color: #F8FFFE;
            --white-color: var(--sicoob-branco);
            --gray-color: #6B7280;
        }

        /* Layout e Estrutura - Identidade Sicoob Oficial */
        body {
            background: linear-gradient(135deg, var(--light-color) 0%, rgba(0, 160, 145, 0.03) 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            position: relative;
        }

        /* Elemento gráfico sutil de fundo inspirado nos padrões oficiais Sicoob */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            right: 0;
            width: 300px;
            height: 100vh;
            background: linear-gradient(60deg,
                transparent 0%,
                rgba(0, 160, 145, 0.02) 20%,
                rgba(125, 182, 28, 0.015) 40%,
                rgba(201, 210, 0, 0.01) 60%,
                rgba(73, 71, 157, 0.005) 80%,
                transparent 100%);
            z-index: -1;
            opacity: 0.7;
        }

        /* Navbar - Identidade Visual Oficial Sicoob */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 4px 20px rgba(0, 54, 65, 0.2);
            border-bottom: 3px solid var(--accent-color);
            position: relative;
        }

        /* Padrão gráfico inspirado nos grafismos oficiais Sicoob */
        .navbar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 100%;
            background: linear-gradient(60deg,
                transparent 30%,
                rgba(125, 182, 28, 0.12) 45%,
                rgba(201, 210, 0, 0.08) 55%,
                rgba(73, 71, 157, 0.05) 70%,
                transparent 85%);
            opacity: 0.8;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .navbar-brand .accent-text {
            color: var(--accent-color);
            font-weight: 300;
        }

        .user-info {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 8px 16px;
        }



        /* Botão Criar - Padrão Oficial Sicoob 2024 */
        .btn-create {
            background: linear-gradient(135deg, var(--sicoob-turquesa) 0%, var(--sicoob-verde-escuro) 100%);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            color: white;
            position: relative;
            overflow: hidden;
        }

        /* Efeito de hover inspirado nos padrões gráficos oficiais Sicoob */
        .btn-create::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.25), transparent);
            transition: left 0.6s ease;
        }

        .btn-create:hover::before {
            left: 100%;
        }

        .btn-create:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 160, 145, 0.4);
            background: linear-gradient(135deg, var(--sicoob-verde-escuro) 0%, var(--sicoob-turquesa) 100%);
            color: white;
        }

        /* Cards dos Livros - Padrão Oficial Sicoob */
        .livro-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 3px 12px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: none;
            overflow: hidden;
            height: 100%;
            margin-bottom: 15px;
            position: relative;
        }

        /* Elemento gráfico inspirado nos padrões oficiais Sicoob */
        .livro-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg,
                var(--sicoob-turquesa) 0%,
                var(--sicoob-verde-medio) 33%,
                var(--sicoob-verde-claro) 66%,
                var(--sicoob-roxo) 100%);
        }

        .livro-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .livro-header {
            height: 70px;
            border-radius: 12px 12px 0 0;
            padding: 15px 18px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: white;
            position: relative;
            overflow: hidden;
        }

        /* Grafismo sutil no header dos cards - Padrão Sicoob */
        .livro-header::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100%;
            background: linear-gradient(60deg,
                transparent 20%,
                rgba(255,255,255,0.12) 40%,
                rgba(255,255,255,0.08) 60%,
                transparent 80%);
        }

        /* Gradientes dos Cards - Paleta Oficial Sicoob 2024 */
        .livro-header.gradient-1 {
            background: linear-gradient(135deg, var(--sicoob-turquesa) 0%, var(--sicoob-verde-escuro) 100%);
        }

        .livro-header.gradient-2 {
            background: linear-gradient(135deg, var(--sicoob-verde-medio) 0%, var(--sicoob-verde-claro) 100%);
        }

        .livro-header.gradient-3 {
            background: linear-gradient(135deg, var(--sicoob-verde-escuro) 0%, var(--sicoob-turquesa) 100%);
        }

        .livro-header.gradient-4 {
            background: linear-gradient(135deg, var(--sicoob-roxo) 0%, var(--sicoob-turquesa) 100%);
        }

        .livro-title {
            font-size: 1.1rem;
            font-weight: 700;
            margin: 0;
            line-height: 1.3;
        }

        .livro-stats {
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            padding: 10px 18px;
            border-radius: 0 0 10px 10px;
        }

        .stat-item {
            text-align: center;
            flex: 1;
        }

        .stat-value {
            font-size: 0.9rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 2px;
        }

        .stat-label {
            font-size: 0.7rem;
            color: var(--gray-color);
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        /* Empty State */
        .empty-state {
            padding: 80px 20px;
            text-align: center;
        }

        .empty-state i {
            font-size: 5rem;
            opacity: 0.6;
            color: var(--gray-color);
        }

        .empty-state h3 {
            color: var(--dark-color);
            font-weight: 600;
            margin: 20px 0 10px;
        }

        .empty-state p {
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
            color: var(--gray-color);
        }

        /* Coming Soon Badge */
        .coming-soon-badge {
            background: linear-gradient(135deg, var(--accent-color), #E6A500);
            color: var(--dark-color);
            padding: 8px 20px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        /* Badges */
        .badge-sm {
            font-size: 0.7rem;
            padding: 0.25em 0.5em;
        }

        .badge-master {
            background: linear-gradient(135deg, var(--sicoob-verde-claro) 0%, var(--sicoob-verde-medio) 100%);
            color: var(--sicoob-verde-escuro);
            font-weight: 600;
        }

        .badge-status-ativo {
            background-color: var(--sicoob-verde-medio);
            color: white;
        }

        .badge-status-fechado {
            background-color: var(--gray-color);
            color: white;
        }

        /* Dropdown customizado */
        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-radius: 10px;
        }

        .dropdown-item:hover {
            background-color: var(--primary-color);
            color: white;
        }

        /* Filtros de Visualização - Identidade Sicoob */
        .btn-outline-primary {
            color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
        }

        .btn-outline-primary:hover,
        .btn-outline-primary:focus,
        .btn-outline-primary.active,
        .btn-check:checked + .btn-outline-primary {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
            color: white;
        }

        .btn-outline-secondary {
            color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
        }

        .btn-outline-secondary:hover,
        .btn-outline-secondary:focus,
        .btn-outline-secondary.active,
        .btn-check:checked + .btn-outline-secondary {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
            color: white;
        }

        .btn-outline-info {
            color: var(--sicoob-verde-medio);
            border-color: var(--sicoob-verde-medio);
        }

        .btn-outline-info:hover,
        .btn-outline-info:focus,
        .btn-outline-info.active,
        .btn-check:checked + .btn-outline-info {
            background-color: var(--sicoob-verde-medio);
            border-color: var(--sicoob-verde-medio);
            color: white;
        }

        .btn-outline-success {
            color: var(--sicoob-verde-medio);
            border-color: var(--sicoob-verde-medio);
        }

        .btn-outline-success:hover,
        .btn-outline-success:focus,
        .btn-outline-success.active {
            background-color: var(--sicoob-verde-medio);
            border-color: var(--sicoob-verde-medio);
            color: white;
        }

        .btn-outline-danger {
            color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .btn-outline-danger:hover,
        .btn-outline-danger:focus,
        .btn-outline-danger.active {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            color: white;
        }

        /* Card de filtros */
        .text-primary {
            color: var(--sicoob-turquesa) !important;
        }

        /* Botões de filtro específicos */
        .btn-filtro {
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-filtro::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-filtro:hover::before,
        .btn-check:checked + .btn-filtro::before {
            left: 100%;
        }

        .btn-filtro:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* Card de filtros com identidade Sicoob */
        .card.border-0.shadow-sm {
            border: 1px solid rgba(0, 160, 145, 0.1) !important;
            background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,255,254,0.98) 100%);
        }

        .card.border-0.shadow-sm::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg,
                var(--sicoob-turquesa) 0%,
                var(--sicoob-verde-medio) 50%,
                var(--sicoob-verde-claro) 100%);
            border-radius: 8px 8px 0 0;
        }

        /* Estilo para campos readonly */
        .form-control[readonly] {
            background-color: #f8f9fa !important;
            border-color: #dee2e6 !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
        }

        .form-control[readonly]:focus {
            background-color: #f8f9fa !important;
            border-color: #dee2e6 !important;
            box-shadow: none !important;
        }

        .form-text {
            font-size: 0.875rem;
            color: #6c757d;
        }

        /* Estilo específico para campo nome com numeração automática */
        #nomeAutomatico[readonly] {
            background-color: #e3f2fd !important;
            border-color: #2196f3 !important;
            color: #1565c0 !important;
        }

        /* Estilo para campo de número do livro */
        #numeroLivro {
            font-weight: 600;
            text-align: center;
        }

        .input-group-text {
            background-color: #f8f9fa;
            border-color: #dee2e6;
            font-weight: 500;
        }

        #infoNumeracao {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px 12px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-book me-2" style="color: var(--accent-color);"></i>
                <span>Sicoob</span>
                <span class="accent-text"> Livro Caixa</span>
            </a>

            <div class="navbar-nav ms-auto d-flex align-items-center">
                <?php if (in_array($nivel_permissao, ['gestor', 'gestor_master', 'admin'])): ?>
                    <button class="btn btn-create me-2" data-bs-toggle="modal" data-bs-target="#modalNovoLivro">
                        <i class="fas fa-plus me-2"></i>Novo Livro Caixa
                    </button>
                <?php endif; ?>
                <!-- Menu Administrativo - Apenas para Admin -->
                <?php if ($nivel_permissao === 'admin'): ?>
                <div class="dropdown me-3">
                    <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" id="menuAdmin" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-cog me-2"></i>Administração
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="menuAdmin">
                        <li>
                            <a class="dropdown-item" href="gerenciar_usuarios.php">
                                <i class="fas fa-users-cog me-2"></i>Gerenciar Usuários LCX
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="gerenciar_tesoureiros_pa.php">
                                <i class="fas fa-building-user me-2"></i>Tesoureiros por PA
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="gerenciar_atm_pa.php">
                                <i class="fas fa-credit-card me-2"></i>ATM/ATMR por PA
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <a class="dropdown-item" href="logs.php">
                                <i class="fas fa-list-alt me-2"></i>Logs do Sistema
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item" href="configuracoes.php">
                                <i class="fas fa-wrench me-2"></i>Configurações
                            </a>
                        </li>
                    </ul>
                </div>
                <?php endif; ?>

                <!-- Informações do Usuário -->
                <div class="user-info">
                    <i class="fas fa-user-circle me-2"></i>
                    <span><?php echo explode(' ', $usuario['nome_completo'])[0]; ?></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Mensagens de feedback -->
        <?php if (isset($_GET['sucesso'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php
                switch ($_GET['sucesso']) {
                    case 'livro_criado':
                        echo 'Livro caixa criado com sucesso!';
                        break;
                    case 'livro_criado_saldo_automatico':
                        echo 'Livro caixa criado com sucesso! O saldo inicial foi obtido automaticamente do livro anterior.';
                        break;
                    case 'livro_fechado':
                        $nome_livro = $_GET['nome'] ?? 'o livro';
                        echo 'Livro caixa "' . htmlspecialchars($nome_livro) . '" foi fechado com sucesso!';
                        break;
                    case 'edicao_geral_habilitada':
                        echo 'Edição retroativa geral habilitada com sucesso! Agora é possível editar movimentações de qualquer data.';
                        break;
                    case 'edicao_especifica_habilitada':
                        $data = $_GET['data'] ?? '';
                        if ($data) {
                            $data_formatada = date('d/m/Y', strtotime($data));
                            echo 'Edição retroativa habilitada para o dia ' . $data_formatada . '! Apenas movimentações desta data poderão ser editadas.';
                        } else {
                            echo 'Edição retroativa específica habilitada com sucesso!';
                        }
                        break;
                    case 'edicao_data_habilitada':
                        echo 'Edição retroativa habilitada com sucesso!';
                        break;
                    case 'edicao_data_desabilitada':
                        echo 'Edição retroativa desabilitada com sucesso!';
                        break;
                    default:
                        echo 'Operação realizada com sucesso!';
                }
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['erro'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php
                switch ($_GET['erro']) {
                    case 'sem_permissao_lcx':
                        echo 'Você não tem permissão para acessar o sistema LCX.';
                        break;
                    case 'sem_permissao_criar':
                        echo 'Você não tem permissão para criar livros caixa.';
                        break;
                    case 'dados_obrigatorios':
                        echo 'Todos os campos obrigatórios devem ser preenchidos.';
                        break;
                    case 'pa_nao_encontrado':
                        echo 'Ponto de atendimento não encontrado.';
                        break;
                    case 'livro_ja_aberto':
                        $pa_nome = $_GET['pa'] ?? 'este ponto de atendimento';
                        echo "Já existe um livro caixa aberto para {$pa_nome}. Feche o livro atual antes de criar um novo.";
                        break;
                    case 'data_invalida':
                        echo 'Data de abertura inválida.';
                        break;
                    case 'tipo_invalido':
                        echo 'Tipo de livro inválido.';
                        break;
                    case 'master_apenas_88_uad':
                        $pa_nome = $_GET['pa'] ?? 'o ponto de atendimento selecionado';
                        echo "Livros do tipo Master só podem ser criados para o Ponto de Atendimento \"88 Uad\". O PA \"{$pa_nome}\" não permite este tipo de livro.";
                        break;
                    case 'erro_criacao':
                        echo 'Erro ao criar livro caixa. Tente novamente.';
                        break;
                    case 'erro_sistema':
                        echo 'Erro interno do sistema. Contate o administrador.';
                        break;
                    case 'sem_permissao_fechar_livro':
                        echo 'Você não tem permissão para fechar livros caixa. Apenas administradores podem realizar esta ação.';
                        break;
                    case 'livro_ja_fechado':
                        echo 'Este livro caixa já está fechado.';
                        break;
                    case 'erro_fechar_livro':
                        echo 'Erro ao fechar livro caixa. Tente novamente.';
                        break;
                    case 'parametros_invalidos':
                        echo 'Parâmetros inválidos fornecidos.';
                        break;
                    case 'metodo_invalido':
                        echo 'Método de requisição inválido.';
                        break;
                    default:
                        echo 'Ocorreu um erro inesperado.';
                }
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Filtros de Visualização -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-body py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <h6 class="mb-0 me-3">
                                    <i class="fas fa-filter me-2 text-primary"></i>Visualizar:
                                </h6>
                                <div class="btn-group" role="group" aria-label="Filtros de livros">
                                    <input type="radio" class="btn-check" name="filtro_status" id="filtro_abertos" value="abertos"
                                           <?php echo (!isset($_GET['status']) || $_GET['status'] === 'abertos') ? 'checked' : ''; ?>>
                                    <label class="btn btn-outline-primary btn-filtro" for="filtro_abertos">
                                        <i class="fas fa-book-open me-1"></i>Livros Abertos
                                    </label>

                                    <input type="radio" class="btn-check" name="filtro_status" id="filtro_fechados" value="fechados"
                                           <?php echo (isset($_GET['status']) && $_GET['status'] === 'fechados') ? 'checked' : ''; ?>>
                                    <label class="btn btn-outline-secondary btn-filtro" for="filtro_fechados">
                                        <i class="fas fa-book me-1"></i>Livros Fechados
                                    </label>

                                    <input type="radio" class="btn-check" name="filtro_status" id="filtro_todos" value="todos"
                                           <?php echo (isset($_GET['status']) && $_GET['status'] === 'todos') ? 'checked' : ''; ?>>
                                    <label class="btn btn-outline-info btn-filtro" for="filtro_todos">
                                        <i class="fas fa-list me-1"></i>Todos os Livros
                                    </label>
                                </div>
                            </div>
                            <div class="text-muted">
                                <small>
                                    <i class="fas fa-info-circle me-1"></i>
                                    Total: <strong><?php echo count($livros); ?></strong> livro(s)
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Livros Caixa -->
        <?php if (!empty($livros)): ?>
            <div class="row g-4">
                <?php foreach ($livros as $index => $livro): ?>
                    <?php
                    $gradient_class = 'gradient-' . (($index % 4) + 1);
                    $status_texto = $livro['status'] === 'aberto' ? 'Aberto' : 'Fechado';
                    $status_class = $livro['status'] === 'aberto' ? 'badge-status-ativo' : 'badge-status-fechado';
                    $pa_info = $livro['pa_nome'];
                    if ($livro['pa_numero']) {
                        $pa_info .= ' (PA: ' . $livro['pa_numero'] . ')';
                    }
                    ?>
                    <div class="col-lg-4 col-md-6">
                        <div class="card livro-card">
                            <!-- Header do Card -->
                            <div class="livro-header <?php echo $gradient_class; ?> d-flex justify-content-between align-items-center">
                                <div>
                                    <h5 class="livro-title"><?php echo htmlspecialchars($livro['nome']); ?></h5>
                                    <div class="d-flex gap-2">
                                        <span class="badge bg-light text-dark">
                                            <?php echo $status_texto; ?>
                                        </span>
                                        <?php if (isset($livro['tipo']) && $livro['tipo'] === 'master'): ?>
                                            <span class="badge badge-master">
                                                <i class="fas fa-crown me-1"></i>Master
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php if (in_array($nivel_permissao, ['gestor', 'gestor_master', 'admin'])): ?>
                                    <button type="button" class="btn btn-sm <?php echo $livro['permite_edicao_data'] ? 'btn-light' : 'btn-outline-secondary'; ?>"
                                            onclick="abrirModalEdicaoRetroativa(<?php echo $livro['id']; ?>, '<?php echo htmlspecialchars($livro['nome']); ?>', <?php echo $livro['permite_edicao_data'] ? 'true' : 'false'; ?>, '<?php echo $livro['data_edicao_especifica'] ?? ''; ?>')"
                                            title="<?php echo $livro['permite_edicao_data'] ? 'Configurar' : 'Habilitar'; ?> edição retroativa">
                                        <i class="fas fa-edit"></i>
                                        <?php if (!empty($livro['data_edicao_especifica'])): ?>
                                            Edição: <?php echo date('d/m/Y', strtotime($livro['data_edicao_especifica'])); ?>
                                        <?php else: ?>
                                            <?php echo $livro['permite_edicao_data'] ? 'Edição Retroativa ON' : 'Edição Retroativa OFF'; ?>
                                        <?php endif; ?>
                                    </button>
                                <?php endif; ?>
                                <div class="dropdown">
                                    <button class="btn btn-link text-white p-0" type="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="visualizarLivro(<?php echo $livro['id']; ?>)">
                                                <i class="fas fa-eye me-2"></i>Visualizar
                                            </a>
                                        </li>
                                        <?php if ($livro['status'] == 'aberto'): ?>
                                            <li>
                                                <?php if (isset($livro['tipo']) && $livro['tipo'] === 'master'): ?>
                                                    <a class="dropdown-item" href="#" onclick="novaMovimentacaoMaster(<?php echo $livro['id']; ?>)">
                                                        <i class="fas fa-plus me-2"></i>Nova Movimentação Master
                                                    </a>
                                                <?php else: ?>
                                                    <a class="dropdown-item" href="#" onclick="novaMovimentacao(<?php echo $livro['id']; ?>)">
                                                        <i class="fas fa-plus me-2"></i>Nova Movimentação
                                                    </a>
                                                <?php endif; ?>
                                            </li>
                                            <?php if ($nivel_permissao === 'admin'): ?>
                                                <li><hr class="dropdown-divider"></li>
                                                <li>
                                                    <button class="dropdown-item text-warning" onclick="fecharLivro(<?php echo $livro['id']; ?>, '<?php echo htmlspecialchars($livro['nome'], ENT_QUOTES); ?>')">
                                                        <i class="fas fa-lock me-2"></i>Fechar Livro
                                                    </button>
                                                </li>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <!-- Opções para livros fechados -->
                                            <li>
                                                <a class="dropdown-item" href="#" onclick="imprimirLivroCompleto(<?php echo $livro['id']; ?>)">
                                                    <i class="fas fa-print me-2"></i>Imprimir Livro Completo
                                                </a>
                                            </li>
                                            <li>
                                                <a class="dropdown-item" href="#" onclick="verHistoricoLivro(<?php echo $livro['id']; ?>)">
                                                    <i class="fas fa-history me-2"></i>Histórico Completo
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </div>
                            </div>

                            <!-- Body do Card -->
                            <div class="card-body" style="padding: 15px 18px;">
                                <div class="d-flex align-items-center justify-content-between mb-2">
                                    <small class="text-muted">
                                        <i class="fas fa-building me-1"></i>
                                        <?php echo htmlspecialchars($pa_info); ?>
                                    </small>
                                    <span class="badge badge-sm <?php echo $livro['tipo'] === 'master' ? 'bg-warning' : 'bg-info'; ?>">
                                        <?php echo $livro['tipo'] === 'master' ? 'Master' : 'Normal'; ?>
                                    </span>
                                </div>
                                <p class="text-muted mb-3 small" style="font-size: 0.85rem; line-height: 1.4;">
                                    <?php echo htmlspecialchars($livro['observacoes'] ?: 'Sem descrição disponível'); ?>
                                </p>

                                <div class="row mb-3">
                                    <div class="col-6">
                                        <small class="text-muted d-block">Saldo Inicial</small>
                                        <strong class="text-info">R$ <?php echo number_format($livro['saldo_inicial'], 2, ',', '.'); ?></strong>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-muted d-block">Saldo Atual</small>
                                        <strong class="<?php echo $livro['saldo_atual'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                            R$ <?php echo number_format($livro['saldo_atual'], 2, ',', '.'); ?>
                                        </strong>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <?php if ($livro['status'] === 'fechado'): ?>
                                            <i class="fas fa-lock me-1"></i>
                                            Fechado em <?php echo isset($livro['data_fechamento']) ? date('d/m/Y', strtotime($livro['data_fechamento'])) : 'N/A'; ?>
                                        <?php else: ?>
                                            <i class="fas fa-calendar-alt me-1"></i>
                                            Aberto em <?php echo date('d/m/Y', strtotime($livro['data_abertura'])); ?>
                                        <?php endif; ?>
                                    </small>
                                    <div class="d-flex gap-1">
                                        <button class="btn btn-outline-primary btn-sm" onclick="visualizarLivro(<?php echo $livro['id']; ?>)">
                                            <i class="fas fa-eye me-1"></i>Ver
                                        </button>
                                        <?php if ($livro['status'] == 'aberto' && $nivel_permissao === 'admin'): ?>
                                            <button class="btn btn-outline-danger btn-sm"
                                                    onclick="confirmarFecharLivro(<?php echo $livro['id']; ?>, '<?php echo htmlspecialchars($livro['nome'], ENT_QUOTES); ?>')">
                                                <i class="fas fa-lock me-1"></i>Fechar
                                            </button>
                                        <?php elseif ($livro['status'] == 'fechado'): ?>
                                            <button class="btn btn-outline-success btn-sm" onclick="imprimirLivroCompleto(<?php echo $livro['id']; ?>)">
                                                <i class="fas fa-print me-1"></i>Imprimir
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Footer do Card (Stats) -->
                            <div class="livro-stats">
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo $livro['total_movimentacoes']; ?></div>
                                    <div class="stat-label">Movimentações</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value"><?php echo date('d/m', strtotime($livro['data_abertura'])); ?></div>
                                    <div class="stat-label">Abertura</div>
                                </div>
                                <div class="stat-item">
                                    <span class="badge <?php echo $status_class; ?>"><?php echo $status_texto; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php else: ?>
            <!-- Empty State -->
            <div class="empty-state">
                <?php if ($filtro_status === 'fechados'): ?>
                    <i class="fas fa-book"></i>
                    <h3>Nenhum livro fechado encontrado</h3>
                    <p>
                        Não há livros caixa fechados no sistema no momento.
                        Os livros aparecem aqui após serem fechados pelo administrador quando atingem o limite de 98 folhas.
                    </p>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>Dica:</strong> Use o filtro "Livros Abertos" para ver os livros em uso.
                        </small>
                    </div>
                <?php elseif ($filtro_status === 'todos'): ?>
                    <i class="fas fa-list"></i>
                    <h3>Nenhum livro caixa encontrado</h3>
                    <p>
                        Não há livros caixa no sistema ou você não tem acesso a nenhum livro.
                        Entre em contato com o administrador para obter acesso aos livros necessários.
                    </p>
                <?php else: ?>
                    <i class="fas fa-book-open"></i>
                    <h3>Nenhum livro aberto encontrado</h3>
                    <?php if ($nivel_permissao === 'admin'): ?>
                        <p>
                            Não há livros caixa abertos no sistema.
                            Clique no botão "Novo Livro Caixa" para criar um novo livro e começar a gerenciar as movimentações financeiras.
                        </p>
                        <button class="btn btn-create mt-3" data-bs-toggle="modal" data-bs-target="#modalNovoLivro">
                            <i class="fas fa-plus me-2"></i>Criar Novo Livro Caixa
                        </button>
                    <?php else: ?>
                        <p>
                            Você não possui acesso a nenhum livro caixa aberto no momento.
                            Isso pode acontecer se você ainda não foi designado como tesoureiro de nenhum ponto de atendimento,
                            ou se não há livros caixa abertos para os pontos onde você atua.
                            Entre em contato com o administrador para obter acesso aos livros necessários.
                        </p>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                <strong>Dica:</strong> Use o filtro "Livros Fechados" para ver livros já finalizados.
                            </small>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Modal Novo Livro Caixa -->
    <div class="modal fade" id="modalNovoLivro" tabindex="-1" aria-labelledby="modalNovoLivroLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); color: white;">
                    <h5 class="modal-title" id="modalNovoLivroLabel">
                        <i class="fas fa-plus me-2"></i>Criar Novo Livro Caixa
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="formNovoLivro" method="POST" action="processar_livro_caixa.php">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="ponto_atendimento" class="form-label">Ponto de Atendimento</label>
                            <select class="form-select" id="ponto_atendimento" name="ponto_atendimento_id" required onchange="verificarPontoAtendimento()">
                                <option value="">Selecione o ponto de atendimento...</option>
                                <?php foreach ($pontos_atendimento as $pa): ?>
                                    <option value="<?php echo $pa['id']; ?>">
                                        <?php echo htmlspecialchars($pa['nome']); ?>
                                        <?php if ($pa['numero']): ?>
                                            (PA: <?php echo htmlspecialchars($pa['numero']); ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="nome" class="form-label">Nome do Livro Caixa</label>

                            <!-- Campo para nome manual (primeiro livro sem padrão) -->
                            <div id="campoNomeManual">
                                <input type="text" class="form-control" id="nomeManual"
                                       placeholder="Ex: Caixa Principal, Caixa Filial Centro">
                            </div>

                            <!-- Campo para número do primeiro livro (quando há padrão) -->
                            <div id="campoNumeroLivro" style="display: none;">
                                <div class="input-group">
                                    <span class="input-group-text">Livro Caixa</span>
                                    <input type="number" class="form-control" id="numeroLivro" min="1" max="999"
                                           placeholder="Digite o número (ex: 1)" oninput="atualizarNomePrimeiroLivro()">
                                </div>
                            </div>

                            <!-- Campo para nome automático (livros subsequentes) -->
                            <div id="campoNomeAutomatico" style="display: none;">
                                <input type="text" class="form-control" id="nomeAutomatico" readonly>
                            </div>

                            <!-- Campo hidden que será enviado no formulário -->
                            <input type="hidden" id="nomeHidden" name="nome" required>

                            <div class="form-text" id="infoNumeracao" style="display: none;">
                                <i class="fas fa-info-circle me-1"></i>
                                <span id="textoNumeracao"></span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="tipo" class="form-label">Tipo de Livro</label>
                            <select class="form-select" id="tipo" name="tipo" required onchange="toggleTipoLivro()">
                                <option value="normal">Normal - Livro individual</option>
                                <option value="master">Master - Gerenciamento por pontos de atendimento</option>
                            </select>
                            <div class="form-text">
                                <small class="text-muted">
                                    <strong>Normal:</strong> Livro caixa tradicional para um local específico.<br>
                                    <strong>Master:</strong> Permite lançamentos selecionando pontos de atendimento como categorias.
                                </small>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="descricao" class="form-label">Descrição</label>
                            <textarea class="form-control" id="descricao" name="descricao" rows="3"
                                      placeholder="Descreva o propósito deste livro caixa..."></textarea>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="saldo_inicial" class="form-label">Saldo Inicial</label>
                                    <div class="input-group">
                                        <span class="input-group-text">R$</span>
                                        <input type="text" class="form-control" id="saldo_inicial" name="saldo_inicial"
                                               value="0,00" placeholder="0,00" required>
                                        <input type="hidden" id="saldo_inicial_hidden" name="saldo_inicial_value" value="0.00">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="data_abertura" class="form-label">Data de Abertura</label>
                                    <input type="date" class="form-control" id="data_abertura" name="data_abertura"
                                           value="<?php echo date('Y-m-d'); ?>" required readonly>
                                    <div class="form-text">
                                        <i class="fas fa-info-circle me-1"></i>
                                        A data de abertura é automaticamente definida como hoje
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Importante:</strong> Após a criação, você poderá gerenciar permissões de acesso
                            e adicionar movimentações ao livro caixa.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-create">
                            <i class="fas fa-save me-2"></i>Criar Livro Caixa
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Fechar Livro -->
    <div class="modal fade" id="modalFecharLivro" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-lock me-2"></i>Fechar Livro Caixa
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="formFecharLivro" method="POST" action="processar_fechar_livro.php">
                    <div class="modal-body">
                        <input type="hidden" id="livro_id_fechar" name="livro_id">

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Atenção!</strong> Esta ação irá fechar permanentemente o livro caixa
                            "<span id="nome_livro_fechar"></span>".
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>O que acontecerá:</strong>
                            <ul class="mb-0 mt-2">
                                <li>O livro será marcado como fechado</li>
                                <li>Não será possível adicionar novas movimentações</li>
                                <li>Se houver um dia aberto, ele será fechado automaticamente</li>
                                <li>O saldo final será preservado</li>
                            </ul>
                        </div>

                        <div class="mb-3">
                            <label for="observacoes_fechamento" class="form-label">Observações do Fechamento</label>
                            <textarea class="form-control" id="observacoes_fechamento" name="observacoes" rows="3"
                                      placeholder="Motivo do fechamento, observações finais, etc. (opcional)"></textarea>
                        </div>

                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirmar_fechamento" required>
                            <label class="form-check-label" for="confirmar_fechamento">
                                Confirmo que desejo fechar este livro caixa permanentemente
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-lock me-2"></i>Fechar Livro Definitivamente
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Função para controlar exibição baseada no tipo de livro
        function toggleTipoLivro() {
            const tipo = document.getElementById('tipo').value;
            const pontoAtendimento = document.getElementById('ponto_atendimento');
            const alertInfo = document.querySelector('.alert-info');

            if (tipo === 'master') {
                // Verificar se o PA selecionado é o 88 Uad
                const paTexto = pontoAtendimento.options[pontoAtendimento.selectedIndex]?.text || '';
                const isPA88Uad = (paTexto.includes('88') && paTexto.toLowerCase().includes('uad')) ||
                                  (paTexto.includes('(PA: 88)'));

                if (!isPA88Uad && pontoAtendimento.value) {
                    // Se não é PA 88 Uad, mostrar aviso e voltar para normal
                    alertInfo.innerHTML = `
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Restrição:</strong> Livros do tipo Master só podem ser criados para o
                        Ponto de Atendimento "88 Uad". Selecione o PA correto ou escolha o tipo "Normal".
                    `;
                    alertInfo.className = 'alert alert-warning';

                    // Voltar para tipo normal
                    document.getElementById('tipo').value = 'normal';
                    return;
                } else if (isPA88Uad) {
                    alertInfo.innerHTML = `
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Livro Master:</strong> Este livro permitirá fazer lançamentos selecionando
                        pontos de atendimento específicos. Ideal para gestores que precisam controlar
                        movimentações de múltiplas unidades.
                    `;
                    alertInfo.className = 'alert alert-info';
                } else {
                    alertInfo.innerHTML = `
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Livro Master:</strong> Selecione primeiro o Ponto de Atendimento "88 Uad"
                        para criar um livro do tipo Master.
                    `;
                    alertInfo.className = 'alert alert-info';
                }
            } else {
                alertInfo.innerHTML = `
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Importante:</strong> Após a criação, você poderá gerenciar permissões de acesso
                    e adicionar movimentações ao livro caixa.
                `;
                alertInfo.className = 'alert alert-info';
            }
        }

        // Função para verificar PA quando mudado
        function verificarPontoAtendimento() {
            const tipo = document.getElementById('tipo').value;
            if (tipo === 'master') {
                toggleTipoLivro(); // Re-verificar a restrição
            }

            // Verificar numeração automática
            verificarNumeracaoLivro();
        }

        // Função para verificar e aplicar numeração automática
        function verificarNumeracaoLivro() {
            const paSelect = document.getElementById('ponto_atendimento');
            const campoNomeManual = document.getElementById('campoNomeManual');
            const campoNumeroLivro = document.getElementById('campoNumeroLivro');
            const campoNomeAutomatico = document.getElementById('campoNomeAutomatico');
            const infoNumeracao = document.getElementById('infoNumeracao');
            const textoNumeracao = document.getElementById('textoNumeracao');

            // Esconder todos os campos primeiro
            campoNomeManual.style.display = 'none';
            campoNumeroLivro.style.display = 'none';
            campoNomeAutomatico.style.display = 'none';
            infoNumeracao.style.display = 'none';

            if (!paSelect.value) {
                // Se não há PA selecionado, mostrar campo manual
                campoNomeManual.style.display = 'block';
                return;
            }

            // Buscar próximo número para o PA selecionado
            fetch('ajax/verificar_numeracao_livro.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ponto_atendimento_id: paSelect.value
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.primeiro_livro) {
                        // Primeiro livro do PA - pedir número
                        campoNumeroLivro.style.display = 'block';
                        document.getElementById('numeroLivro').value = '';
                        document.getElementById('numeroLivro').required = true;
                        textoNumeracao.textContent = 'Este é o primeiro livro deste PA. Digite o número desejado (ex: 1).';
                        infoNumeracao.style.display = 'block';
                    } else if (data.tem_padrao_livro_caixa) {
                        // Livros subsequentes com padrão "Livro Caixa X" - numeração automática
                        campoNomeAutomatico.style.display = 'block';
                        const nomeAutomatico = document.getElementById('nomeAutomatico');
                        const nomeHidden = document.getElementById('nomeHidden');
                        nomeAutomatico.value = data.nome_sugerido;
                        nomeHidden.value = data.nome_sugerido;
                        textoNumeracao.textContent = `Numeração automática: ${data.nome_sugerido}`;
                        infoNumeracao.style.display = 'block';
                    } else {
                        // Livros subsequentes sem padrão "Livro Caixa" - campo manual
                        campoNomeManual.style.display = 'block';
                        document.getElementById('nomeManual').required = true;
                        textoNumeracao.textContent = 'Os livros existentes não seguem o padrão "Livro Caixa X". Digite o nome manualmente.';
                        infoNumeracao.style.display = 'block';
                    }
                } else {
                    console.error('Erro ao verificar numeração:', data.message);
                    campoNomeManual.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Erro na requisição:', error);
                campoNomeManual.style.display = 'block';
            });
        }

        // Função para atualizar nome quando usuário digita número do primeiro livro
        function atualizarNomePrimeiroLivro() {
            const numeroLivro = document.getElementById('numeroLivro').value;
            const nomeHidden = document.getElementById('nomeHidden');

            if (numeroLivro && numeroLivro > 0) {
                nomeHidden.value = 'Livro Caixa ' + numeroLivro;
            } else {
                nomeHidden.value = '';
            }
        }

        // Função para atualizar nome quando usuário digita nome manual
        function atualizarNomeManual() {
            const nomeManual = document.getElementById('nomeManual').value;
            const nomeHidden = document.getElementById('nomeHidden');
            nomeHidden.value = nomeManual;
        }

        // Event listener para quando o modal for aberto
        document.addEventListener('DOMContentLoaded', function() {
            const modalNovoLivro = document.getElementById('modalNovoLivro');
            if (modalNovoLivro) {
                modalNovoLivro.addEventListener('show.bs.modal', function() {
                    // Resetar todos os campos quando modal abrir
                    document.getElementById('campoNomeManual').style.display = 'block';
                    document.getElementById('campoNumeroLivro').style.display = 'none';
                    document.getElementById('campoNomeAutomatico').style.display = 'none';
                    document.getElementById('infoNumeracao').style.display = 'none';

                    // Limpar valores
                    document.getElementById('nomeManual').value = '';
                    document.getElementById('numeroLivro').value = '';
                    document.getElementById('nomeAutomatico').value = '';
                    document.getElementById('nomeHidden').value = '';
                    document.getElementById('ponto_atendimento').value = '';

                    // Remover required de todos os campos
                    document.getElementById('nomeManual').required = false;
                    document.getElementById('numeroLivro').required = false;
                });
            }

            // Adicionar event listeners para atualizar o campo hidden
            document.getElementById('nomeManual').addEventListener('input', atualizarNomeManual);

            // Validação customizada do formulário
            document.getElementById('formNovoLivro').addEventListener('submit', function(e) {
                const nomeHidden = document.getElementById('nomeHidden').value;
                const campoNomeManual = document.getElementById('campoNomeManual');
                const campoNumeroLivro = document.getElementById('campoNumeroLivro');
                const campoNomeAutomatico = document.getElementById('campoNomeAutomatico');

                let valido = true;
                let mensagem = '';

                if (campoNomeManual.style.display !== 'none') {
                    // Campo manual visível
                    const nomeManual = document.getElementById('nomeManual').value.trim();
                    if (!nomeManual) {
                        valido = false;
                        mensagem = 'Por favor, digite o nome do livro caixa.';
                    }
                } else if (campoNumeroLivro.style.display !== 'none') {
                    // Campo número visível
                    const numeroLivro = document.getElementById('numeroLivro').value;
                    if (!numeroLivro || numeroLivro < 1) {
                        valido = false;
                        mensagem = 'Por favor, digite um número válido para o livro caixa.';
                    }
                } else if (campoNomeAutomatico.style.display !== 'none') {
                    // Campo automático - já deve ter valor
                    if (!nomeHidden) {
                        valido = false;
                        mensagem = 'Erro interno: nome automático não foi gerado.';
                    }
                }

                if (!valido) {
                    e.preventDefault();
                    alert(mensagem);
                    return false;
                }
            });
        });

        // Funções de navegação
        function visualizarLivro(id) {
            window.location.href = 'visualizar_livro.php?id=' + id;
        }

        function novaMovimentacao(id) {
            alert('Nova movimentação para livro ID: ' + id + '\n\nEsta funcionalidade será implementada em breve.');
        }

        function novaMovimentacaoMaster(id) {
            alert('Nova movimentação master para livro ID: ' + id + '\n\nEsta funcionalidade será implementada em breve.');
        }

        function imprimirLivroCompleto(id) {
            const url = 'imprimir_livro_completo.php?id=' + id;
            window.open(url, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
        }

        function verHistoricoLivro(id) {
            alert('Histórico completo do livro ID: ' + id + '\n\nEsta funcionalidade será implementada em breve.\n\nPor enquanto, use a opção "Visualizar" para ver as movimentações.');
        }

        function confirmarFecharLivro(id, nome) {
            document.getElementById('livro_id_fechar').value = id;
            document.getElementById('nome_livro_fechar').textContent = nome;
            document.getElementById('observacoes_fechamento').value = '';
            document.getElementById('confirmar_fechamento').checked = false;

            const modal = new bootstrap.Modal(document.getElementById('modalFecharLivro'));
            modal.show();
        }

        // Função para filtrar livros
        function filtrarLivros(status) {
            const url = new URL(window.location);
            if (status === 'abertos') {
                url.searchParams.delete('status');
            } else {
                url.searchParams.set('status', status);
            }
            window.location.href = url.toString();
        }

        // Formatação do campo de valor
        document.addEventListener('DOMContentLoaded', function() {
            // Configurar eventos dos filtros
            document.querySelectorAll('input[name="filtro_status"]').forEach(function(radio) {
                radio.addEventListener('change', function() {
                    if (this.checked) {
                        filtrarLivros(this.value);
                    }
                });
            });

            // Máscara monetária para o campo saldo inicial
            const saldoInput = document.getElementById('saldo_inicial');
            const saldoHidden = document.getElementById('saldo_inicial_hidden');

            if (saldoInput && saldoHidden) {
                // Função para formatar valor monetário
                function formatarMoeda(valor) {
                    // Remove tudo que não é dígito
                    valor = valor.replace(/\D/g, '');

                    // Converte para centavos
                    valor = (valor / 100).toFixed(2) + '';

                    // Adiciona separadores de milhares e decimais
                    valor = valor.replace('.', ',');
                    valor = valor.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.');

                    return valor;
                }

                // Função para converter para valor numérico
                function converterParaNumero(valorFormatado) {
                    return valorFormatado
                        .replace(/\./g, '') // Remove pontos de milhares
                        .replace(',', '.'); // Converte vírgula para ponto decimal
                }

                // Event listener para formatação em tempo real
                saldoInput.addEventListener('input', function() {
                    const valorFormatado = formatarMoeda(this.value);
                    this.value = valorFormatado;

                    // Atualiza campo hidden com valor numérico
                    saldoHidden.value = converterParaNumero(valorFormatado);
                });

                // Event listener para quando o campo perde o foco
                saldoInput.addEventListener('blur', function() {
                    if (this.value === '' || this.value === '0,00') {
                        this.value = '0,00';
                        saldoHidden.value = '0.00';
                    }
                });

                // Event listener para quando o campo ganha foco
                saldoInput.addEventListener('focus', function() {
                    if (this.value === '0,00') {
                        this.value = '';
                    }
                });
            }

            // Interceptar submit do formulário para validar restrições
            document.getElementById('formNovoLivro').addEventListener('submit', function(e) {
                const tipo = document.getElementById('tipo').value;
                const pontoAtendimento = document.getElementById('ponto_atendimento');

                if (tipo === 'master') {
                    const paTexto = pontoAtendimento.options[pontoAtendimento.selectedIndex]?.text || '';
                    const isPA88Uad = (paTexto.includes('88') && paTexto.toLowerCase().includes('uad')) ||
                                      (paTexto.includes('(PA: 88)'));

                    if (!isPA88Uad) {
                        e.preventDefault();
                        alert('Livros do tipo Master só podem ser criados para o Ponto de Atendimento "88 Uad".\n\nPor favor, selecione o PA correto ou escolha o tipo "Normal".');
                        return false;
                    }
                }

                // Garantir que o valor do saldo seja enviado corretamente
                const saldoInput = document.getElementById('saldo_inicial');
                const saldoHidden = document.getElementById('saldo_inicial_hidden');

                if (saldoInput && saldoHidden) {
                    // Atualizar o name do campo para enviar o valor correto
                    saldoInput.name = 'saldo_inicial_display';
                    saldoHidden.name = 'saldo_inicial';
                }

                // Permitir que o formulário seja enviado normalmente
                return true;
            });
        });
    </script>

    <!-- Modal de Edição Retroativa -->
    <div class="modal fade" id="modalEdicaoRetroativa" tabindex="-1" aria-labelledby="modalEdicaoRetroativaLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalEdicaoRetroativaLabel">
                        <i class="fas fa-edit me-2"></i>Configurar Edição Retroativa
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="formEdicaoRetroativa" method="POST" action="processar_controle_dia.php">
                    <div class="modal-body">
                        <input type="hidden" name="acao" value="configurar_edicao_especifica">
                        <input type="hidden" name="livro_id" id="livro_id_edicao">

                        <div class="mb-3">
                            <label class="form-label fw-bold">Livro:</label>
                            <p class="text-muted mb-0" id="nome_livro_edicao"></p>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Tipo de Edição:</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="tipo_edicao" id="edicao_geral" value="geral">
                                <label class="form-check-label" for="edicao_geral">
                                    <strong>Edição Geral</strong><br>
                                    <small class="text-muted">Permite editar movimentações de qualquer data</small>
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="tipo_edicao" id="edicao_especifica" value="especifica">
                                <label class="form-check-label" for="edicao_especifica">
                                    <strong>Edição de Data Específica</strong><br>
                                    <small class="text-muted">Permite editar apenas movimentações de uma data específica</small>
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="tipo_edicao" id="desabilitar_edicao" value="desabilitar">
                                <label class="form-check-label" for="desabilitar_edicao">
                                    <strong>Desabilitar Edição</strong><br>
                                    <small class="text-muted">Remove todas as permissões de edição retroativa</small>
                                </label>
                            </div>
                        </div>

                        <div class="mb-3" id="div_data_especifica" style="display: none;">
                            <label for="data_edicao_especifica" class="form-label">Data para Edição:</label>
                            <input type="date" class="form-control" id="data_edicao_especifica" name="data_edicao_especifica">
                            <div class="form-text">
                                Apenas movimentações desta data poderão ser editadas
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Importante:</strong> Esta configuração afeta apenas este livro específico.
                            A edição retroativa permite modificar movimentações de datas anteriores.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Salvar Configuração
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function abrirModalEdicaoRetroativa(livroId, nomeLivro, edicaoAtiva, dataEspecifica) {
            // Preencher dados do modal
            document.getElementById('livro_id_edicao').value = livroId;
            document.getElementById('nome_livro_edicao').textContent = nomeLivro;

            // Limpar seleções anteriores
            document.querySelectorAll('input[name="tipo_edicao"]').forEach(radio => radio.checked = false);
            document.getElementById('data_edicao_especifica').value = '';

            // Definir estado atual
            if (dataEspecifica) {
                // Tem data específica configurada
                document.getElementById('edicao_especifica').checked = true;
                document.getElementById('data_edicao_especifica').value = dataEspecifica;
                document.getElementById('div_data_especifica').style.display = 'block';
            } else if (edicaoAtiva) {
                // Edição geral ativa
                document.getElementById('edicao_geral').checked = true;
            } else {
                // Edição desabilitada
                document.getElementById('desabilitar_edicao').checked = true;
            }

            // Abrir modal
            new bootstrap.Modal(document.getElementById('modalEdicaoRetroativa')).show();
        }

        // Controlar exibição do campo de data
        document.querySelectorAll('input[name="tipo_edicao"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const divData = document.getElementById('div_data_especifica');
                if (this.value === 'especifica') {
                    divData.style.display = 'block';
                    document.getElementById('data_edicao_especifica').required = true;
                } else {
                    divData.style.display = 'none';
                    document.getElementById('data_edicao_especifica').required = false;
                }
            });
        });
    </script>
</body>
</html>
