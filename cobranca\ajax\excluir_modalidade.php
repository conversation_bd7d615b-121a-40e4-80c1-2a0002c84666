<?php
// Iniciar sessão se ainda não estiver iniciada
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Definir cabeçalho para garantir que a resposta seja JSON
header('Content-Type: application/json');

// Iniciar captura de saída para evitar que HTML acidental seja enviado
ob_start();

try {
    require_once __DIR__ . '/../../config/database.php';

    // Verificar se o usuário tem permissão de GESTOR
    if (!isset($_SESSION['user_id'])) {
        throw new Exception('Usuário não autenticado.');
    }
    
    $stmt = $pdo->prepare("
        SELECT tipo_acesso 
        FROM cbp_permissoes_usuarios 
        WHERE usuario_id = ? 
        AND ativo = 1
        AND tipo_acesso = 'GESTOR'
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $permissao = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$permissao) {
        throw new Exception('Acesso não autorizado. Apenas gestores podem excluir modalidades.');
    }

    // Log para depuração
    error_log("=== INÍCIO DO PROCESSO DE EXCLUSÃO DE MODALIDADE ===");
    error_log("Método: " . $_SERVER['REQUEST_METHOD']);
    error_log("Dados recebidos: " . print_r($_POST, true));

    // Verificar se a requisição é POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido.');
    }

    // Validar parâmetros obrigatórios
    if (!isset($_POST['modalidade_id']) || !is_numeric($_POST['modalidade_id'])) {
        throw new Exception('ID da modalidade inválido.');
    }

    $modalidade_id = (int)$_POST['modalidade_id'];
    error_log("Modalidade ID para exclusão: " . $modalidade_id);

    // Iniciar transação
    $pdo->beginTransaction();

    // Verificar se a modalidade existe
    $stmt = $pdo->prepare("SELECT * FROM cbp_modalidades_processo WHERE id = ?");
    $stmt->execute([$modalidade_id]);
    $modalidade = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$modalidade) {
        throw new Exception('Modalidade não encontrada.');
    }
    
    error_log("Dados da modalidade encontrada para exclusão:");
    error_log(print_r($modalidade, true));
    
    // Verificar se a modalidade está sendo usada em processos
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_processos_judiciais WHERE modalidade_id = ?");
    $stmt->execute([$modalidade_id]);
    $processos_count = $stmt->fetchColumn();
    
    if ($processos_count > 0) {
        throw new Exception('Não é possível excluir esta modalidade porque ela está vinculada a ' . $processos_count . ' processo(s) judicial(is).');
    }
    
    // Excluir a modalidade
    $stmt = $pdo->prepare("DELETE FROM cbp_modalidades_processo WHERE id = ?");
    $stmt->execute([$modalidade_id]);
    
    if ($stmt->rowCount() === 0) {
        throw new Exception('Falha ao excluir a modalidade.');
    }

    error_log("Modalidade excluída com sucesso");

    // Registrar a operação no log do sistema
    $detalhes_log = "Exclusão de modalidade - ID: {$modalidade_id} - Nome: {$modalidade['nome']}";
    
    $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$_SESSION['user_id'], 'Exclusão de Modalidade', $detalhes_log]);
    
    error_log("Registro de log criado para a exclusão da modalidade");

    // Commit
    $pdo->commit();
    error_log("=== FIM DA EXCLUSÃO DE MODALIDADE - SUCESSO ===");

    // Limpar buffer de saída antes de enviar a resposta JSON
    ob_end_clean();
    
    // Responder com sucesso em formato JSON
    echo json_encode([
        'success' => true,
        'message' => 'Modalidade excluída com sucesso.'
    ]);
    exit;

} catch (PDOException $e) {
    // Rollback em caso de erro com o banco de dados
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("=== ERRO NA EXCLUSÃO DE MODALIDADE (PDO) ===");
    error_log("Mensagem: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    error_log("=== FIM DO ERRO ===");

    // Limpar buffer de saída antes de enviar a resposta JSON
    ob_end_clean();
    
    // Responder com erro em formato JSON
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro de banco de dados: ' . $e->getMessage()
    ]);
    exit;

} catch (Exception $e) {
    // Rollback em caso de qualquer outro erro
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("=== ERRO NA EXCLUSÃO DE MODALIDADE ===");
    error_log("Mensagem: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    error_log("=== FIM DO ERRO ===");

    // Limpar buffer de saída antes de enviar a resposta JSON
    ob_end_clean();
    
    // Responder com erro em formato JSON
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    exit;
} 