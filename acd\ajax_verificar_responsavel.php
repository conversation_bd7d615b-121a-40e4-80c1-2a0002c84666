<?php
require_once '../config/database.php';
require_once 'check_acd_permission.php';
require_once 'vinculos_functions.php';
session_start();

// Verificar se o usuário está logado e tem permissão
if (!isset($_SESSION['user_id']) || !checkACDPermission($_SESSION['user_id'])) {
    http_response_code(403);
    echo json_encode(['erro' => 'Acesso negado']);
    exit;
}

// Verificar se é uma requisição POST com PA ID
if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['pa_id'])) {
    http_response_code(400);
    echo json_encode(['erro' => 'Parâmetros inválidos']);
    exit;
}

$pa_id = intval($_POST['pa_id']);
$data = $_POST['data'] ?? date('Y-m-d');

try {
    // Verificar se PA tem responsável ativo
    $responsavel = verificarPATemResponsavel($pa_id, $data);
    
    if ($responsavel) {
        // Buscar informações do PA
        $stmt = $pdo->prepare("SELECT numero, nome FROM pontos_atendimento WHERE id = ?");
        $stmt->execute([$pa_id]);
        $pa_info = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'tem_responsavel' => true,
            'responsavel' => [
                'id' => $responsavel['usuario_id'],
                'nome' => $responsavel['usuario_nome'],
                'email' => $responsavel['usuario_email'],
                'data_inicio' => $responsavel['data_inicio']
            ],
            'pa' => [
                'id' => $pa_id,
                'numero' => $pa_info['numero'],
                'nome' => $pa_info['nome']
            ]
        ]);
    } else {
        // Buscar informações do PA
        $stmt = $pdo->prepare("SELECT numero, nome FROM pontos_atendimento WHERE id = ?");
        $stmt->execute([$pa_id]);
        $pa_info = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'tem_responsavel' => false,
            'pa' => [
                'id' => $pa_id,
                'numero' => $pa_info['numero'],
                'nome' => $pa_info['nome']
            ]
        ]);
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['erro' => 'Erro interno: ' . $e->getMessage()]);
}
?>
