<?php
require_once 'funcoes_dias_uteis.php';

/**
 * Verifica divergências entre saldo do livro master e livro da agência
 * Considera a regra especial dos ATMs (lançamento no dia seguinte para competência do dia anterior)
 */
function verificarDivergenciaSaldo($pdo, $livro_agencia_id, $ponto_atendimento_id) {
    try {
        // Buscar dados do livro da agência
        $stmt = $pdo->prepare("
            SELECT lc.*, pa.nome as pa_nome, pa.numero as pa_numero
            FROM lcx_livros_caixa lc
            JOIN pontos_atendimento pa ON lc.ponto_atendimento_id = pa.id
            WHERE lc.id = ? AND lc.tipo = 'normal'
        ");
        $stmt->execute([$livro_agencia_id]);
        $livro_agencia = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$livro_agencia) {
            return null; // Livro não encontrado ou não é de agência
        }
        
        // Buscar livro master ativo (qualquer PA para teste, depois restringir para PA 88)
        $stmt = $pdo->prepare("
            SELECT id, saldo_atual, nome, ponto_atendimento_id
            FROM lcx_livros_caixa
            WHERE tipo = 'master' AND status = 'aberto'
            ORDER BY created_at DESC
            LIMIT 1
        ");
        $stmt->execute();
        $livro_master = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$livro_master) {
            error_log("DEBUG: Nenhum livro master ativo encontrado");
            return null; // Não há livro master ativo
        }

        error_log("DEBUG: Livro master encontrado: " . $livro_master['nome'] . " (ID: " . $livro_master['id'] . ", PA: " . $livro_master['ponto_atendimento_id'] . ")");
        
        // Calcular saldo esperado da agência considerando ATMs
        $saldo_esperado = calcularSaldoEsperadoAgencia($pdo, $livro_master['id'], $ponto_atendimento_id);

        if ($saldo_esperado === null) {
            return null; // Não foi possível calcular
        }

        $saldo_atual_agencia = floatval($livro_agencia['saldo_atual']);

        // Ajustar o saldo da agência removendo ATMs que já foram enviados ao master
        // mas que impactaram o saldo da agência retroativamente
        $saldo_agencia_ajustado = ajustarSaldoAgenciaParaComparacao($pdo, $livro_agencia['id'], $saldo_atual_agencia);

        $diferenca = $saldo_agencia_ajustado - $saldo_esperado;
        
        // Considerar divergência se diferença for maior que R$ 0,01
        if (abs($diferenca) > 0.01) {
            return [
                'tem_divergencia' => true,
                'saldo_agencia' => $saldo_atual_agencia,
                'saldo_agencia_ajustado' => $saldo_agencia_ajustado,
                'saldo_esperado' => $saldo_esperado,
                'diferenca' => $diferenca,
                'livro_master_id' => $livro_master['id'],
                'livro_master_nome' => $livro_master['nome'],
                'pa_nome' => $livro_agencia['pa_nome'],
                'pa_numero' => $livro_agencia['pa_numero'],
                'data_verificacao' => date('Y-m-d H:i:s')
            ];
        }

        return [
            'tem_divergencia' => false,
            'saldo_agencia' => $saldo_atual_agencia,
            'saldo_agencia_ajustado' => $saldo_agencia_ajustado,
            'saldo_esperado' => $saldo_esperado,
            'diferenca' => 0,
            'livro_master_id' => $livro_master['id'],
            'livro_master_nome' => $livro_master['nome']
        ];
        
    } catch (Exception $e) {
        error_log("Erro ao verificar divergência de saldo: " . $e->getMessage());
        return null;
    }
}

/**
 * Calcula o saldo esperado da agência baseado no livro master
 * Considera movimentações ATM com regra especial de datas
 */
function calcularSaldoEsperadoAgencia($pdo, $livro_master_id, $ponto_atendimento_id) {
    try {
        $hoje = date('Y-m-d');
        $ontem = date('Y-m-d', strtotime('-1 day'));

        // Buscar informações do PA
        $stmt_pa = $pdo->prepare("SELECT nome, numero FROM pontos_atendimento WHERE id = ?");
        $stmt_pa->execute([$ponto_atendimento_id]);
        $pa_info = $stmt_pa->fetch(PDO::FETCH_ASSOC);

        if (!$pa_info) {
            return null;
        }

        // Buscar último lançamento no livro master para este PA
        // Vamos buscar por várias possibilidades de categoria
        $categorias_busca = [
            'Agência ' . $ponto_atendimento_id,  // "Agência 1"
            'PA ' . $ponto_atendimento_id,       // "PA 1"
            $pa_info['nome'],                    // Nome do PA
            'Agência ' . $pa_info['nome']        // "Agência" + nome
        ];

        if ($pa_info['numero']) {
            $categorias_busca[] = 'PA ' . $pa_info['numero'];
            $categorias_busca[] = 'Agência ' . $pa_info['numero'];
        }

        $ultimo_lancamento_master = null;
        foreach ($categorias_busca as $categoria) {
            $stmt = $pdo->prepare("
                SELECT valor, tipo, data_competencia, origem, data_movimentacao, categoria
                FROM lcx_movimentacoes
                WHERE livro_caixa_id = ?
                AND categoria LIKE ?
                ORDER BY data_competencia DESC, data_movimentacao DESC
                LIMIT 1
            ");

            $busca = '%' . $categoria . '%';
            $stmt->execute([$livro_master_id, $busca]);
            $resultado = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($resultado) {
                $ultimo_lancamento_master = $resultado;
                break;
            }
        }

        if (!$ultimo_lancamento_master) {
            return 0; // Sem lançamentos no master para este PA
        }

        $saldo_master = floatval($ultimo_lancamento_master['valor']);
        if ($ultimo_lancamento_master['tipo'] === 'saida') {
            $saldo_master = -$saldo_master;
        }

        // Verificar se há movimentações ATM pendentes de lançamento
        $saldo_atm_pendente = calcularSaldoATMPendente($pdo, $ponto_atendimento_id, $hoje, $ontem);

        return $saldo_master + $saldo_atm_pendente;

    } catch (Exception $e) {
        error_log("Erro ao calcular saldo esperado: " . $e->getMessage());
        return null;
    }
}

/**
 * Ajusta o saldo da agência para comparação, removendo ATMs que já foram enviados ao master
 * mas que impactaram o saldo retroativamente
 */
function ajustarSaldoAgenciaParaComparacao($pdo, $livro_agencia_id, $saldo_atual) {
    try {
        $hoje = date('Y-m-d');
        $ontem = date('Y-m-d', strtotime('-1 day'));

        // Buscar movimentações ATM que foram lançadas hoje mas com competência de ontem
        // Estas movimentações já estão no master, mas afetaram o saldo da agência retroativamente
        $stmt = $pdo->prepare("
            SELECT SUM(CASE WHEN tipo = 'entrada' THEN valor ELSE -valor END) as saldo_atm_retroativo
            FROM lcx_movimentacoes
            WHERE livro_caixa_id = ?
            AND origem = 'atm'
            AND data_competencia = ?
            AND DATE(data_movimentacao) = ?
        ");

        $stmt->execute([$livro_agencia_id, $ontem, $hoje]);
        $resultado = $stmt->fetch(PDO::FETCH_ASSOC);
        $saldo_atm_retroativo = floatval($resultado['saldo_atm_retroativo'] ?? 0);

        // Remover o impacto dos ATMs retroativos para comparar com o master
        // que já considera estes ATMs no saldo esperado
        return $saldo_atual - $saldo_atm_retroativo;

    } catch (Exception $e) {
        error_log("Erro ao ajustar saldo da agência: " . $e->getMessage());
        return $saldo_atual; // Retorna saldo original em caso de erro
    }
}

/**
 * Calcula saldo de movimentações ATM que ainda não foram lançadas no master
 * ATMs são lançados no dia seguinte para competência do dia anterior
 */
function calcularSaldoATMPendente($pdo, $ponto_atendimento_id, $data_atual, $data_anterior) {
    try {
        // Buscar livro da agência para este PA
        $stmt = $pdo->prepare("
            SELECT id FROM lcx_livros_caixa
            WHERE ponto_atendimento_id = ? AND tipo = 'normal' AND status = 'aberto'
            ORDER BY created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$ponto_atendimento_id]);
        $livro_agencia = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$livro_agencia) {
            return 0;
        }

        // Buscar movimentações ATM que foram lançadas hoje (data_movimentacao = hoje)
        // mas com competência de ontem (data_competencia = ontem)
        // Estas são as movimentações que "chegaram" hoje mas afetam o saldo de ontem
        $stmt = $pdo->prepare("
            SELECT SUM(CASE WHEN tipo = 'entrada' THEN valor ELSE -valor END) as saldo_atm
            FROM lcx_movimentacoes
            WHERE livro_caixa_id = ?
            AND origem = 'atm'
            AND data_competencia = ?
            AND DATE(data_movimentacao) = ?
        ");

        $stmt->execute([$livro_agencia['id'], $data_anterior, $data_atual]);
        $resultado = $stmt->fetch(PDO::FETCH_ASSOC);

        return floatval($resultado['saldo_atm'] ?? 0);

    } catch (Exception $e) {
        error_log("Erro ao calcular saldo ATM pendente: " . $e->getMessage());
        return 0;
    }
}

/**
 * Busca todas as divergências de saldo para um relatório
 */
function buscarTodasDivergencias($pdo) {
    try {
        $divergencias = [];
        
        // Buscar todos os livros de agência ativos
        $stmt = $pdo->query("
            SELECT lc.id, lc.ponto_atendimento_id, pa.nome as pa_nome, pa.numero as pa_numero
            FROM lcx_livros_caixa lc
            JOIN pontos_atendimento pa ON lc.ponto_atendimento_id = pa.id
            WHERE lc.tipo = 'normal' AND lc.status = 'aberto'
            ORDER BY pa.nome
        ");
        
        $livros_agencia = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($livros_agencia as $livro) {
            $divergencia = verificarDivergenciaSaldo($pdo, $livro['id'], $livro['ponto_atendimento_id']);
            
            if ($divergencia && $divergencia['tem_divergencia']) {
                $divergencias[] = array_merge($divergencia, [
                    'livro_agencia_id' => $livro['id']
                ]);
            }
        }
        
        return $divergencias;
        
    } catch (Exception $e) {
        error_log("Erro ao buscar todas as divergências: " . $e->getMessage());
        return [];
    }
}

/**
 * Formata valor monetário para exibição
 */
function formatarValorMonetario($valor) {
    return 'R$ ' . number_format($valor, 2, ',', '.');
}

/**
 * Retorna classe CSS baseada no tipo de divergência
 */
function getClasseDivergencia($diferenca) {
    if ($diferenca > 0) {
        return 'text-success'; // Saldo maior que esperado
    } else {
        return 'text-danger'; // Saldo menor que esperado
    }
}

/**
 * Verifica divergências entre saldo do livro master e livro da agência em uma data específica
 * Considera a regra especial dos ATMs (lançamento no dia seguinte para competência do dia anterior)
 */
function verificarDivergenciaSaldoPorData($pdo, $livro_agencia_id, $ponto_atendimento_id, $data_referencia) {
    try {
        // Buscar dados do livro da agência
        $stmt = $pdo->prepare("
            SELECT lc.*, pa.nome as pa_nome, pa.numero as pa_numero
            FROM lcx_livros_caixa lc
            JOIN pontos_atendimento pa ON lc.ponto_atendimento_id = pa.id
            WHERE lc.id = ? AND lc.tipo = 'normal'
        ");
        $stmt->execute([$livro_agencia_id]);
        $livro_agencia = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$livro_agencia) {
            return null; // Livro não encontrado ou não é de agência
        }

        // Buscar livro master correspondente
        $stmt = $pdo->prepare("
            SELECT id, nome, ponto_atendimento_id
            FROM lcx_livros_caixa
            WHERE tipo = 'master' AND status = 'aberto'
            ORDER BY created_at DESC
            LIMIT 1
        ");
        $stmt->execute();
        $livro_master = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$livro_master) {
            return null; // Livro master não encontrado
        }

        // Calcular saldo esperado da agência considerando ATMs até a data específica
        $saldo_esperado = calcularSaldoEsperadoAgenciaPorData($pdo, $livro_master['id'], $ponto_atendimento_id, $data_referencia);

        if ($saldo_esperado === null) {
            return null; // Não foi possível calcular
        }

        // Calcular saldo da agência até a data específica
        $saldo_agencia_na_data = calcularSaldoAgenciaPorData($pdo, $livro_agencia_id, $data_referencia);

        if ($saldo_agencia_na_data === null) {
            return null; // Não foi possível calcular
        }

        $diferenca = $saldo_agencia_na_data - $saldo_esperado;

        // Considerar divergência se diferença for maior que R$ 0,01
        if (abs($diferenca) > 0.01) {
            return [
                'tem_divergencia' => true,
                'saldo_agencia' => $saldo_agencia_na_data,
                'saldo_esperado' => $saldo_esperado,
                'diferenca' => $diferenca,
                'livro_master_id' => $livro_master['id'],
                'livro_master_nome' => $livro_master['nome'],
                'pa_nome' => $livro_agencia['pa_nome'],
                'pa_numero' => $livro_agencia['pa_numero'],
                'data_referencia' => $data_referencia,
                'data_verificacao' => date('Y-m-d H:i:s')
            ];
        }

        return [
            'tem_divergencia' => false,
            'saldo_agencia' => $saldo_agencia_na_data,
            'saldo_esperado' => $saldo_esperado,
            'diferenca' => 0,
            'livro_master_id' => $livro_master['id'],
            'livro_master_nome' => $livro_master['nome'],
            'data_referencia' => $data_referencia
        ];

    } catch (Exception $e) {
        error_log("Erro ao verificar divergência de saldo por data: " . $e->getMessage());
        return null;
    }
}

/**
 * Calcula o saldo esperado da agência baseado no livro master até uma data específica
 */
function calcularSaldoEsperadoAgenciaPorData($pdo, $livro_master_id, $ponto_atendimento_id, $data_referencia) {
    try {
        // Buscar informações do PA
        $stmt_pa = $pdo->prepare("SELECT nome, numero FROM pontos_atendimento WHERE id = ?");
        $stmt_pa->execute([$ponto_atendimento_id]);
        $pa_info = $stmt_pa->fetch(PDO::FETCH_ASSOC);

        if (!$pa_info) {
            return null;
        }

        // Buscar a última movimentação do PA no master até a data de referência
        // O que importa é o saldo_posterior da última movimentação, não a soma de todas
        $categorias_busca = [
            '(PA: ' . $pa_info['numero'] . ')',
            $pa_info['nome'] . ' (PA: ' . $pa_info['numero'] . ')'
        ];

        $ultima_movimentacao = null;
        foreach ($categorias_busca as $categoria) {
            $stmt = $pdo->prepare("
                SELECT saldo_posterior, data_competencia, data_movimentacao, categoria, valor, tipo
                FROM lcx_movimentacoes
                WHERE livro_caixa_id = ?
                AND categoria LIKE ?
                AND data_competencia <= ?
                ORDER BY data_competencia DESC, data_movimentacao DESC, id DESC
                LIMIT 1
            ");

            $busca = '%' . $categoria . '%';
            $stmt->execute([$livro_master_id, $busca, $data_referencia]);
            $resultado = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($resultado) {
                $ultima_movimentacao = $resultado;
                break;
            }
        }

        if (!$ultima_movimentacao) {
            // Se não há movimentações do PA no master, o saldo esperado é 0
            return 0;
        }

        // O saldo esperado é o saldo_posterior da última movimentação do PA no master
        return floatval($ultima_movimentacao['saldo_posterior']);

    } catch (Exception $e) {
        error_log("Erro ao calcular saldo esperado por data: " . $e->getMessage());
        return null;
    }
}

/**
 * Calcula o saldo da agência até uma data específica
 */
function calcularSaldoAgenciaPorData($pdo, $livro_agencia_id, $data_referencia) {
    try {
        // Buscar saldo inicial do livro
        $stmt = $pdo->prepare("SELECT saldo_inicial FROM lcx_livros_caixa WHERE id = ?");
        $stmt->execute([$livro_agencia_id]);
        $livro = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$livro) {
            return null;
        }

        $saldo = floatval($livro['saldo_inicial']);

        // Somar todas as movimentações até a data de referência
        $stmt = $pdo->prepare("
            SELECT SUM(CASE WHEN tipo = 'entrada' THEN valor ELSE -valor END) as total_movimentacoes
            FROM lcx_movimentacoes
            WHERE livro_caixa_id = ?
            AND data_competencia <= ?
        ");
        $stmt->execute([$livro_agencia_id, $data_referencia]);
        $resultado = $stmt->fetch(PDO::FETCH_ASSOC);

        $total_movimentacoes = floatval($resultado['total_movimentacoes'] ?? 0);

        return $saldo + $total_movimentacoes;

    } catch (Exception $e) {
        error_log("Erro ao calcular saldo da agência por data: " . $e->getMessage());
        return null;
    }
}
?>
