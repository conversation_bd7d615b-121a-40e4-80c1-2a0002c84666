<?php
session_start();
require_once '../config/database.php';
require_once 'check_acd_permission.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se o usuário tem permissão para acessar a pasta ACD
if (!checkACDPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Verificar se o usuário é administrador ou gestor
if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Função para buscar usuários da API
function buscarTodosUsuarios() {
    $apiFields = [
        'api_user' => 'UFL7GXZ14LU9NOR',
        'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
        'api_module' => 'Usuarios',
        'api_action' => 'listarUsuarios'
    ];

    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => http_build_query($apiFields),
    ));

    $response = curl_exec($curl);

    if (curl_errno($curl)) {
        curl_close($curl);
        return [];
    }

    curl_close($curl);

    $usuarios = json_decode($response, true);

    if (!is_array($usuarios)) {
        return [];
    }

    $usuarios_mapeados = [];
    foreach ($usuarios as $user) {
        if (isset($user['status']) && $user['status'] == 1 &&
            isset($user['bloqueado']) && $user['bloqueado'] == 0) {
            $usuarios_mapeados[$user['id']] = [
                'id' => $user['id'],
                'nome' => $user['nome'] ?? '',
                'pa' => $user['nomeAgencia'] ?? '',
                'login' => $user['loginAD'] ?? $user['loginSISBR'] ?? ''
            ];
        }
    }

    return $usuarios_mapeados;
}

// Processar filtros
$filtro_periodo = $_GET['periodo'] ?? '';
$filtro_pa = $_GET['pa'] ?? '';
$filtro_usuario = $_GET['usuario_id'] ?? '';
$filtro_usuario_pa = $_GET['usuario_pa'] ?? '';
$filtro_acao = $_GET['acao'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 50;
$offset = ($page - 1) * $per_page;

try {
    // Buscar usuários da API
    $usuarios_api = buscarTodosUsuarios();

    // Construir WHERE clause baseado nos filtros
    $where_conditions = [];
    $params = [];

    if (!empty($filtro_periodo)) {
        switch ($filtro_periodo) {
            case 'hoje':
                $where_conditions[] = "DATE(f.data_criacao) = CURDATE()";
                break;
            case 'semana':
                $where_conditions[] = "YEARWEEK(f.data_criacao, 1) = YEARWEEK(CURDATE(), 1)";
                break;
            case 'mes':
                $where_conditions[] = "YEAR(f.data_criacao) = YEAR(CURDATE()) AND MONTH(f.data_criacao) = MONTH(CURDATE())";
                break;
            case 'ano':
                $where_conditions[] = "YEAR(f.data_criacao) = YEAR(CURDATE())";
                break;
        }
    }

    if (!empty($filtro_pa)) {
        $where_conditions[] = "f.pa = ?";
        $params[] = $filtro_pa;
    }

    if (!empty($filtro_usuario)) {
        $where_conditions[] = "f.usuario_id = ?";
        $params[] = $filtro_usuario;
    }

    if (!empty($filtro_usuario_pa)) {
        $where_conditions[] = "f.usuario_pa = ?";
        $params[] = $filtro_usuario_pa;
    }

    if (!empty($filtro_acao)) {
        $where_conditions[] = "f.acao = ?";
        $params[] = $filtro_acao;
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // Contar total de registros
    $count_sql = "SELECT COUNT(*) as total FROM acd_formularios f $where_clause";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_records = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_pages = ceil($total_records / $per_page);

    // Buscar propostas com paginação
    $sql = "
        SELECT
            f.pa,
            f.usuario_id,
            f.usuario_pa,
            f.acao,
            f.data_criacao,
            u.nome_completo as usuario_nome
        FROM acd_formularios f
        LEFT JOIN usuarios u ON f.usuario_id = u.id
        $where_clause
        ORDER BY f.data_criacao DESC
        LIMIT $per_page OFFSET $offset
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $propostas = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Buscar lista de PAs para o filtro
    $stmt = $pdo->query("
        SELECT DISTINCT f.pa
        FROM acd_formularios f
        WHERE f.pa IS NOT NULL
        ORDER BY f.pa
    ");
    $pas_disponiveis = $stmt->fetchAll(PDO::FETCH_COLUMN);

    // Buscar lista de usuários para o filtro
    $stmt = $pdo->query("
        SELECT DISTINCT f.usuario_id, u.nome_completo
        FROM acd_formularios f
        LEFT JOIN usuarios u ON f.usuario_id = u.id
        WHERE f.usuario_id IS NOT NULL
        ORDER BY u.nome_completo, f.usuario_id
    ");
    $usuarios_disponiveis = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Buscar lista de usuario_pa para o filtro
    $stmt = $pdo->query("
        SELECT DISTINCT f.usuario_pa
        FROM acd_formularios f
        WHERE f.usuario_pa IS NOT NULL
        ORDER BY f.usuario_pa
    ");
    $usuarios_pa_disponiveis = $stmt->fetchAll(PDO::FETCH_COLUMN);

} catch (Exception $e) {
    error_log("Erro ao buscar relatórios: " . $e->getMessage());
    $propostas = [];
    $pas_disponiveis = [];
    $usuarios_disponiveis = [];
    $usuarios_pa_disponiveis = [];
    $usuarios_api = [];
    $total_records = 0;
    $total_pages = 0;
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatórios - ACD</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #003641;
            --secondary-color: #00AE9D;
            --text-light: #666666;
            --text-dark: #333333;
            --bg-light: #f5f5f5;
            --bg-white: #ffffff;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: var(--bg-light);
            margin: 0;
            padding: 0;
        }

        .wrapper {
            display: flex;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
            padding: 1.5rem;
            margin-left: 250px;
        }

        .page-title {
            color: var(--primary-color);
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filters-section {
            background: var(--bg-white);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
        }

        .filters-title {
            color: var(--primary-color);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .filter-label {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-dark);
        }

        .filter-select, .filter-input {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9rem;
            background: white;
        }

        .filter-select:focus, .filter-input:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 2px rgba(0, 174, 157, 0.2);
        }

        .filter-actions {
            display: flex;
            gap: 0.75rem;
            justify-content: flex-end;
        }

        .btn-filter {
            background: var(--secondary-color);
            color: white;
            border: none;
            padding: 0.6rem 1.2rem;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-filter:hover {
            background: #008a7a;
            transform: translateY(-1px);
        }

        .btn-clear {
            background: #6c757d;
            color: white;
            border: none;
            padding: 0.6rem 1.2rem;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-clear:hover {
            background: #5a6268;
        }

        .results-section {
            background: var(--bg-white);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
        }

        .results-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .results-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
        }

        .results-count {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .table-container {
            overflow-x: auto;
        }

        .proposals-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.85rem;
        }

        .proposals-table th {
            background: #f8f9fa;
            color: var(--text-dark);
            font-weight: 600;
            padding: 0.75rem;
            text-align: left;
            border-bottom: 2px solid #dee2e6;
            white-space: nowrap;
        }

        .proposals-table td {
            padding: 0.75rem;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }

        .proposals-table tbody tr:hover {
            background: rgba(0, 174, 157, 0.05);
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-submeter {
            background: #d4edda;
            color: #155724;
        }

        .status-devolver {
            background: #f8d7da;
            color: #721c24;
        }

        .pagination-container {
            padding: 1rem 1.5rem;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: between;
            align-items: center;
            gap: 1rem;
        }

        .pagination {
            display: flex;
            gap: 0.25rem;
            margin: 0;
        }

        .page-link {
            padding: 0.5rem 0.75rem;
            border: 1px solid #dee2e6;
            color: var(--primary-color);
            text-decoration: none;
            border-radius: 4px;
            font-size: 0.85rem;
        }

        .page-link:hover {
            background: var(--secondary-color);
            color: white;
            border-color: var(--secondary-color);
        }

        .page-link.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .page-info {
            font-size: 0.85rem;
            color: var(--text-light);
        }

        /* Responsividade */
        @media (max-width: 1200px) {
            .main-content {
                margin-left: 0;
                padding: 1rem;
            }
        }

        @media (max-width: 768px) {
            .filters-grid {
                grid-template-columns: 1fr;
            }
            
            .filter-actions {
                justify-content: stretch;
            }
            
            .btn-filter, .btn-clear {
                flex: 1;
            }
            
            .results-header {
                flex-direction: column;
                gap: 0.5rem;
                text-align: center;
            }
            
            .pagination-container {
                flex-direction: column;
                gap: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'components/sidebar.php'; ?>
        
        <div class="main-content">
            <h1 class="page-title">
                <i class="fas fa-file-alt"></i>
                Relatórios de Propostas
            </h1>

            <!-- Seção de Filtros -->
            <div class="filters-section">
                <div class="filters-title">
                    <i class="fas fa-filter"></i>
                    Filtros
                </div>

                <form method="GET" action="">
                    <div class="filters-grid">
                        <div class="filter-group">
                            <label class="filter-label">Período</label>
                            <select name="periodo" class="filter-select">
                                <option value="">Todos os períodos</option>
                                <option value="hoje" <?php echo $filtro_periodo === 'hoje' ? 'selected' : ''; ?>>Hoje</option>
                                <option value="semana" <?php echo $filtro_periodo === 'semana' ? 'selected' : ''; ?>>Esta semana</option>
                                <option value="mes" <?php echo $filtro_periodo === 'mes' ? 'selected' : ''; ?>>Este mês</option>
                                <option value="ano" <?php echo $filtro_periodo === 'ano' ? 'selected' : ''; ?>>Este ano</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">Ponto de Atendimento</label>
                            <select name="pa" class="filter-select">
                                <option value="">Todos os PAs</option>
                                <?php foreach ($pas_disponiveis as $pa): ?>
                                    <option value="<?php echo htmlspecialchars($pa); ?>"
                                            <?php echo $filtro_pa === $pa ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($pa); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">Usuário (Análise)</label>
                            <select name="usuario_id" class="filter-select">
                                <option value="">Todos os usuários</option>
                                <?php foreach ($usuarios_disponiveis as $usuario): ?>
                                    <?php
                                    $nome_exibir = $usuario['nome_completo'] ? $usuario['nome_completo'] : 'ID: ' . $usuario['usuario_id'];
                                    ?>
                                    <option value="<?php echo $usuario['usuario_id']; ?>"
                                            <?php echo $filtro_usuario == $usuario['usuario_id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($nome_exibir); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">Usuário do PA</label>
                            <select name="usuario_pa" class="filter-select">
                                <option value="">Todos os usuários PA</option>
                                <?php foreach ($usuarios_pa_disponiveis as $usuario_pa_id): ?>
                                    <?php
                                    $nome_usuario_pa = isset($usuarios_api[$usuario_pa_id]) ? $usuarios_api[$usuario_pa_id]['nome'] : "ID: $usuario_pa_id";
                                    ?>
                                    <option value="<?php echo $usuario_pa_id; ?>"
                                            <?php echo $filtro_usuario_pa == $usuario_pa_id ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($nome_usuario_pa); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label class="filter-label">Ação</label>
                            <select name="acao" class="filter-select">
                                <option value="">Todas as ações</option>
                                <option value="submeter" <?php echo $filtro_acao === 'submeter' ? 'selected' : ''; ?>>Submeter</option>
                                <option value="devolver" <?php echo $filtro_acao === 'devolver' ? 'selected' : ''; ?>>Devolver</option>
                            </select>
                        </div>
                    </div>

                    <div class="filter-actions">
                        <button type="button" class="btn-clear" onclick="window.location.href='relatorios.php'">
                            <i class="fas fa-times"></i> Limpar
                        </button>
                        <button type="submit" class="btn-filter">
                            <i class="fas fa-search"></i> Filtrar
                        </button>
                    </div>
                </form>
            </div>

            <!-- Seção de Resultados -->
            <div class="results-section">
                <div class="results-header">
                    <h3 class="results-title">
                        <i class="fas fa-list"></i>
                        Propostas Encontradas
                    </h3>
                    <div class="results-count">
                        <?php echo number_format($total_records, 0, ',', '.'); ?> registro(s) encontrado(s)
                    </div>
                </div>

                <?php if (!empty($propostas)): ?>
                    <div class="table-container">
                        <table class="proposals-table">
                            <thead>
                                <tr>
                                    <th>Data/Hora</th>
                                    <th>PA</th>
                                    <th>Usuário (Análise)</th>
                                    <th>Usuário do PA</th>
                                    <th>Ação</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($propostas as $proposta): ?>
                                    <?php
                                    // Usuário do PA (usuario_pa da API)
                                    $usuario_pa_info = isset($usuarios_api[$proposta['usuario_pa']]) ? $usuarios_api[$proposta['usuario_pa']] : null;
                                    $nome_usuario_pa = $usuario_pa_info ? $usuario_pa_info['nome'] : ($proposta['usuario_pa'] ? "ID: " . $proposta['usuario_pa'] : 'N/A');
                                    ?>
                                    <tr>
                                        <td>
                                            <?php
                                            $data = new DateTime($proposta['data_criacao']);
                                            echo $data->format('d/m/Y H:i');
                                            ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($proposta['pa']); ?></td>
                                        <td><?php echo htmlspecialchars($proposta['usuario_nome'] ?? 'ID: ' . $proposta['usuario_id']); ?></td>
                                        <td><?php echo htmlspecialchars($nome_usuario_pa); ?></td>
                                        <td>
                                            <span class="status-badge status-<?php echo $proposta['acao']; ?>">
                                                <?php echo $proposta['acao'] === 'submeter' ? 'Submeter' : 'Devolver'; ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Paginação -->
                    <?php if ($total_pages > 1): ?>
                        <div class="pagination-container">
                            <div class="page-info">
                                Página <?php echo $page; ?> de <?php echo $total_pages; ?>
                                (<?php echo number_format($total_records, 0, ',', '.'); ?> registros)
                            </div>

                            <div class="pagination">
                                <?php
                                $filtros_url = http_build_query([
                                    'periodo' => $filtro_periodo,
                                    'pa' => $filtro_pa,
                                    'usuario_id' => $filtro_usuario,
                                    'usuario_pa' => $filtro_usuario_pa,
                                    'acao' => $filtro_acao
                                ]);
                                $filtros_url = $filtros_url ? '&' . $filtros_url : '';
                                ?>

                                <?php if ($page > 1): ?>
                                    <a href="?page=1<?php echo $filtros_url; ?>" class="page-link">
                                        <i class="fas fa-angle-double-left"></i>
                                    </a>
                                    <a href="?page=<?php echo $page - 1; ?><?php echo $filtros_url; ?>" class="page-link">
                                        <i class="fas fa-angle-left"></i>
                                    </a>
                                <?php endif; ?>

                                <?php
                                $start_page = max(1, $page - 2);
                                $end_page = min($total_pages, $page + 2);

                                for ($i = $start_page; $i <= $end_page; $i++):
                                ?>
                                    <a href="?page=<?php echo $i; ?><?php echo $filtros_url; ?>"
                                       class="page-link <?php echo $i === $page ? 'active' : ''; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endfor; ?>

                                <?php if ($page < $total_pages): ?>
                                    <a href="?page=<?php echo $page + 1; ?><?php echo $filtros_url; ?>" class="page-link">
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                    <a href="?page=<?php echo $total_pages; ?><?php echo $filtros_url; ?>" class="page-link">
                                        <i class="fas fa-angle-double-right"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                <?php else: ?>
                    <div style="padding: 3rem; text-align: center; color: var(--text-light);">
                        <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                        <h4>Nenhuma proposta encontrada</h4>
                        <p>Tente ajustar os filtros para encontrar as propostas desejadas.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
