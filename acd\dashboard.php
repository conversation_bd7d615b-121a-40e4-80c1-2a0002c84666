<?php
session_start();
require_once '../config/database.php';

// Dashboard público - não requer login ou permissões especiais

try {
    // 1. TÍTULO - Informações gerais
    $titulo = "Dashboard - Análise de Crédito e Devoluções";

    // Calcular início e fim da semana atual (segunda a domingo)
    $inicio_semana = date('Y-m-d', strtotime('monday this week'));
    $fim_semana = date('Y-m-d', strtotime('sunday this week'));
    $data_semana = date('d/m', strtotime($inicio_semana)) . ' a ' . date('d/m/Y', strtotime($fim_semana));

    // Função para buscar dados do usuário na API
    function getUsuarioAPI($usuario_id) {
        $apiFields = [
            'api_user' => 'UFL7GXZ14LU9NOR',
            'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
            'api_module' => 'Usuarios',
            'api_action' => 'listarUsuarios'
        ];

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => http_build_query($apiFields),
        ));

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            curl_close($curl);
            return null;
        }

        curl_close($curl);

        $usuarios = json_decode($response, true);

        if (!is_array($usuarios)) {
            return null;
        }

        // Procurar o usuário específico pelo ID
        foreach ($usuarios as $usuario) {
            if (isset($usuario['id']) && $usuario['id'] == $usuario_id) {
                $foto = null;
                if (!empty($usuario['foto'])) {
                    $foto = 'https://intranet.sicoobcredilivre.com.br/sys/conteudo/usuarios/' . $usuario['foto'];
                }

                return [
                    'id' => $usuario['id'] ?? null,
                    'nome' => $usuario['nome'] ?? null,
                    'foto' => $foto,
                    'loginAD' => $usuario['loginAD'] ?? null,
                    'nomeAgencia' => $usuario['nomeAgencia'] ?? null
                ];
            }
        }

        return null;
    }

    // Função para buscar dados do usuário na API por loginAD
    function getUsuarioAPIByLoginAD($loginAD) {
        $apiFields = [
            'api_user' => 'UFL7GXZ14LU9NOR',
            'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
            'api_module' => 'Usuarios',
            'api_action' => 'listarUsuarios'
        ];

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => http_build_query($apiFields),
        ));

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            curl_close($curl);
            return null;
        }

        curl_close($curl);

        $usuarios = json_decode($response, true);

        if (!is_array($usuarios)) {
            return null;
        }

        // Procurar o usuário específico pelo loginAD
        foreach ($usuarios as $usuario) {
            if (isset($usuario['loginAD']) && $usuario['loginAD'] == $loginAD) {
                $foto = null;
                if (!empty($usuario['foto'])) {
                    $foto = 'https://intranet.sicoobcredilivre.com.br/sys/conteudo/usuarios/' . $usuario['foto'];
                }

                return [
                    'id' => $usuario['id'] ?? null,
                    'nome' => $usuario['nome'] ?? null,
                    'foto' => $foto,
                    'loginAD' => $usuario['loginAD'] ?? null,
                    'nomeAgencia' => $usuario['nomeAgencia'] ?? null
                ];
            }
        }

        return null;
    }

    // 2. PROPOSTAS POR PA DA SEMANA COM PERCENTUAL DE DEVOLUÇÃO (INCLUINDO ZERADOS)
    $stmt = $pdo->query("
        SELECT
            pa.nome as pa,
            COALESCE(COUNT(f.id), 0) as total_propostas,
            COALESCE(SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END), 0) as devolvidas,
            CASE
                WHEN COUNT(f.id) > 0 THEN ROUND((SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(f.id)) * 100, 1)
                ELSE 0
            END as taxa_devolucao
        FROM pontos_atendimento pa
        LEFT JOIN acd_formularios f ON pa.nome COLLATE utf8mb4_general_ci = f.pa COLLATE utf8mb4_general_ci
            AND YEARWEEK(f.data_criacao, 1) = YEARWEEK(CURDATE(), 1)
            AND f.pa != '0' AND f.pa IS NOT NULL
        WHERE pa.nome != '0' AND pa.nome IS NOT NULL AND pa.nome != 'UAD'
        GROUP BY pa.nome
        ORDER BY total_propostas DESC, pa.nome ASC
    ");
    $propostas_por_pa = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 3. QUANTIDADE DE PROPOSTAS POR USUÁRIO_PA DA SEMANA (TOP 5)
    $stmt = $pdo->query("
        SELECT
            usuario_pa,
            COUNT(*) as total_propostas
        FROM acd_formularios
        WHERE YEARWEEK(data_criacao, 1) = YEARWEEK(CURDATE(), 1)
        AND pa != '0' AND pa IS NOT NULL
        AND usuario_pa IS NOT NULL AND usuario_pa != ''
        GROUP BY usuario_pa
        ORDER BY total_propostas DESC
        LIMIT 5
    ");
    $propostas_por_usuario = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 4. PROPOSTAS POR PERÍODO COM PERCENTUAL DE DEVOLUÇÃO
    // Semana atual
    $stmt = $pdo->query("
        SELECT
            COUNT(*) as total_propostas,
            SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
            ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as taxa_devolucao
        FROM acd_formularios
        WHERE YEARWEEK(data_criacao, 1) = YEARWEEK(CURDATE(), 1)
        AND pa != '0' AND pa IS NOT NULL
    ");
    $dados_semana = $stmt->fetch(PDO::FETCH_ASSOC);

    // Mês atual
    $stmt = $pdo->query("
        SELECT
            COUNT(*) as total_propostas,
            SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
            ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as taxa_devolucao
        FROM acd_formularios
        WHERE YEAR(data_criacao) = YEAR(CURDATE())
        AND MONTH(data_criacao) = MONTH(CURDATE())
        AND pa != '0' AND pa IS NOT NULL
    ");
    $dados_mes = $stmt->fetch(PDO::FETCH_ASSOC);

    // Ano atual
    $stmt = $pdo->query("
        SELECT
            COUNT(*) as total_propostas,
            SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
            ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as taxa_devolucao
        FROM acd_formularios
        WHERE YEAR(data_criacao) = YEAR(CURDATE())
        AND pa != '0' AND pa IS NOT NULL
    ");
    $dados_ano = $stmt->fetch(PDO::FETCH_ASSOC);


    // 5. USUÁRIOS COM MENOR PERCENTUAL DE DEVOLUÇÃO DA SEMANA (ENTRE QUEM ENVIOU PROPOSTAS)
    $stmt = $pdo->query("
        SELECT
            usuario_pa,
            COUNT(*) as total_propostas,
            SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) as aprovadas,
            SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
            ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as taxa_devolucao
        FROM acd_formularios
        WHERE YEARWEEK(data_criacao, 1) = YEARWEEK(CURDATE(), 1)
        AND pa != '0' AND pa IS NOT NULL
        AND usuario_pa IS NOT NULL AND usuario_pa != ''
        GROUP BY usuario_pa
        HAVING COUNT(*) >= 2
        ORDER BY taxa_devolucao ASC, total_propostas DESC
        LIMIT 5
    ");
    $melhores_usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 6. USUÁRIOS DESTAQUE - PRESENTES EM AMBOS OS RANKINGS
    // Encontrar usuários que estão tanto no top propostas quanto no menor % devolução
    $usuarios_top_propostas = array_column($propostas_por_usuario, 'usuario_pa');
    $usuarios_menor_devolucao = array_column($melhores_usuarios, 'usuario_pa');
    $usuarios_destaque = array_intersect($usuarios_top_propostas, $usuarios_menor_devolucao);

    $top_usuarios_completos = [];
    if (!empty($usuarios_destaque)) {
        // Buscar dados completos dos usuários que estão em ambos os rankings
        foreach ($usuarios_destaque as $usuario_pa) {
            // Buscar dados do ranking de propostas
            $dados_propostas = array_filter($propostas_por_usuario, function($u) use ($usuario_pa) {
                return $u['usuario_pa'] === $usuario_pa;
            });
            $dados_propostas = reset($dados_propostas);

            // Buscar dados do ranking de menor devolução
            $dados_devolucao = array_filter($melhores_usuarios, function($u) use ($usuario_pa) {
                return $u['usuario_pa'] === $usuario_pa;
            });
            $dados_devolucao = reset($dados_devolucao);

            if ($dados_propostas && $dados_devolucao) {
                $top_usuarios_completos[] = [
                    'usuario_pa' => $usuario_pa,
                    'total_propostas' => $dados_propostas['total_propostas'],
                    'taxa_devolucao' => $dados_devolucao['taxa_devolucao'],
                    'aprovadas' => $dados_devolucao['aprovadas'],
                    'devolvidas' => $dados_devolucao['devolvidas'],
                    // Pontuação baseada em estar em ambos os rankings
                    'pontuacao' => round(($dados_propostas['total_propostas'] * 0.3) + ((100 - $dados_devolucao['taxa_devolucao']) * 0.7), 1)
                ];
            }
        }

        // Ordenar por pontuação
        usort($top_usuarios_completos, function($a, $b) {
            return $b['pontuacao'] <=> $a['pontuacao'];
        });

        // Limitar a 3 usuários
        $top_usuarios_completos = array_slice($top_usuarios_completos, 0, 3);
    }

    // 7. USUÁRIO DESTAQUE DA SEMANA (DO BANCO LOCAL)
    $stmt = $pdo->query("
        SELECT
            u.id as usuario_id,
            u.username,
            COUNT(f.id) as total_analisadas,
            SUM(CASE WHEN f.acao = 'submeter' THEN 1 ELSE 0 END) as aprovadas,
            SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
            ROUND((SUM(CASE WHEN f.acao = 'submeter' THEN 1 ELSE 0 END) / COUNT(f.id)) * 100, 1) as taxa_aprovacao
        FROM acd_formularios f
        JOIN usuarios u ON f.usuario_id = u.id
        WHERE YEARWEEK(f.data_criacao, 1) = YEARWEEK(CURDATE(), 1)
        AND f.pa != '0' AND f.pa IS NOT NULL
        GROUP BY f.usuario_id, u.username
        HAVING COUNT(f.id) >= 3
        ORDER BY total_analisadas DESC, taxa_aprovacao DESC
        LIMIT 1
    ");
    $usuario_destaque = $stmt->fetch(PDO::FETCH_ASSOC);

    // Buscar dados da API para o usuário destaque usando username = loginAD
    $dados_usuario_destaque = null;
    if ($usuario_destaque) {
        $dados_usuario_destaque = getUsuarioAPIByLoginAD($usuario_destaque['username']);
    }
    
} catch (Exception $e) {
    die('Erro ao buscar dados: ' . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard ACD</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #003641;
            --secondary-color: #00AE9D;
            --accent-color: #FFB800;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --bg-light: #f8f9fa;
            --border-color: #dee2e6;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: var(--text-dark);
            line-height: 1.6;
            height: 100vh;
            width: 100vw;
            margin: 0;
            padding: 15px;
            overflow: hidden;
        }

        .container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            margin-bottom: 1rem;
            box-shadow: 0 8px 25px rgba(0, 54, 65, 0.3);
            text-align: center;
            flex-shrink: 0;
        }

        .header h1 {
            font-size: 1.8rem;
            margin-bottom: 0.25rem;
            font-weight: 700;
        }

        .header p {
            font-size: 0.9rem;
            opacity: 0.9;
            margin: 0;
        }

        /* Dashboard Grid 3x3 com alturas customizadas */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: 2fr 1fr 2fr;
            gap: 1rem;
            flex: 1;
            height: 100%;
            min-height: 0;
        }

        /* Grid positioning */
        .grid-item-1 { grid-column: 1; grid-row: 1 / 4; }
        .grid-item-2 { grid-column: 2; grid-row: 1; }
        .grid-item-3 { grid-column: 2; grid-row: 2; }
        .grid-item-4 { grid-column: 2; grid-row: 3; }
        .grid-item-5 { grid-column: 3; grid-row: 1; }
        .grid-item-6 { grid-column: 3; grid-row: 2; height: 20%; }
        .grid-item-7 { grid-column: 3; grid-row: 3; height: 40%; }

        /* Cards */
        .card {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            height: 100%;
            min-height: 0;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            margin-bottom: 0.75rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--bg-light);
            flex-shrink: 0;
        }

        .card-title {
            font-size: 0.9rem;
            font-weight: 700;
            color: var(--text-dark);
            text-align: center;
        }

        .card-subtitle {
            font-size: 0.7rem;
            font-weight: 400;
            color: var(--text-light);
            text-align: center;
            margin-top: 0.25rem;
            font-style: italic;
        }

        .card-content {
            flex: 1;
            overflow: hidden;
            min-height: 0;
        }

        /* Specific card styles */
        .card-pa .card-icon { background: var(--primary-color); }
        .card-usuario .card-icon { background: var(--secondary-color); }
        .card-periodo .card-icon { background: var(--info-color); }
        .card-melhores .card-icon { background: var(--success-color); }
        .card-destaque .card-icon { background: var(--accent-color); }

        /* Chart-like lists */
        .chart-list {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
            height: 100%;
            overflow: hidden;
        }

        .chart-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.4rem 0.6rem;
            background: var(--bg-light);
            border-radius: 5px;
            border-left: 3px solid var(--primary-color);
            min-height: 28px;
            flex-shrink: 0;
        }

        .chart-name {
            font-weight: 600;
            color: var(--text-dark);
            flex: 1;
            font-size: 0.7rem;
            line-height: 1.1;
        }

        .chart-values {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.1rem;
            min-width: 60px;
        }

        .chart-value {
            font-weight: 700;
            color: var(--primary-color);
            font-size: 0.75rem;
            text-align: right;
        }

        .chart-percent {
            font-size: 0.65rem;
            padding: 0.1rem 0.3rem;
            border-radius: 8px;
            font-weight: 600;
            text-align: right;
        }

        /* Gráfico de barras para PAs */
        .bar-chart {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
            height: 100%;
            overflow: hidden;
            padding: 0.5rem 0;
        }

        .bar-item {
            display: grid;
            grid-template-columns: 70px 1fr auto;
            align-items: center;
            gap: 0.5rem;
            min-height: 22px;
        }

        .bar-label {
            font-size: 0.65rem;
            font-weight: 600;
            color: var(--text-dark);
            text-align: left;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .bar-container {
            height: 16px;
            background: var(--bg-light);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .bar-fill {
            height: 100%;
            border-radius: 7px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transition: width 0.8s ease;
            position: relative;
            min-width: 20px;
        }

        .bar-count {
            position: absolute;
            left: 4px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.6rem;
            font-weight: 700;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.7);
            z-index: 2;
        }

        .bar-percent {
            font-size: 0.55rem;
            padding: 0.1rem 0.3rem;
            border-radius: 6px;
            font-weight: 600;
            white-space: nowrap;
            min-width: 45px;
            text-align: center;
        }

        /* Animação para texto de alta devolução */
        .bar-percent.percent-high {
            animation: blinkRed 1.5s infinite;
        }

        @keyframes blinkRed {
            0% {
                background: #dc3545;
                color: white;
                transform: scale(1);
            }
            50% {
                background: #ff4757;
                color: white;
                transform: scale(1.05);
                box-shadow: 0 0 8px rgba(220, 53, 69, 0.8);
            }
            100% {
                background: #dc3545;
                color: white;
                transform: scale(1);
            }
        }

        /* Period stats */
        .period-stats {
            display: flex;
            justify-content: space-between;
            gap: 0.4rem;
            height: 100%;
            align-items: center;
        }

        .period-item {
            flex: 1;
            text-align: center;
            padding: 0.5rem 0.3rem;
            background: var(--bg-light);
            border-radius: 6px;
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 80px;
        }

        .period-label {
            font-size: 0.65rem;
            color: var(--text-light);
            margin-bottom: 0.2rem;
            font-weight: 600;
        }

        .period-value {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.2rem;
            line-height: 1;
        }

        .period-percent {
            font-size: 0.6rem;
            padding: 0.15rem 0.3rem;
            border-radius: 8px;
            font-weight: 600;
        }

        .percent-low { background: #d4edda; color: #155724; }
        .percent-medium { background: #fff3cd; color: #856404; }
        .percent-high { background: #f8d7da; color: #721c24; }



        /* Destaque do usuário */
        .user-highlight {
            background: var(--bg-light);
            border: 2px solid var(--primary-color);
            text-align: center;
            padding: 1rem;
            border-radius: 10px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 1rem;
            border: 3px solid white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .user-avatar-placeholder {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0 auto 1rem;
            border: 3px solid white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .user-highlight .user-name {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .user-highlight .user-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
        }

        .user-stat {
            background: white;
            padding: 0.75rem;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .user-stat-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
            line-height: 1;
        }

        .user-stat-label {
            font-size: 0.8rem;
            color: var(--text-light);
            line-height: 1.1;
        }

        /* Responsive */
        @media (max-width: 1366px) {
            .dashboard-grid {
                gap: 0.75rem;
            }

            .card {
                padding: 0.75rem;
            }

            .card-header {
                margin-bottom: 0.5rem;
                padding-bottom: 0.5rem;
            }
        }

        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr 1fr;
                grid-template-rows: auto;
            }

            .grid-item-1 { grid-column: 1; grid-row: 1; }
            .grid-item-2 { grid-column: 2; grid-row: 1; }
            .grid-item-3 { grid-column: 1 / 3; grid-row: 2; }
            .grid-item-4 { grid-column: 1; grid-row: 3; }
            .grid-item-5 { grid-column: 2; grid-row: 3; }
            .grid-item-6 { grid-column: 1 / 3; grid-row: 4; height: auto; }
            .grid-item-7 { grid-column: 1 / 3; grid-row: 5; height: auto; }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
                grid-template-rows: auto;
                gap: 1rem;
                height: auto;
            }

            .grid-item-1,
            .grid-item-2,
            .grid-item-3,
            .grid-item-4,
            .grid-item-5,
            .grid-item-6,
            .grid-item-7 {
                grid-column: 1;
                grid-row: auto;
            }

            .period-stats {
                flex-direction: column;
                gap: 0.75rem;
            }

            .podium {
                flex-direction: column;
                align-items: center;
                gap: 0.75rem;
            }

            .podium-item {
                width: 100%;
                max-width: 200px;
                height: auto !important;
                min-height: 100px;
            }

            .user-highlight .user-stats {
                grid-template-columns: 1fr;
            }

            .chart-list {
                gap: 0.5rem;
            }

            .chart-item {
                padding: 0.5rem;
            }
        }

        /* Scrollbar customization */
        .card-content::-webkit-scrollbar {
            width: 4px;
        }

        .card-content::-webkit-scrollbar-track {
            background: var(--bg-light);
            border-radius: 2px;
        }

        .card-content::-webkit-scrollbar-thumb {
            background: var(--secondary-color);
            border-radius: 2px;
        }

        .card-content::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* Pódio Simples - Usuários Destaque */
        .podium-simple {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            padding: 0.5rem;
        }

        .podium-user {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.5);
            border: 1px solid rgba(0, 174, 157, 0.2);
            transition: all 0.3s ease;
        }

        .podium-user:hover {
            background: rgba(0, 174, 157, 0.1);
            border-color: var(--secondary-color);
            transform: translateY(-1px);
        }

        .podium-position-1 {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
            border-color: #FFD700;
        }

        .podium-position-2 {
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.2), rgba(192, 192, 192, 0.1));
            border-color: #C0C0C0;
        }

        .podium-position-3 {
            background: linear-gradient(135deg, rgba(205, 127, 50, 0.2), rgba(205, 127, 50, 0.1));
            border-color: #CD7F32;
        }

        .user-position {
            font-size: 1rem;
            font-weight: 700;
            color: var(--primary-color);
            min-width: 25px;
            text-align: center;
        }

        .user-photo {
            position: relative;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid var(--secondary-color);
            background: var(--secondary-color);
        }

        .user-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-initials {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--secondary-color);
            color: white;
            font-size: 0.8rem;
            font-weight: 700;
        }

        .user-info {
            flex: 1;
            min-width: 0;
        }

        .user-name {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--text-dark);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-pa {
            font-size: 0.7rem;
            color: var(--text-light);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Destaque da Semana - Layout Compacto */
        .user-highlight-compact {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem;
        }

        .user-avatar-compact {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--secondary-color);
        }

        .user-avatar-placeholder-compact {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--secondary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            font-weight: 700;
            border: 2px solid var(--secondary-color);
        }

        .user-info-compact {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .user-name-compact {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-dark);
        }

        .user-metrics-compact {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.75rem;
            color: var(--text-light);
        }

        .metric-item {
            font-weight: 500;
        }

        .metric-separator {
            color: var(--secondary-color);
            font-weight: 700;
        }

        /* Destaque da Semana - Estatísticas Expandidas */
        .user-stats-expanded {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            padding: 1rem;
        }

        .user-stats-expanded .user-stat {
            text-align: center;
            padding: 0.75rem;
            background: rgba(0, 174, 157, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(0, 174, 157, 0.2);
        }

        .user-stats-expanded .user-stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }

        .user-stats-expanded .user-stat-label {
            font-size: 0.8rem;
            color: var(--text-light);
            font-weight: 500;
        }

        /* Pódio dos PAs */
        .podium-pas {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            padding: 0.5rem;
        }

        .podium-pa {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem;
            border-radius: 6px;
            border: 1px solid rgba(0, 174, 157, 0.2);
            background: rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }

        .podium-pa:hover {
            background: rgba(0, 174, 157, 0.1);
            border-color: var(--secondary-color);
            transform: translateY(-1px);
        }

        .podium-pa-1 {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 215, 0, 0.05));
            border-color: #FFD700;
        }

        .podium-pa-2 {
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.15), rgba(192, 192, 192, 0.05));
            border-color: #C0C0C0;
        }

        .podium-pa-3 {
            background: linear-gradient(135deg, rgba(205, 127, 50, 0.15), rgba(205, 127, 50, 0.05));
            border-color: #CD7F32;
        }

        .pa-position {
            font-size: 0.9rem;
            font-weight: 700;
            color: var(--primary-color);
            min-width: 20px;
            text-align: center;
        }

        .pa-info {
            flex: 1;
            min-width: 0;
        }

        .pa-name {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--text-dark);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 0.1rem;
        }

        .pa-metrics {
            font-size: 0.7rem;
            color: var(--text-light);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 1. TÍTULO -->
        <div class="header">
            <h1><i class="fas fa-chart-line"></i> <?php echo $titulo; ?></h1>
            <p><i class="fas fa-calendar-week"></i> Semana: <?php echo $data_semana; ?></p>
        </div>

        <!-- Dashboard Grid 3x3 -->
        <div class="dashboard-grid">
            <!-- 1ª Coluna: Propostas por PA (TODA COLUNA) -->
            <div class="card card-pa grid-item-1">
                <div class="card-header">
                    <div class="card-title">Propostas por PA</div>
                </div>
                <div class="card-content">
                    <div class="bar-chart">
                        <?php
                        // Encontrar o valor máximo para normalizar as barras
                        if (!empty($propostas_por_pa)) {
    $max_propostas = max(array_column($propostas_por_pa, 'total_propostas'));
    foreach ($propostas_por_pa as $pa):
        // Evita divisão por zero
        $largura_barra = ($max_propostas > 0) ? ($pa['total_propostas'] / $max_propostas) * 100 : 0;
?>
<div class="bar-item">
<div class="bar-label"><?php echo htmlspecialchars($pa['pa']); ?></div>
<div class="bar-container">
<?php if ($pa['total_propostas'] > 0): ?>
<div class="bar-fill" style="width: <?php echo $largura_barra; ?>%;">
<span class="bar-count"><?php echo number_format($pa['total_propostas'], 0, ',', '.'); ?></span>
</div>
<?php else: ?>
<div class="bar-fill" style="width: 100%; background: #e9ecef; border: 1px solid #dee2e6;">
<span class="bar-count" style="color: var(--text-light);">0</span>
</div>
<?php endif; ?>
                                </div>
                                <?php if ($pa['total_propostas'] > 0): ?>
                                    <div class="bar-percent <?php
                                        $taxa = $pa['taxa_devolucao'];
                                        echo $taxa >= 20 ? 'percent-high' : ($taxa >= 10 ? 'percent-medium' : 'percent-low');
                                    ?>"><?php echo $pa['taxa_devolucao']; ?>% devol.</div>
                                <?php else: ?>
                                    <div class="bar-percent" style="color: var(--text-light); font-size: 0.6rem;">Sem propostas</div>
                                <?php endif; ?>
                            </div>
                        <?php
                            endforeach;
                        } else {
                        ?>
                            <div style="text-align: center; color: var(--text-light); padding: 2rem;">
                                <i class="fas fa-info-circle"></i> Nenhuma proposta encontrada para esta semana
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>

            <!-- 2ª Coluna, 1ª Linha: Propostas por Usuário -->
            <div class="card card-usuario grid-item-2">
                <div class="card-header">
                    <div class="card-title">Propostas por Usuário</div>
                </div>
                <div class="card-content">
                    <div class="chart-list">
                        <?php foreach ($propostas_por_usuario as $usuario): ?>
                            <?php
                                // Buscar dados da API para o usuário
                                $dados_usuario = getUsuarioAPI($usuario['usuario_pa']);
                                $nome_exibir = $dados_usuario['nome'] ?? $usuario['usuario_pa'];
                            ?>
                            <div class="chart-item">
                                <div class="chart-name"><?php echo htmlspecialchars($nome_exibir); ?></div>
                                <div class="chart-value"><?php echo number_format($usuario['total_propostas'], 0, ',', '.'); ?></div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- 2ª Coluna, 2ª Linha: Propostas por Período -->
            <div class="card card-periodo grid-item-3">
                <div class="card-header">
                    <div class="card-title">Propostas por Período</div>
                </div>
                <div class="card-content">
                    <div class="period-stats">
                        <div class="period-item">
                            <div class="period-label">Semana</div>
                            <div class="period-value"><?php echo number_format($dados_semana['total_propostas'], 0, ',', '.'); ?></div>
                            <div class="period-percent <?php
                                $taxa = $dados_semana['taxa_devolucao'];
                                echo $taxa >= 20 ? 'percent-high' : ($taxa >= 10 ? 'percent-medium' : 'percent-low');
                            ?>"><?php echo $dados_semana['taxa_devolucao']; ?>% devol.</div>
                        </div>
                        <div class="period-item">
                            <div class="period-label">Mês</div>
                            <div class="period-value"><?php echo number_format($dados_mes['total_propostas'], 0, ',', '.'); ?></div>
                            <div class="period-percent <?php
                                $taxa = $dados_mes['taxa_devolucao'];
                                echo $taxa >= 20 ? 'percent-high' : ($taxa >= 10 ? 'percent-medium' : 'percent-low');
                            ?>"><?php echo $dados_mes['taxa_devolucao']; ?>% devol.</div>
                        </div>
                        <div class="period-item">
                            <div class="period-label">Ano</div>
                            <div class="period-value"><?php echo number_format($dados_ano['total_propostas'], 0, ',', '.'); ?></div>
                            <div class="period-percent <?php
                                $taxa = $dados_ano['taxa_devolucao'];
                                echo $taxa >= 20 ? 'percent-high' : ($taxa >= 10 ? 'percent-medium' : 'percent-low');
                            ?>"><?php echo $dados_ano['taxa_devolucao']; ?>% devol.</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 2ª Coluna, 3ª Linha: Melhores Usuários (menor taxa devolução) -->
            <div class="card card-melhores grid-item-4">
                <div class="card-header">
                    <div class="card-title">Menor Taxa de Devolução</div>
                </div>
                <div class="card-content">
                    <div class="chart-list">
                        <?php foreach ($melhores_usuarios as $usuario): ?>
                            <?php
                                // Buscar dados da API para o usuário
                                $dados_usuario = getUsuarioAPI($usuario['usuario_pa']);
                                $nome_exibir = $dados_usuario['nome'] ?? $usuario['usuario_pa'];
                            ?>
                            <div class="chart-item">
                                <div class="chart-name"><?php echo htmlspecialchars($nome_exibir); ?></div>
                                <div class="chart-value"><?php echo $usuario['taxa_devolucao']; ?>% devol. | <?php echo $usuario['total_propostas']; ?> propostas</div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- 3ª Coluna, 1ª Linha: Usuários Destaque -->
            <div class="card card-destaque grid-item-5">
                <div class="card-header">
                    <div class="card-title">Usuários Destaque</div>
                    <div class="card-subtitle">Presentes em ambos os rankings</div>
                </div>
                <div class="card-content">
                    <?php if (!empty($top_usuarios_completos)): ?>
                        <div class="podium-simple">
                            <?php foreach ($top_usuarios_completos as $index => $usuario): ?>
                                <?php
                                    // Buscar dados da API para o usuário
                                    $dados_usuario = getUsuarioAPI($usuario['usuario_pa']);
                                    $nome_exibir = $dados_usuario['nome'] ?? $usuario['usuario_pa'];
                                    $foto_url = $dados_usuario['foto'] ?? null;
                                    $pa_usuario = $dados_usuario['nomeAgencia'] ?? 'N/A';
                                ?>
                                <div class="podium-user podium-position-<?php echo $index + 1; ?>">
                                    <div class="user-position"><?php echo $index + 1; ?>º</div>
                                    <div class="user-photo">
                                        <?php if ($foto_url): ?>
                                            <img src="<?php echo htmlspecialchars($foto_url); ?>" alt="<?php echo htmlspecialchars($nome_exibir); ?>" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                            <div class="user-initials" style="display: none;">
                                                <?php echo strtoupper(substr($nome_exibir, 0, 2)); ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="user-initials">
                                                <?php echo strtoupper(substr($nome_exibir, 0, 2)); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="user-info">
                                        <div class="user-name"><?php echo htmlspecialchars($nome_exibir); ?></div>
                                        <div class="user-pa"><?php echo htmlspecialchars($pa_usuario); ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p style="text-align: center; color: var(--text-light); padding: 2rem;">
                            <i class="fas fa-info-circle"></i> Nenhum usuário presente em ambos os rankings
                        </p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 3ª Coluna, 2ª Linha: Destaque da Semana - Foto, Nome e Métricas (20%) -->
            <div class="card card-destaque grid-item-6">
                <div class="card-header">
                    <div class="card-title">Analista Destaque</div>
                </div>
                <div class="card-content">
                    <?php if ($usuario_destaque): ?>
                        <div class="user-highlight-compact">
                            <?php if ($dados_usuario_destaque && !empty($dados_usuario_destaque['foto'])): ?>
                                <img src="<?php echo htmlspecialchars($dados_usuario_destaque['foto']); ?>"
                                     alt="Foto de <?php echo htmlspecialchars($dados_usuario_destaque['nome'] ?? $usuario_destaque['username']); ?>"
                                     class="user-avatar-compact"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div class="user-avatar-placeholder-compact" style="display: none;">
                                    <?php echo strtoupper(substr($usuario_destaque['username'], 0, 2)); ?>
                                </div>
                            <?php else: ?>
                                <div class="user-avatar-placeholder-compact">
                                    <?php echo strtoupper(substr($usuario_destaque['username'], 0, 2)); ?>
                                </div>
                            <?php endif; ?>
                            <div class="user-info-compact">
                                <div class="user-name-compact">
                                    <?php echo htmlspecialchars($dados_usuario_destaque['nome'] ?? $usuario_destaque['username']); ?>
                                </div>
                                <div class="user-metrics-compact">
                                    <span class="metric-item"><?php echo $usuario_destaque['total_analisadas']; ?> propostas</span>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <p style="text-align: center; color: var(--text-light); padding: 1rem; font-size: 0.8rem;">
                            <i class="fas fa-info-circle"></i> Sem dados suficientes
                        </p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 3ª Coluna, 3ª Linha: Top 3 PAs da Semana (40%) -->
            <div class="card card-destaque grid-item-7">
                <div class="card-header">
                    <div class="card-title">Top Score</div>
                </div>
                <div class="card-content">
                    <?php
                    // Buscar Top 3 PAs da semana
                    $stmt = $pdo->query("
                        SELECT
                            pa.nome as pa,
                            COUNT(f.id) as total_propostas,
                            SUM(CASE WHEN f.acao = 'submeter' THEN 1 ELSE 0 END) as aprovadas,
                            SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
                            ROUND((SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(f.id)) * 100, 1) as taxa_devolucao,
                            -- Pontuação: 40% propostas + 60% baixa devolução
                            ROUND(
                                (COUNT(f.id) * 0.4) +
                                ((100 - (SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(f.id)) * 100) * 0.6),
                                1
                            ) as pontuacao
                        FROM pontos_atendimento pa
                        LEFT JOIN acd_formularios f ON pa.nome COLLATE utf8mb4_general_ci = f.pa COLLATE utf8mb4_general_ci
                            AND YEARWEEK(f.data_criacao, 1) = YEARWEEK(CURDATE(), 1)
                            AND f.pa != '0' AND f.pa IS NOT NULL
                        WHERE pa.nome != '0' AND pa.nome IS NOT NULL AND pa.nome != 'UAD'
                        GROUP BY pa.nome
                        HAVING COUNT(f.id) >= 3
                        ORDER BY pontuacao DESC, total_propostas DESC
                        LIMIT 3
                    ");
                    $top_pas_semana = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    ?>

                    <?php if (!empty($top_pas_semana)): ?>
                        <div class="podium-pas">
                            <?php foreach ($top_pas_semana as $index => $pa): ?>
                                <div class="podium-pa podium-pa-<?php echo $index + 1; ?>">
                                    <div class="pa-position"><?php echo $index + 1; ?>º</div>
                                    <div class="pa-info">
                                        <div class="pa-name"><?php echo htmlspecialchars($pa['pa']); ?></div>
                                        <div class="pa-metrics">
                                            <?php echo $pa['total_propostas']; ?> propostas • <?php echo $pa['taxa_devolucao']; ?>% devolução
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p style="text-align: center; color: var(--text-light); padding: 2rem;">
                            <i class="fas fa-info-circle"></i> Dados insuficientes para ranking
                        </p>
                    <?php endif; ?>
                </div>
            </div>
        </div>


    <script>
        // Auto-refresh da página a cada 5 minutos
        setTimeout(function() {
            location.reload();
        }, 300000);





        // Adicionar tooltip nos cards do pódio
        document.querySelectorAll('.podium-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05) translateY(-5px)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1) translateY(0)';
            });
        });
    </script>
</body>
</html>
