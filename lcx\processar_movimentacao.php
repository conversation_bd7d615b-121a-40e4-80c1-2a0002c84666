<?php
session_start();
require_once '../config/database.php';
require_once 'functions/logs.php';
require_once 'funcoes_dias_uteis.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user_id = $_SESSION['user_id'];

// Verificar se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: index.php?erro=metodo_invalido');
    exit;
}

// Verificar permissões LCX do usuário
try {
    $stmt = $pdo->prepare("SELECT nivel_permissao FROM lcx_permissoes WHERE usuario_id = ? AND ativo = 1");
    $stmt->execute([$user_id]);
    $permissao_lcx = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$permissao_lcx) {
        header('Location: ../dashboard.php?erro=sem_permissao_lcx');
        exit;
    }
    
    $nivel_permissao = $permissao_lcx['nivel_permissao'];
    
    // Verificar se tem permissão para fazer movimentações
    if (!in_array($nivel_permissao, ['tesoureiro', 'gestor', 'gestor_master', 'admin'])) {
        header('Location: ../dashboard.php?erro=sem_permissao_movimentacao');
        exit;
    }
} catch (Exception $e) {
    error_log("Erro ao verificar permissões: " . $e->getMessage());
    header('Location: ../dashboard.php?erro=erro_sistema');
    exit;
}

// Validar dados obrigatórios
$livro_id = intval($_POST['livro_id'] ?? 0);
$tipo = $_POST['tipo'] ?? '';
$valor = floatval($_POST['valor'] ?? 0);
$descricao = trim($_POST['descricao'] ?? '');
$categoria = trim($_POST['categoria'] ?? '');
$origem = $_POST['origem'] ?? 'caixa';
$equipamento_atm_id = !empty($_POST['equipamento_atm_id']) ? intval($_POST['equipamento_atm_id']) : null;
$data_competencia = $_POST['data_competencia'] ?? '';
// Data/hora de movimentação é sempre o momento atual
$data_movimentacao = date('Y-m-d H:i:s');

if (!$livro_id || !$tipo || !$valor || !$categoria || !$origem || !$data_competencia) {
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=dados_obrigatorios');
    exit;
}

if (!in_array($tipo, ['entrada', 'saida'])) {
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=tipo_invalido');
    exit;
}

if (!in_array($origem, ['caixa', 'atm', 'transportadora'])) {
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=origem_invalida');
    exit;
}

// Validar equipamento ATM quando origem for ATM
if ($origem === 'atm' && !$equipamento_atm_id) {
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=equipamento_atm_obrigatorio');
    exit;
}

if ($valor <= 0) {
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=valor_invalido');
    exit;
}

// Validar categorias específicas por tipo (apenas para livros normais)
if ($livro['tipo'] === 'normal') {
    $categorias_entrada = ['Recolhimento', 'Suprimento Reserva'];
    $categorias_saida = ['Envio de Numerário', 'Envio de Reserva', 'Suprimento'];

    if ($tipo === 'entrada' && !in_array($categoria, $categorias_entrada)) {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=categoria_invalida');
        exit;
    }

    if ($tipo === 'saida' && !in_array($categoria, $categorias_saida)) {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=categoria_invalida');
        exit;
    }
}
// Para livros master, qualquer categoria (PA) é válida

// Validar data de competência
if (!DateTime::createFromFormat('Y-m-d', $data_competencia)) {
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=data_competencia_invalida');
    exit;
}

// Buscar dados do livro caixa
try {
    $stmt = $pdo->prepare("
        SELECT lc.*, pa.nome as pa_nome 
        FROM lcx_livros_caixa lc
        JOIN pontos_atendimento pa ON lc.ponto_atendimento_id = pa.id
        WHERE lc.id = ?
    ");
    $stmt->execute([$livro_id]);
    $livro = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$livro) {
        header('Location: index.php?erro=livro_nao_encontrado');
        exit;
    }
    
    // Verificar se o livro está aberto
    if ($livro['status'] !== 'aberto') {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=livro_fechado');
        exit;
    }

    // NOVA REGRA: Impedir lançamentos em anos diferentes do ano de abertura do livro
    $ano_abertura = date('Y', strtotime($livro['data_abertura'] ?? $livro['created_at']));
    $ano_competencia = date('Y', strtotime($data_competencia));
    if ($ano_abertura != $ano_competencia) {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=ano_invalido');
        exit;
    }

    // Verificar se o usuário é tesoureiro do PA deste livro (SEM EXCEÇÕES - nem admin)
    $stmt = $pdo->prepare("
        SELECT id FROM lcx_tesoureiros_pa
        WHERE usuario_id = ? AND ponto_atendimento_id = ? AND ativo = 1
    ");
    $stmt->execute([$user_id, $livro['ponto_atendimento_id']]);

    if ($stmt->rowCount() === 0) {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=sem_permissao_pa');
        exit;
    }

    // Sistema de validação de data removido - edição sempre habilitada
    // Usuário pode escolher qualquer data de competência
} catch (Exception $e) {
    error_log("Erro ao buscar livro: " . $e->getMessage());
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=erro_sistema');
    exit;
}

// Verificar se o usuário tem acesso a este livro
$tem_acesso = false;
if (in_array($nivel_permissao, ['admin', 'gestor_master'])) {
    $tem_acesso = true;
} else {
    // Verificar se é tesoureiro do PA deste livro
    try {
        $stmt = $pdo->prepare("
            SELECT id FROM lcx_tesoureiros_pa 
            WHERE usuario_id = ? AND ponto_atendimento_id = ? AND ativo = 1
        ");
        $stmt->execute([$user_id, $livro['ponto_atendimento_id']]);
        $tem_acesso = $stmt->rowCount() > 0;
    } catch (Exception $e) {
        error_log("Erro ao verificar acesso ao livro: " . $e->getMessage());
    }
}

if (!$tem_acesso) {
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=sem_acesso_livro');
    exit;
}

// Validar se o dia está aberto para permitir lançamentos
if (!podeRegistrarMovimentacao($livro_id, $pdo)) {
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=dia_nao_aberto');
    exit;
}

// Calcular novo saldo
$saldo_anterior = $livro['saldo_atual'];
$saldo_posterior = $saldo_anterior;

if ($tipo === 'entrada') {
    $saldo_posterior += $valor;
} else {
    $saldo_posterior -= $valor;
    
    // Verificar se há saldo suficiente (permitir saldo negativo com aviso)
    if ($saldo_posterior < 0) {
        // Log do saldo negativo
        error_log("Movimentação resultará em saldo negativo: Livro {$livro_id}, Saldo atual: {$saldo_anterior}, Valor saída: {$valor}, Novo saldo: {$saldo_posterior}");
    }
}

try {
    // Iniciar transação
    $pdo->beginTransaction();
    
    // Registrar a movimentação
    $stmt = $pdo->prepare("
        INSERT INTO lcx_movimentacoes (
            livro_caixa_id, tipo, valor, descricao, data_movimentacao,
            saldo_anterior, saldo_posterior, categoria, origem, data_competencia,
            equipamento_atm_id, criado_por
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    $stmt->execute([
        $livro_id,
        $tipo,
        $valor,
        $descricao,
        $data_movimentacao,
        $saldo_anterior,
        $saldo_posterior,
        $categoria ?: null,
        $origem,
        $data_competencia,
        $equipamento_atm_id,
        $user_id
    ]);
    
    $movimentacao_id = $pdo->lastInsertId();
    
    // Atualizar saldo do livro caixa
    $stmt = $pdo->prepare("
        UPDATE lcx_livros_caixa
        SET saldo_atual = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ");
    $stmt->execute([$saldo_posterior, $livro_id]);

    // Atualizar contador de folhas utilizadas (cada 30 movimentações = 1 folha)
    $stmt = $pdo->prepare("
        UPDATE lcx_livros_caixa
        SET folhas_utilizadas = LEAST(98, CEILING((
            SELECT COUNT(*) FROM lcx_movimentacoes WHERE livro_caixa_id = ?
        ) / 30.0))
        WHERE id = ?
    ");
    $stmt->execute([$livro_id, $livro_id]);
    
    // Confirmar transação
    $pdo->commit();

    // Registrar log estruturado da operação
    log_nova_movimentacao($pdo, $livro_id, $movimentacao_id, $tipo, $valor, $categoria, $descricao);

    // Redirecionar com sucesso
    $mensagem = 'movimentacao_criada';
    if ($saldo_posterior < 0) {
        $mensagem = 'movimentacao_criada_saldo_negativo';
    }
    
    header('Location: visualizar_livro.php?id=' . $livro_id . '&sucesso=' . $mensagem);
    exit;
    
} catch (Exception $e) {
    // Reverter transação em caso de erro
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    error_log("Erro ao processar movimentação: " . $e->getMessage());
    
    // Redirecionar com erro específico
    if (strpos($e->getMessage(), 'foreign key') !== false) {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=livro_nao_encontrado');
    } else {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=erro_movimentacao');
    }
    exit;
}
?>
