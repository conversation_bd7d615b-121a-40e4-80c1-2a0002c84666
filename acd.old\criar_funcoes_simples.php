<?php
/**
 * Script para criar funções simples compatíveis com a estrutura atual
 */

require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

session_start();
if (!isset($_SESSION['user_id'])) {
    die("Acesso negado. Faça login para continuar.");
}

if (!checkACDPermission($_SESSION['user_id'])) {
    die("Acesso negado. Você não tem permissão para acessar o sistema ACD.");
}

echo "<h2>🔧 Criando Funções Compatíveis</h2>";

try {
    // Verificar estrutura atual
    $stmt = $pdo->query("DESCRIBE acd_usuario_pa");
    $colunas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $campos = array_column($colunas, 'Field');
    
    echo "<p>📋 Campos detectados: " . implode(', ', $campos) . "</p>";
    
    // Criar arquivo de funções compatíveis
    $funcoes_content = '<?php
/**
 * Funções compatíveis com a estrutura atual da tabela acd_usuario_pa
 * Gerado automaticamente em ' . date('Y-m-d H:i:s') . '
 */

function buscarVinculosAtivos() {
    global $pdo;
    
    try {';
    
    if (in_array('usuario_api_id', $campos)) {
        // Estrutura nova com API
        $funcoes_content .= '
        $sql = "
            SELECT 
                v.id,
                v.usuario_api_id,
                v.pa_id,
                v.data_inicio,
                p.nome as pa_nome,
                p.numero as pa_numero
            FROM acd_usuario_pa v
            INNER JOIN pontos_atendimento p ON v.pa_id = p.id
            WHERE v.status = \'ativo\'
            AND (v.data_fim IS NULL OR v.data_fim > CURDATE())
            ORDER BY p.nome
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $vinculos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Buscar dados da API para cada usuário
        foreach ($vinculos as &$vinculo) {
            $vinculo[\'usuario_nome\'] = \'Usuário API ID: \' . $vinculo[\'usuario_api_id\'];
            $vinculo[\'usuario_email\'] = \'\';
        }
        
        return $vinculos;';
        
    } elseif (in_array('usuario_id', $campos)) {
        // Estrutura com usuario_id
        $funcoes_content .= '
        $sql = "
            SELECT 
                v.id,
                v.usuario_id,
                v.pa_id,';
        
        if (in_array('data_inicio', $campos)) {
            $funcoes_content .= '
                v.data_inicio,';
        } elseif (in_array('data_vinculo', $campos)) {
            $funcoes_content .= '
                DATE(v.data_vinculo) as data_inicio,';
        } else {
            $funcoes_content .= '
                CURDATE() as data_inicio,';
        }
        
        $funcoes_content .= '
                u.nome_completo as usuario_nome,
                u.email as usuario_email,
                p.nome as pa_nome,
                p.numero as pa_numero
            FROM acd_usuario_pa v
            INNER JOIN usuarios u ON v.usuario_id = u.id
            INNER JOIN pontos_atendimento p ON v.pa_id = p.id
            WHERE ';
        
        if (in_array('status', $campos)) {
            $funcoes_content .= '(v.status = \'ativo\' OR v.status = 1)';
        } else {
            $funcoes_content .= '1=1';
        }
        
        if (in_array('data_fim', $campos)) {
            $funcoes_content .= '
            AND (v.data_fim IS NULL OR v.data_fim > CURDATE())';
        }
        
        $funcoes_content .= '
            ORDER BY p.nome, u.nome_completo
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);';
    }
    
    $funcoes_content .= '
    } catch (Exception $e) {
        error_log("Erro em buscarVinculosAtivos: " . $e->getMessage());
        return [];
    }
}

function buscarPAsSemResponsavel() {
    global $pdo;
    
    try {
        $sql = "
            SELECT
                pa.id,
                pa.numero,
                pa.nome
            FROM pontos_atendimento pa
            LEFT JOIN acd_usuario_pa v ON pa.id = v.pa_id';
    
    if (in_array('status', $campos)) {
        $funcoes_content .= '
            AND (v.status = \'ativo\' OR v.status = 1)';
    }
    
    $funcoes_content .= '
            WHERE v.id IS NULL
            ORDER BY pa.numero
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Erro em buscarPAsSemResponsavel: " . $e->getMessage());
        return [];
    }
}

function criarVinculo($usuario_id, $pa_id, $data_inicio = null, $criado_por = null, $observacoes = null) {
    global $pdo;
    
    try {
        if ($data_inicio === null) {
            $data_inicio = date("Y-m-d");
        }
        
        if ($criado_por === null) {
            $criado_por = $_SESSION["user_id"] ?? 1;
        }';
    
    if (in_array('usuario_api_id', $campos)) {
        // Estrutura nova
        $funcoes_content .= '
        $sql = "
            INSERT INTO acd_usuario_pa (
                usuario_api_id, pa_id, data_inicio, status, criado_por, observacoes
            ) VALUES (?, ?, ?, \'ativo\', ?, ?)
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$usuario_id, $pa_id, $data_inicio, $criado_por, $observacoes]);';
        
    } elseif (in_array('data_inicio', $campos) && in_array('criado_por', $campos)) {
        // Estrutura com histórico
        $funcoes_content .= '
        $sql = "
            INSERT INTO acd_usuario_pa (
                usuario_id, pa_id, data_inicio, status, criado_por, observacoes
            ) VALUES (?, ?, ?, \'ativo\', ?, ?)
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$usuario_id, $pa_id, $data_inicio, $criado_por, $observacoes]);';
        
    } else {
        // Estrutura simples
        $funcoes_content .= '
        $sql = "
            INSERT INTO acd_usuario_pa (
                usuario_id, pa_id';
        
        if (in_array('data_vinculo', $campos)) {
            $funcoes_content .= ', data_vinculo';
        }
        if (in_array('status', $campos)) {
            $funcoes_content .= ', status';
        }
        
        $funcoes_content .= '
            ) VALUES (?, ?';
        
        if (in_array('data_vinculo', $campos)) {
            $funcoes_content .= ', NOW()';
        }
        if (in_array('status', $campos)) {
            $funcoes_content .= ', 1';
        }
        
        $funcoes_content .= ')";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$usuario_id, $pa_id]);';
    }
    
    $funcoes_content .= '
        
        return [
            "sucesso" => true,
            "vinculo_id" => $pdo->lastInsertId(),
            "mensagem" => "Vínculo criado com sucesso"
        ];
        
    } catch (Exception $e) {
        return [
            "sucesso" => false,
            "erro" => $e->getMessage()
        ];
    }
}

function desativarVinculo($vinculo_id, $data_fim = null, $desativado_por = null, $observacoes = null) {
    global $pdo;
    
    try {';
    
    if (in_array('data_fim', $campos) && in_array('desativado_por', $campos)) {
        $funcoes_content .= '
        $sql = "
            UPDATE acd_usuario_pa 
            SET status = \'inativo\',
                data_fim = ?,
                desativado_por = ?,
                observacoes = CONCAT(IFNULL(observacoes, \'\'), \' | \', ?)
            WHERE id = ?
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$data_fim ?? date("Y-m-d"), $desativado_por ?? $_SESSION["user_id"], $observacoes ?? "Desativado", $vinculo_id]);';
    } else {
        $funcoes_content .= '
        $sql = "
            UPDATE acd_usuario_pa 
            SET status = 0
            WHERE id = ?
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$vinculo_id]);';
    }
    
    $funcoes_content .= '
        
        if ($stmt->rowCount() > 0) {
            return [
                "sucesso" => true,
                "mensagem" => "Vínculo desativado com sucesso"
            ];
        } else {
            return [
                "sucesso" => false,
                "erro" => "Vínculo não encontrado"
            ];
        }
        
    } catch (Exception $e) {
        return [
            "sucesso" => false,
            "erro" => $e->getMessage()
        ];
    }
}
?>';
    
    // Salvar arquivo
    $arquivo_funcoes = 'vinculos_functions_compativel.php';
    file_put_contents($arquivo_funcoes, $funcoes_content);
    
    echo "<h3>✅ Funções Criadas</h3>";
    echo "<p>Arquivo criado: <strong>$arquivo_funcoes</strong></p>";
    
    // Testar as funções
    echo "<h3>🧪 Testando Funções</h3>";
    
    include $arquivo_funcoes;
    
    // Teste 1: buscarVinculosAtivos
    echo "<p>🔍 Testando buscarVinculosAtivos()...</p>";
    $vinculos = buscarVinculosAtivos();
    echo "<p>✅ Resultado: " . count($vinculos) . " vínculos encontrados</p>";
    
    // Teste 2: buscarPAsSemResponsavel
    echo "<p>🔍 Testando buscarPAsSemResponsavel()...</p>";
    $pas_sem_responsavel = buscarPAsSemResponsavel();
    echo "<p>✅ Resultado: " . count($pas_sem_responsavel) . " PAs sem responsável</p>";
    
    echo "<h3>🔄 Próximo Passo</h3>";
    echo "<p>Agora você precisa atualizar o arquivo principal para usar as novas funções:</p>";
    
    if (isset($_POST['aplicar_funcoes'])) {
        // Fazer backup do arquivo original
        if (file_exists('vinculos_functions.php')) {
            copy('vinculos_functions.php', 'vinculos_functions_backup_' . date('Y_m_d_H_i_s') . '.php');
            echo "<p>💾 Backup criado do arquivo original</p>";
        }
        
        // Substituir arquivo
        copy($arquivo_funcoes, 'vinculos_functions.php');
        echo "<p>✅ Funções aplicadas com sucesso!</p>";
        echo "<p><a href='gerenciar_vinculos.php'>← Testar Gerenciar Vínculos</a></p>";
    } else {
        echo "<form method='POST'>";
        echo "<button type='submit' name='aplicar_funcoes' class='btn btn-success'>✅ Aplicar Funções</button>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Erro:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "<br><a href='verificar_estrutura_atual.php'>← Voltar para Verificação</a>";
?>

<style>
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    background: #28a745; 
    color: white; 
    text-decoration: none; 
    border-radius: 4px; 
    border: none;
    cursor: pointer;
    margin: 5px;
}
.btn:hover { background: #218838; }
</style>
