<?php
/**
 * Funções para estrutura antiga com histórico
 * REGRA: Apenas UM responsável ativo por PA
 * Gerado automaticamente em 2025-06-06 19:02:44
 */

function buscarVinculosAtivos() {
    global $pdo;
    
    try {
        $sql = "
            SELECT 
                v.id,
                v.usuario_id,
                v.pa_id,
                COALESCE(v.data_inicio, DATE(v.data_vinculo), CURDATE()) as data_inicio,
                v.data_fim,
                u.nome_completo as usuario_nome,
                u.email as usuario_email,
                p.nome as pa_nome,
                p.numero as pa_numero,
                COALESCE(v.observacoes, '') as observacoes
            FROM acd_usuario_pa v
            INNER JOIN usuarios u ON v.usuario_id = u.id
            INNER JOIN pontos_atendimento p ON v.pa_id = p.id
            WHERE v.data_fim IS NULL OR v.data_fim = ''
            ORDER BY p.nome, u.nome_completo
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Erro em buscarVinculosAtivos: " . $e->getMessage());
        return [];
    }
}

function buscarPAsSemResponsavel() {
    global $pdo;
    
    try {
        $sql = "
            SELECT
                pa.id,
                pa.numero,
                pa.nome
            FROM pontos_atendimento pa
            LEFT JOIN acd_usuario_pa v ON (
                pa.id = v.pa_id
                AND (v.data_fim IS NULL OR v.data_fim = '')
            )
            WHERE v.id IS NULL
            ORDER BY pa.numero
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Erro em buscarPAsSemResponsavel: " . $e->getMessage());
        return [];
    }
}

function criarVinculo($usuario_id, $pa_id, $data_inicio = null, $criado_por = null, $observacoes = null) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Verificar se o usuário existe
        $stmt = $pdo->prepare("SELECT id, nome_completo FROM usuarios WHERE id = ?");
        $stmt->execute([$usuario_id]);
        $usuario = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$usuario) {
            $pdo->rollBack();
            return [
                "sucesso" => false,
                "erro" => "Usuário não encontrado (ID: " . $usuario_id . ")"
            ];
        }
        
        // Verificar se o PA existe
        $stmt = $pdo->prepare("SELECT id, nome FROM pontos_atendimento WHERE id = ?");
        $stmt->execute([$pa_id]);
        $pa = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$pa) {
            $pdo->rollBack();
            return [
                "sucesso" => false,
                "erro" => "Ponto de Atendimento não encontrado (ID: " . $pa_id . ")"
            ];
        }
        
        // REGRA: Verificar se já existe responsável ativo para este PA (sem data de fim)
        $stmt = $pdo->prepare("
            SELECT v.id, v.usuario_id, u.nome_completo, v.data_inicio
            FROM acd_usuario_pa v
            INNER JOIN usuarios u ON v.usuario_id = u.id
            WHERE v.pa_id = ?
            AND (v.data_fim IS NULL OR v.data_fim = '')
        ");
        $stmt->execute([$pa_id]);
        $responsavel_atual = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($responsavel_atual) {
            // Se é o mesmo usuário, não fazer nada
            if ($responsavel_atual["usuario_id"] == $usuario_id) {
                $pdo->rollBack();
                return [
                    "sucesso" => false,
                    "erro" => "Este usuário já é responsável por este PA desde " . $responsavel_atual["data_inicio"]
                ];
            }
            
            // Desativar responsável atual (definindo data_fim)
            $data_fim = $data_inicio ?? date("Y-m-d");
            $stmt = $pdo->prepare("
                UPDATE acd_usuario_pa
                SET data_fim = ?,
                    desativado_por = ?,
                    desativado_em = NOW(),
                    observacoes = CONCAT(IFNULL(observacoes, ''), ?, ?)
                WHERE pa_id = ?
                AND (data_fim IS NULL OR data_fim = '')
            ");
            $stmt->execute([
                $data_fim,
                $criado_por ?? $_SESSION["user_id"] ?? 1,
                ' | ',
                'Substituído por ' . $usuario["nome_completo"],
                $pa_id
            ]);
        }
        
        // Criar novo vínculo
        $campos_insert = ["usuario_id", "pa_id"];
        $valores = [$usuario_id, $pa_id];
        $placeholders = ["?", "?"];
        
        if (in_array("data_inicio", ["id","usuario_id","pa_id","data_inicio","data_fim","criado_por","criado_em","desativado_por","desativado_em","observacoes","data_vinculo","status"])) {
            $campos_insert[] = "data_inicio";
            $valores[] = $data_inicio ?? date("Y-m-d");
            $placeholders[] = "?";
        }
        
        if (in_array("data_vinculo", ["id","usuario_id","pa_id","data_inicio","data_fim","criado_por","criado_em","desativado_por","desativado_em","observacoes","data_vinculo","status"])) {
            $campos_insert[] = "data_vinculo";
            $valores[] = date("Y-m-d H:i:s");
            $placeholders[] = "?";
        }
        
        if (in_array("status", ["id","usuario_id","pa_id","data_inicio","data_fim","criado_por","criado_em","desativado_por","desativado_em","observacoes","data_vinculo","status"])) {
            $campos_insert[] = "status";
            $valores[] = "ativo";
            $placeholders[] = "?";
        }
        
        if (in_array("criado_por", ["id","usuario_id","pa_id","data_inicio","data_fim","criado_por","criado_em","desativado_por","desativado_em","observacoes","data_vinculo","status"])) {
            $campos_insert[] = "criado_por";
            $valores[] = $criado_por ?? $_SESSION["user_id"] ?? 1;
            $placeholders[] = "?";
        }
        
        if (in_array("criado_em", ["id","usuario_id","pa_id","data_inicio","data_fim","criado_por","criado_em","desativado_por","desativado_em","observacoes","data_vinculo","status"])) {
            $campos_insert[] = "criado_em";
            $valores[] = date("Y-m-d H:i:s");
            $placeholders[] = "?";
        }
        
        if (in_array("observacoes", ["id","usuario_id","pa_id","data_inicio","data_fim","criado_por","criado_em","desativado_por","desativado_em","observacoes","data_vinculo","status"]) && $observacoes) {
            $campos_insert[] = "observacoes";
            $valores[] = $observacoes;
            $placeholders[] = "?";
        }
        
        $sql = "INSERT INTO acd_usuario_pa (" . implode(", ", $campos_insert) . ") VALUES (" . implode(", ", $placeholders) . ")";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($valores);
        
        $novo_vinculo_id = $pdo->lastInsertId();
        
        $pdo->commit();
        
        $mensagem = "Vínculo criado: " . $usuario["nome_completo"] . " → " . $pa["nome"];
        if ($responsavel_atual) {
            $mensagem .= " (substituindo " . $responsavel_atual["nome_completo"] . ")";
        }
        
        return [
            "sucesso" => true,
            "vinculo_id" => $novo_vinculo_id,
            "mensagem" => $mensagem
        ];
        
    } catch (Exception $e) {
        $pdo->rollBack();
        return [
            "sucesso" => false,
            "erro" => $e->getMessage()
        ];
    }
}

function desativarVinculo($vinculo_id, $data_fim = null, $desativado_por = null, $observacoes = null) {
    global $pdo;

    try {
        // Lógica simplificada: apenas definir data_fim para desativar
        $campos_update = ["data_fim = ?"];
        $valores = [$data_fim ?? date("Y-m-d")];

        // Adicionar observações se fornecidas
        if ($observacoes) {
            $campos_update[] = "observacoes = CONCAT(IFNULL(observacoes, ''), ?, ?)";
            $valores[] = (empty($observacoes) ? '' : ' | ');
            $valores[] = $observacoes;
        }

        // Adicionar campos de auditoria se existirem na tabela
        $campos_update[] = "desativado_por = ?";
        $valores[] = $desativado_por ?? $_SESSION["user_id"] ?? 1;

        $campos_update[] = "desativado_em = NOW()";

        $valores[] = $vinculo_id;

        $sql = "UPDATE acd_usuario_pa SET " . implode(", ", $campos_update) . " WHERE id = ?";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($valores);

        if ($stmt->rowCount() > 0) {
            return [
                "sucesso" => true,
                "mensagem" => "Vínculo desativado com sucesso (data de fim definida)"
            ];
        } else {
            return [
                "sucesso" => false,
                "erro" => "Vínculo não encontrado ou já inativo"
            ];
        }

    } catch (Exception $e) {
        return [
            "sucesso" => false,
            "erro" => $e->getMessage()
        ];
    }
}

function verificarResponsavelAtivo($pa_id) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT v.id, u.nome_completo, v.data_inicio
            FROM acd_usuario_pa v
            INNER JOIN usuarios u ON v.usuario_id = u.id
            WHERE v.pa_id = ?
            AND (v.data_fim IS NULL OR v.data_fim = '')
        ");
        $stmt->execute([$pa_id]);

        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return null;
    }
}

function buscarHistoricoPA($pa_id) {
    global $pdo;
    
    try {
        $sql = "
            SELECT 
                v.*,
                u.nome_completo as usuario_nome,
                uc.nome_completo as criado_por_nome,
                ud.nome_completo as desativado_por_nome
            FROM acd_usuario_pa v
            INNER JOIN usuarios u ON v.usuario_id = u.id
            LEFT JOIN usuarios uc ON v.criado_por = uc.id
            LEFT JOIN usuarios ud ON v.desativado_por = ud.id
            WHERE v.pa_id = ?
            ORDER BY COALESCE(v.data_inicio, v.data_vinculo) DESC, v.criado_em DESC
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$pa_id]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Erro em buscarHistoricoPA: " . $e->getMessage());
        return [];
    }
}
?>