<?php
/**
 * Script para verificar se a reversão foi bem-sucedida
 */

require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

session_start();
if (!isset($_SESSION['user_id'])) {
    die("Acesso negado. Faça login para continuar.");
}

if (!checkACDPermission($_SESSION['user_id'])) {
    die("Acesso negado. Você não tem permissão para acessar o sistema ACD.");
}

echo "<h2>🔍 Verificação da Reversão</h2>";

try {
    // Verificar se a tabela existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'acd_usuario_pa'");
    if ($stmt->rowCount() == 0) {
        echo "<p>❌ Tabela acd_usuario_pa não existe!</p>";
        echo "<p>💡 A tabela foi removida durante a reversão. Vamos recriá-la:</p>";
        
        if (isset($_POST['recriar_tabela'])) {
            $sql_criar = "
            CREATE TABLE acd_usuario_pa (
                id INT AUTO_INCREMENT PRIMARY KEY,
                usuario_id INT NOT NULL COMMENT 'ID do usuário local',
                pa_id INT NOT NULL COMMENT 'ID do PA',
                data_vinculo DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '1=ativo, 0=inativo',
                
                INDEX idx_usuario_id (usuario_id),
                INDEX idx_pa_id (pa_id),
                INDEX idx_status (status),
                
                FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
                FOREIGN KEY (pa_id) REFERENCES pontos_atendimento(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            COMMENT='Vínculos entre usuários locais e PAs - Estrutura Antiga'";
            
            $pdo->exec($sql_criar);
            echo "<p>✅ Tabela recriada com estrutura antiga!</p>";
        } else {
            echo "<form method='POST'>";
            echo "<button type='submit' name='recriar_tabela' class='btn btn-primary'>🔨 Recriar Tabela</button>";
            echo "</form>";
        }
        exit;
    }
    
    echo "<p>✅ Tabela acd_usuario_pa existe</p>";
    
    // Verificar estrutura atual
    echo "<h3>📋 Estrutura Atual</h3>";
    $stmt = $pdo->query("DESCRIBE acd_usuario_pa");
    $colunas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $campos = array_column($colunas, 'Field');
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    foreach ($colunas as $coluna) {
        echo "<tr>";
        echo "<td><strong>{$coluna['Field']}</strong></td>";
        echo "<td>{$coluna['Type']}</td>";
        echo "<td>{$coluna['Null']}</td>";
        echo "<td>{$coluna['Key']}</td>";
        echo "<td>{$coluna['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Verificar se tem estrutura antiga
    $tem_usuario_id = in_array('usuario_id', $campos);
    $tem_pa_id = in_array('pa_id', $campos);
    $tem_data_vinculo = in_array('data_vinculo', $campos);
    $tem_status = in_array('status', $campos);
    $tem_usuario_api_id = in_array('usuario_api_id', $campos);
    
    echo "<h3>🔍 Análise da Estrutura</h3>";
    
    if ($tem_usuario_id && $tem_pa_id && !$tem_usuario_api_id) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>✅ Reversão Bem-Sucedida!</h4>";
        echo "<p>A tabela está com a estrutura antiga correta:</p>";
        echo "<ul>";
        echo "<li>✅ usuario_id (referencia usuários locais)</li>";
        echo "<li>✅ pa_id (referencia PAs locais)</li>";
        if ($tem_data_vinculo) echo "<li>✅ data_vinculo</li>";
        if ($tem_status) echo "<li>✅ status</li>";
        echo "<li>❌ usuario_api_id (removido corretamente)</li>";
        echo "</ul>";
        echo "</div>";
        
        // Verificar dados
        echo "<h3>📊 Dados na Tabela</h3>";
        $stmt = $pdo->query("SELECT COUNT(*) FROM acd_usuario_pa");
        $total = $stmt->fetchColumn();
        echo "<p>Total de registros: <strong>$total</strong></p>";
        
        // Verificar usuários disponíveis
        echo "<h3>👥 Usuários Locais Disponíveis</h3>";
        $stmt = $pdo->query("SELECT COUNT(*) FROM usuarios");
        $total_usuarios = $stmt->fetchColumn();
        echo "<p>Total de usuários locais: <strong>$total_usuarios</strong></p>";
        
        if ($total_usuarios > 0) {
            $stmt = $pdo->query("SELECT id, nome_completo, username FROM usuarios LIMIT 5");
            $usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p><strong>Exemplos de usuários:</strong></p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'><th>ID</th><th>Nome</th><th>Username</th></tr>";
            foreach ($usuarios as $usuario) {
                echo "<tr>";
                echo "<td>{$usuario['id']}</td>";
                echo "<td>{$usuario['nome_completo']}</td>";
                echo "<td>{$usuario['username']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Verificar PAs disponíveis
        echo "<h3>🏢 PAs Disponíveis</h3>";
        $stmt = $pdo->query("SELECT COUNT(*) FROM pontos_atendimento");
        $total_pas = $stmt->fetchColumn();
        echo "<p>Total de PAs: <strong>$total_pas</strong></p>";
        
        echo "<h3>🔧 Próximos Passos</h3>";
        echo "<ol>";
        echo "<li><a href='aplicar_correcao_estrutura_antiga.php' class='btn btn-primary'>🔧 Aplicar Funções para Estrutura Antiga</a></li>";
        echo "<li><a href='gerenciar_vinculos.php' class='btn btn-success'>🔗 Testar Gerenciar Vínculos</a></li>";
        echo "</ol>";
        
    } elseif ($tem_usuario_api_id) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>❌ Reversão Incompleta</h4>";
        echo "<p>A tabela ainda tem estrutura nova (usuario_api_id presente).</p>";
        echo "<p><a href='reverter_para_estrutura_antiga.php' class='btn btn-warning'>🔄 Tentar Reversão Novamente</a></p>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>⚠️ Estrutura Incompleta</h4>";
        echo "<p>A estrutura não está completa. Campos faltando:</p>";
        echo "<ul>";
        if (!$tem_usuario_id) echo "<li>❌ usuario_id</li>";
        if (!$tem_pa_id) echo "<li>❌ pa_id</li>";
        if (!$tem_data_vinculo) echo "<li>❌ data_vinculo</li>";
        if (!$tem_status) echo "<li>❌ status</li>";
        echo "</ul>";
        echo "<p><a href='reverter_para_estrutura_antiga.php' class='btn btn-warning'>🔄 Corrigir Estrutura</a></p>";
        echo "</div>";
    }
    
    // Verificar backups
    echo "<h3>💾 Backups Disponíveis</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'acd_usuario_pa_backup_%'");
    $backups = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!empty($backups)) {
        echo "<p>Backups encontrados:</p>";
        echo "<ul>";
        foreach ($backups as $backup) {
            $stmt = $pdo->query("SELECT COUNT(*) FROM `$backup`");
            $registros = $stmt->fetchColumn();
            echo "<li><strong>$backup</strong> ($registros registros)</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>Nenhum backup encontrado.</p>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Erro:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "<br><a href='gerenciar_vinculos.php'>← Voltar para Gerenciar Vínculos</a>";
?>

<style>
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    color: white; 
    text-decoration: none; 
    border-radius: 4px; 
    border: none;
    cursor: pointer;
    margin: 5px;
}
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-warning { background: #ffc107; color: #212529; }
.btn:hover { opacity: 0.8; }
table { font-size: 12px; }
th, td { padding: 8px; text-align: left; }
</style>
