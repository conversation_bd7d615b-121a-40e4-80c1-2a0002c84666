<?php
session_start();
require_once '../config/database.php';
require_once 'check_acd_permission.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se o usuário tem permissão para acessar a pasta ACD
if (!checkACDPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Verificar se o usuário é administrador ou gestor
if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Função para buscar usuários da API (incluindo inativos)
function buscarTodosUsuarios() {
    $apiFields = [
        'api_user' => 'UFL7GXZ14LU9NOR',
        'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
        'api_module' => 'Usuarios',
        'api_action' => 'listarUsuarios'
    ];

    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => http_build_query($apiFields),
    ));

    $response = curl_exec($curl);

    if (curl_errno($curl)) {
        curl_close($curl);
        return [];
    }

    curl_close($curl);

    $usuarios = json_decode($response, true);

    if (!is_array($usuarios)) {
        return [];
    }

    $usuarios_mapeados = [];
    foreach ($usuarios as $user) {
        // Incluir TODOS os usuários (ativos e inativos)
        $is_ativo = (isset($user['status']) && $user['status'] == 1 && 
                     isset($user['bloqueado']) && $user['bloqueado'] == 0);
        
        $usuarios_mapeados[$user['id']] = [
            'id' => $user['id'],
            'nome' => $user['nome'] ?? '',
            'pa' => $user['nomeAgencia'] ?? '',
            'login' => $user['loginAD'] ?? $user['loginSISBR'] ?? '',
            'ativo' => $is_ativo
        ];
    }

    return $usuarios_mapeados;
}

// Processar filtros
$filtro_periodo = $_GET['periodo'] ?? '';
$filtro_data_inicio = $_GET['data_inicio'] ?? '';
$filtro_data_fim = $_GET['data_fim'] ?? '';
$filtro_pa = $_GET['pa'] ?? '';
$filtro_usuario = $_GET['usuario_id'] ?? '';
$filtro_usuario_pa = $_GET['usuario_pa'] ?? '';
$filtro_acao = $_GET['acao'] ?? '';

try {
    // Buscar usuários da API
    $usuarios_api = buscarTodosUsuarios();

    // Construir WHERE clause baseado nos filtros
    $where_conditions = [];
    $params = [];

    if (!empty($filtro_periodo)) {
        switch ($filtro_periodo) {
            case 'hoje':
                $where_conditions[] = "DATE(f.data_criacao) = CURDATE()";
                break;
            case 'semana':
                $where_conditions[] = "YEARWEEK(f.data_criacao, 1) = YEARWEEK(CURDATE(), 1)";
                break;
            case 'mes':
                $where_conditions[] = "YEAR(f.data_criacao) = YEAR(CURDATE()) AND MONTH(f.data_criacao) = MONTH(CURDATE())";
                break;
            case 'ano':
                $where_conditions[] = "YEAR(f.data_criacao) = YEAR(CURDATE())";
                break;
            case 'personalizado':
                if (!empty($filtro_data_inicio) && !empty($filtro_data_fim)) {
                    $where_conditions[] = "DATE(f.data_criacao) BETWEEN ? AND ?";
                    $params[] = $filtro_data_inicio;
                    $params[] = $filtro_data_fim;
                }
                break;
        }
    }

    if (!empty($filtro_pa)) {
        $where_conditions[] = "f.pa = ?";
        $params[] = $filtro_pa;
    }

    if (!empty($filtro_usuario)) {
        $where_conditions[] = "f.usuario_id = ?";
        $params[] = $filtro_usuario;
    }

    if (!empty($filtro_usuario_pa)) {
        $where_conditions[] = "f.usuario_pa = ?";
        $params[] = $filtro_usuario_pa;
    }

    if (!empty($filtro_acao)) {
        $where_conditions[] = "f.acao = ?";
        $params[] = $filtro_acao;
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // Buscar todas as propostas (sem paginação para PDF)
    $sql = "
        SELECT
            f.id,
            f.pa,
            f.documento,
            f.nome,
            f.mesa,
            f.usuario_id,
            f.usuario_pa,
            f.acao,
            f.motivo_devolucao,
            f.data_criacao,
            u.nome_completo as usuario_nome
        FROM acd_formularios f
        LEFT JOIN usuarios u ON f.usuario_id = u.id
        $where_clause
        ORDER BY f.data_criacao DESC
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $propostas = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Determinar descrição do período
    $periodo_descricao = '';
    switch ($filtro_periodo) {
        case 'hoje':
            $periodo_descricao = 'Hoje (' . date('d/m/Y') . ')';
            break;
        case 'semana':
            $periodo_descricao = 'Esta semana';
            break;
        case 'mes':
            $periodo_descricao = 'Este mês (' . date('m/Y') . ')';
            break;
        case 'ano':
            $periodo_descricao = 'Este ano (' . date('Y') . ')';
            break;
        case 'personalizado':
            if (!empty($filtro_data_inicio) && !empty($filtro_data_fim)) {
                $periodo_descricao = 'De ' . date('d/m/Y', strtotime($filtro_data_inicio)) . ' até ' . date('d/m/Y', strtotime($filtro_data_fim));
            }
            break;
        default:
            $periodo_descricao = 'Todos os períodos';
    }

} catch (Exception $e) {
    error_log("Erro ao buscar relatórios para PDF: " . $e->getMessage());
    $propostas = [];
    $usuarios_api = [];
}

// Configurar cabeçalhos para PDF
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Propostas ACD</title>
    <style>
        @page {
            margin: 15mm;
            size: A4 landscape;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 10px;
            line-height: 1.3;
            margin: 0;
            padding: 0;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #003641;
            padding-bottom: 10px;
        }
        
        .header h1 {
            color: #003641;
            font-size: 18px;
            margin: 0 0 5px 0;
        }
        
        .header .subtitle {
            color: #666;
            font-size: 12px;
            margin: 0;
        }
        
        .filters-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
        }
        
        .filters-info h3 {
            margin: 0 0 8px 0;
            color: #003641;
            font-size: 12px;
        }
        
        .filter-item {
            display: inline-block;
            margin-right: 15px;
            margin-bottom: 5px;
        }
        
        .filter-label {
            font-weight: bold;
            color: #333;
        }
        
        .propostas-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 9px;
        }
        
        .propostas-table th {
            background: #003641;
            color: white;
            padding: 8px 4px;
            text-align: left;
            border: 1px solid #ddd;
            font-weight: bold;
        }
        
        .propostas-table td {
            padding: 6px 4px;
            border: 1px solid #ddd;
            vertical-align: top;
        }
        
        .propostas-table tbody tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .status-submeter {
            background: #d4edda;
            color: #155724;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8px;
            font-weight: bold;
        }
        
        .status-devolver {
            background: #f8d7da;
            color: #721c24;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 8px;
            font-weight: bold;
        }
        
        .usuario-inativo {
            color: #dc3545;
            font-weight: bold;
        }
        
        .motivo-devolucao {
            max-width: 150px;
            word-wrap: break-word;
            font-size: 8px;
        }
        
        .summary {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin-top: 15px;
        }
        
        .summary h3 {
            margin: 0 0 8px 0;
            color: #003641;
            font-size: 12px;
        }
        
        .info-row {
            margin-bottom: 3px;
        }
        
        .info-row strong {
            color: #333;
        }
        
        .text-muted {
            color: #6c757d;
        }
        
        @media print {
            body { print-color-adjust: exact; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Relatório de Propostas ACD</h1>
        <p class="subtitle">Sistema de Análise de Crédito e Documentação</p>
    </div>

    <div class="filters-info">
        <h3>🔍 Filtros Aplicados</h3>
        <div class="filter-item">
            <span class="filter-label">Período:</span> <?php echo htmlspecialchars($periodo_descricao); ?>
        </div>
        <?php if (!empty($filtro_pa)): ?>
            <div class="filter-item">
                <span class="filter-label">PA:</span> <?php echo htmlspecialchars($filtro_pa); ?>
            </div>
        <?php endif; ?>
        <?php if (!empty($filtro_acao)): ?>
            <div class="filter-item">
                <span class="filter-label">Ação:</span> <?php echo $filtro_acao === 'submeter' ? 'Submeter' : 'Devolver'; ?>
            </div>
        <?php endif; ?>
        <div class="filter-item">
            <span class="filter-label">Total de registros:</span> <?php echo number_format(count($propostas), 0, ',', '.'); ?>
        </div>
        <div class="filter-item">
            <span class="filter-label">Data de geração:</span> <?php echo date('d/m/Y H:i:s'); ?>
        </div>
    </div>

    <?php if (!empty($propostas)): ?>
        <table class="propostas-table">
            <thead>
                <tr>
                    <th style="width: 8%;">Data/Hora</th>
                    <th style="width: 8%;">PA</th>
                    <th style="width: 10%;">Documento</th>
                    <th style="width: 15%;">Nome</th>
                    <th style="width: 6%;">Mesa</th>
                    <th style="width: 12%;">Usuário (Análise)</th>
                    <th style="width: 12%;">Usuário do PA</th>
                    <th style="width: 7%;">Ação</th>
                    <th style="width: 22%;">Motivo</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($propostas as $proposta): ?>
                    <?php
                    // Usuário do PA (usuario_pa da API)
                    $usuario_pa_info = isset($usuarios_api[$proposta['usuario_pa']]) ? $usuarios_api[$proposta['usuario_pa']] : null;
                    if ($usuario_pa_info) {
                        $nome_usuario_pa = $usuario_pa_info['nome'];
                        $classe_usuario_pa = $usuario_pa_info['ativo'] ? '' : 'usuario-inativo';
                        if (!$usuario_pa_info['ativo']) {
                            $nome_usuario_pa .= ' (INATIVO)';
                        }
                    } else {
                        $nome_usuario_pa = $proposta['usuario_pa'] ? "ID: " . $proposta['usuario_pa'] : 'N/A';
                        $classe_usuario_pa = '';
                    }
                    ?>
                    <tr>
                        <td>
                            <?php
                            $data = new DateTime($proposta['data_criacao']);
                            echo $data->format('d/m/Y H:i');
                            ?>
                        </td>
                        <td><?php echo htmlspecialchars($proposta['pa']); ?></td>
                        <td><?php echo htmlspecialchars($proposta['documento']); ?></td>
                        <td><?php echo htmlspecialchars($proposta['nome']); ?></td>
                        <td><?php echo htmlspecialchars(ucfirst($proposta['mesa'])); ?></td>
                        <td><?php echo htmlspecialchars($proposta['usuario_nome'] ?? 'ID: ' . $proposta['usuario_id']); ?></td>
                        <td class="<?php echo $classe_usuario_pa; ?>">
                            <?php echo htmlspecialchars($nome_usuario_pa); ?>
                        </td>
                        <td>
                            <span class="status-<?php echo $proposta['acao']; ?>">
                                <?php echo $proposta['acao'] === 'submeter' ? 'SUBMETER' : 'DEVOLVER'; ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($proposta['acao'] === 'devolver' && !empty($proposta['motivo_devolucao'])): ?>
                                <span class="motivo-devolucao"><?php echo htmlspecialchars($proposta['motivo_devolucao']); ?></span>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <div style="text-align: center; padding: 40px; color: #666;">
            <h3>Nenhuma proposta encontrada</h3>
            <p>Não foram encontradas propostas com os filtros aplicados.</p>
        </div>
    <?php endif; ?>

    <div class="summary">
        <h3>📈 Resumo</h3>
        <div class="info-row">
            <strong>Total de Propostas:</strong> <?php echo number_format(count($propostas), 0, ',', '.'); ?>
        </div>
        <?php
        $total_submeter = array_filter($propostas, function($p) { return $p['acao'] === 'submeter'; });
        $total_devolver = array_filter($propostas, function($p) { return $p['acao'] === 'devolver'; });
        ?>
        <div class="info-row">
            <strong>Submissões:</strong> <?php echo number_format(count($total_submeter), 0, ',', '.'); ?>
        </div>
        <div class="info-row">
            <strong>Devoluções:</strong> <?php echo number_format(count($total_devolver), 0, ',', '.'); ?>
        </div>
        <?php if (count($propostas) > 0): ?>
            <div class="info-row">
                <strong>Taxa de Devolução:</strong> <?php echo number_format((count($total_devolver) / count($propostas)) * 100, 1); ?>%
            </div>
        <?php endif; ?>
    </div>

    <script>
        // Auto-imprimir quando a página carregar
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
