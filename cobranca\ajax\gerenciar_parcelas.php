<?php
// Desabilitar a exibição de erros
error_reporting(0);
ini_set('display_errors', 0);

// Garantir que nada seja enviado antes do JSON
ob_start();

try {
    require_once '../../auth_check.php';
    require_once '../../config/database.php';

    header('Content-Type: application/json');

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido');
    }

    $acao = $_POST['acao'] ?? '';

    switch ($acao) {
        case 'personalizar_parcelas':
            $acordo_id = filter_input(INPUT_POST, 'acordo_id', FILTER_VALIDATE_INT);
            
            if (!$acordo_id) {
                throw new Exception('ID do acordo não especificado');
            }
            
            // Verificar se o acordo existe e está ativo com personalização
            $stmt = $pdo->prepare("SELECT * FROM cbp_acordos WHERE id = ? AND ativo = 1 AND personalizar_parcelas = 1");
            $stmt->execute([$acordo_id]);
            $acordo = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$acordo) {
                throw new Exception('Acordo não encontrado ou não está configurado para personalização');
            }
            
            // Obter tipo de personalização
            $tipo_personalizacao = $acordo['tipo_personalizacao'];
            
            // Obter parcelas enviadas
            $parcelas_json = $_POST['parcelas'] ?? '';
            
            if (empty($parcelas_json)) {
                throw new Exception('Nenhuma parcela foi enviada');
            }
            
            $parcelas = json_decode($parcelas_json, true);
            
            if (json_last_error() !== JSON_ERROR_NONE || !is_array($parcelas)) {
                throw new Exception('Formato inválido de parcelas');
            }
            
            // Iniciar transação
            $pdo->beginTransaction();
            
            try {
                foreach ($parcelas as $parcela) {
                    $parcela_id = isset($parcela['id']) ? intval($parcela['id']) : 0;
                    
                    if (!$parcela_id) {
                        continue;
                    }
                    
                    // Verificar se a parcela pertence ao acordo
                    $stmt = $pdo->prepare("SELECT * FROM cbp_parcelas_acordo WHERE id = ? AND acordo_id = ?");
                    $stmt->execute([$parcela_id, $acordo_id]);
                    $parcela_atual = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if (!$parcela_atual) {
                        throw new Exception("Parcela ID {$parcela_id} não pertence ao acordo especificado");
                    }
                    
                    // Se a parcela já estiver paga, não permitir alteração
                    if ($parcela_atual['status'] !== 'PENDENTE') {
                        continue;
                    }
                    
                    // Preparar dados para atualização
                    $campos = [];
                    $valores = [];
                    
                    // Atualizar valor da parcela
                    if (($tipo_personalizacao === 'valores' || $tipo_personalizacao === 'ambos') && isset($parcela['valor'])) {
                        $campos[] = "valor_parcela = ?";
                        $valores[] = floatval($parcela['valor']);
                    }
                    
                    // Atualizar data de vencimento
                    if (($tipo_personalizacao === 'datas' || $tipo_personalizacao === 'ambos') && isset($parcela['data'])) {
                        $campos[] = "data_vencimento = ?";
                        $valores[] = $parcela['data'];
                    }
                    
                    // Se houver campos para atualizar
                    if (!empty($campos)) {
                        $valores[] = $parcela_id;
                        
                        $sql = "UPDATE cbp_parcelas_acordo SET " . implode(", ", $campos) . ", updated_at = NOW() WHERE id = ?";
                        
                        $stmt = $pdo->prepare($sql);
                        $stmt->execute($valores);
                    }
                }
                
                // Commit
                $pdo->commit();
                
                // Retornar sucesso
                echo json_encode([
                    'success' => true,
                    'message' => 'Parcelas personalizadas com sucesso!',
                    'reload' => true
                ]);
            } catch (Exception $e) {
                $pdo->rollBack();
                throw $e;
            }
            break;
            
        default:
            throw new Exception('Ação inválida');
    }
} catch (Exception $e) {
    ob_clean();
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    exit;
} 