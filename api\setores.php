<?php
require_once '../auth_check.php';
require_once '../config/database.php';

header('Content-Type: application/json');

try {
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            if (isset($_GET['id'])) {
                $stmt = $pdo->prepare("SELECT * FROM setores WHERE id = ?");
                $stmt->execute([$_GET['id']]);
                echo json_encode($stmt->fetch());
            }
            break;

        case 'POST':
            $id = $_POST['id'] ?? null;
            $nome = $_POST['nome'];
            $descricao = $_POST['descricao'];

            if ($id) {
                $stmt = $pdo->prepare("UPDATE setores SET nome = ?, descricao = ? WHERE id = ?");
                $stmt->execute([$nome, $descricao, $id]);
            } else {
                $stmt = $pdo->prepare("INSERT INTO setores (nome, descricao) VALUES (?, ?)");
                $stmt->execute([$nome, $descricao]);
            }
            echo json_encode(['success' => true]);
            break;

        case 'DELETE':
            $id = $_GET['id'];
            $stmt = $pdo->prepare("UPDATE setores SET ativo = 0 WHERE id = ?");
            $stmt->execute([$id]);
            echo json_encode(['success' => true]);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
} 