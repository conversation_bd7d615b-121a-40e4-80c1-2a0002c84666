<?php
/**
 * Script para aplicar correção para estrutura nova
 * Corrige as funções para trabalhar com usuario_api_id
 */

require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

session_start();
if (!isset($_SESSION['user_id'])) {
    die("Acesso negado. Faça login para continuar.");
}

if (!checkACDPermission($_SESSION['user_id'])) {
    die("Acesso negado. Você não tem permissão para acessar o sistema ACD.");
}

if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    die("Acesso negado. Apenas administradores e gestores podem executar esta correção.");
}

// Função para buscar usuários da API
function buscarTodosUsuarios() {
    $apiFields = [
        'api_user' => 'UFL7GXZ14LU9NOR',
        'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
        'api_module' => 'Usuarios',
        'api_action' => 'listarUsuarios'
    ];

    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => http_build_query($apiFields),
    ));
    
    $response = curl_exec($curl);
    curl_close($curl);
    
    if (!$response) return [];
    
    $usuarios = json_decode($response, true);
    return is_array($usuarios) ? $usuarios : [];
}

echo "<h2>🔧 Correção para Estrutura Nova (API)</h2>";
echo "<p>Este script corrige as funções para trabalhar com a estrutura nova onde usuario_api_id referencia usuários da API.</p>";

try {
    echo "<h3>📋 Verificando Estrutura</h3>";
    
    // Verificar estrutura da tabela
    $stmt = $pdo->query("DESCRIBE acd_usuario_pa");
    $colunas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $campos = array_column($colunas, 'Field');
    
    echo "<p>Campos encontrados: " . implode(', ', $campos) . "</p>";
    
    $tem_usuario_api_id = in_array('usuario_api_id', $campos);
    $tem_pa_id = in_array('pa_id', $campos);
    $tem_data_inicio = in_array('data_inicio', $campos);
    $tem_status = in_array('status', $campos);
    
    if (!$tem_usuario_api_id || !$tem_pa_id) {
        echo "<p>❌ Estrutura não compatível. Campos usuario_api_id e pa_id são obrigatórios.</p>";
        exit;
    }
    
    echo "<p>✅ Estrutura nova confirmada (com usuario_api_id)</p>";
    
    // Verificar dados existentes
    echo "<h3>📊 Verificando Dados</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM acd_usuario_pa");
    $total_vinculos = $stmt->fetchColumn();
    echo "<p>Total de vínculos: $total_vinculos</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM pontos_atendimento");
    $total_pas = $stmt->fetchColumn();
    echo "<p>Total de PAs: $total_pas</p>";
    
    // Testar API
    echo "<h3>🌐 Testando API</h3>";
    $usuarios_api = buscarTodosUsuarios();
    echo "<p>Usuários encontrados na API: " . count($usuarios_api) . "</p>";
    
    if (count($usuarios_api) == 0) {
        echo "<p>⚠️ Nenhum usuário encontrado na API. Verifique a conexão.</p>";
    }
    
    echo "<h3>🔄 Aplicando Correção</h3>";
    
    if (isset($_POST['aplicar_correcao'])) {
        // Fazer backup do arquivo original
        if (file_exists('vinculos_functions.php')) {
            $backup_name = 'vinculos_functions_backup_' . date('Y_m_d_H_i_s') . '.php';
            copy('vinculos_functions.php', $backup_name);
            echo "<p>💾 Backup criado: $backup_name</p>";
        }
        
        // Criar funções para estrutura nova
        $funcoes_nova = '<?php
/**
 * Funções para estrutura nova da tabela acd_usuario_pa
 * Onde usuario_api_id referencia usuários da API
 * Gerado automaticamente em ' . date('Y-m-d H:i:s') . '
 */

// Função para buscar usuários da API
function buscarTodosUsuarios() {
    $apiFields = [
        "api_user" => "UFL7GXZ14LU9NOR",
        "api_token" => "20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG",
        "api_module" => "Usuarios",
        "api_action" => "listarUsuarios"
    ];

    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => "https://intranet.sicoobcredilivre.com.br/api",
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => http_build_query($apiFields),
    ));
    
    $response = curl_exec($curl);
    curl_close($curl);
    
    if (!$response) return [];
    
    $usuarios = json_decode($response, true);
    return is_array($usuarios) ? $usuarios : [];
}

function buscarVinculosAtivos() {
    global $pdo;
    
    try {
        // Buscar vínculos da tabela
        $sql = "
            SELECT 
                v.id,
                v.usuario_api_id,
                v.pa_id,
                v.data_inicio,
                p.nome as pa_nome,
                p.numero as pa_numero
            FROM acd_usuario_pa v
            INNER JOIN pontos_atendimento p ON v.pa_id = p.id
            WHERE v.status = \"ativo\"
            AND (v.data_fim IS NULL OR v.data_fim > CURDATE())
            ORDER BY p.nome
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $vinculos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Buscar dados dos usuários da API
        $usuarios_api = buscarTodosUsuarios();
        $usuarios_map = [];
        foreach ($usuarios_api as $usuario) {
            $usuarios_map[$usuario["id"]] = $usuario;
        }
        
        // Enriquecer vínculos com dados da API
        foreach ($vinculos as &$vinculo) {
            $usuario_api = $usuarios_map[$vinculo["usuario_api_id"]] ?? null;
            if ($usuario_api) {
                $vinculo["usuario_nome"] = $usuario_api["nome"] ?? "Nome não disponível";
                $vinculo["usuario_email"] = $usuario_api["email"] ?? "";
            } else {
                $vinculo["usuario_nome"] = "Usuário não encontrado (ID: " . $vinculo["usuario_api_id"] . ")";
                $vinculo["usuario_email"] = "";
            }
        }
        
        return $vinculos;
        
    } catch (Exception $e) {
        error_log("Erro em buscarVinculosAtivos: " . $e->getMessage());
        return [];
    }
}

function buscarPAsSemResponsavel() {
    global $pdo;
    
    try {
        $sql = "
            SELECT
                pa.id,
                pa.numero,
                pa.nome
            FROM pontos_atendimento pa
            LEFT JOIN acd_usuario_pa v ON (
                pa.id = v.pa_id
                AND v.status = \"ativo\"
                AND (v.data_fim IS NULL OR v.data_fim > CURDATE())
            )
            WHERE v.id IS NULL
            ORDER BY pa.numero
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Erro em buscarPAsSemResponsavel: " . $e->getMessage());
        return [];
    }
}

function criarVinculo($usuario_api_id, $pa_id, $data_inicio = null, $criado_por = null, $observacoes = null) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Verificar se o PA existe
        $stmt = $pdo->prepare("SELECT id, nome FROM pontos_atendimento WHERE id = ?");
        $stmt->execute([$pa_id]);
        $pa = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$pa) {
            $pdo->rollBack();
            return [
                "sucesso" => false,
                "erro" => "Ponto de Atendimento não encontrado (ID: " . $pa_id . ")"
            ];
        }
        
        // Verificar se o usuário existe na API
        $usuarios_api = buscarTodosUsuarios();
        $usuario_encontrado = null;
        foreach ($usuarios_api as $usuario) {
            if ($usuario["id"] == $usuario_api_id) {
                $usuario_encontrado = $usuario;
                break;
            }
        }
        
        if (!$usuario_encontrado) {
            $pdo->rollBack();
            return [
                "sucesso" => false,
                "erro" => "Usuário não encontrado na API (ID: " . $usuario_api_id . ")"
            ];
        }
        
        // Desativar vínculos ativos para este PA
        $stmt = $pdo->prepare("
            UPDATE acd_usuario_pa 
            SET status = \"inativo\",
                data_fim = ?,
                desativado_por = ?,
                desativado_em = NOW(),
                observacoes = CONCAT(IFNULL(observacoes, \"\"), \" | Substituído por novo responsável\")
            WHERE pa_id = ? 
            AND status = \"ativo\"
        ");
        $stmt->execute([$data_inicio ?? date("Y-m-d"), $criado_por ?? $_SESSION["user_id"] ?? 1, $pa_id]);
        
        // Criar novo vínculo
        $stmt = $pdo->prepare("
            INSERT INTO acd_usuario_pa (
                usuario_api_id, pa_id, data_inicio, status, 
                criado_por, criado_em, observacoes
            ) VALUES (?, ?, ?, \"ativo\", ?, NOW(), ?)
        ");
        
        $stmt->execute([
            $usuario_api_id, 
            $pa_id, 
            $data_inicio ?? date("Y-m-d"), 
            $criado_por ?? $_SESSION["user_id"] ?? 1, 
            $observacoes
        ]);
        
        $novo_vinculo_id = $pdo->lastInsertId();
        
        $pdo->commit();
        
        return [
            "sucesso" => true,
            "vinculo_id" => $novo_vinculo_id,
            "mensagem" => "Vínculo criado com sucesso: " . $usuario_encontrado["nome"] . " → " . $pa["nome"]
        ];
        
    } catch (Exception $e) {
        $pdo->rollBack();
        return [
            "sucesso" => false,
            "erro" => $e->getMessage()
        ];
    }
}

function desativarVinculo($vinculo_id, $data_fim = null, $desativado_por = null, $observacoes = null) {
    global $pdo;
    
    try {
        $sql = "
            UPDATE acd_usuario_pa 
            SET status = \"inativo\",
                data_fim = ?,
                desativado_por = ?,
                desativado_em = NOW(),
                observacoes = CONCAT(IFNULL(observacoes, \"\"), \" | \", ?)
            WHERE id = ? 
            AND status = \"ativo\"
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $data_fim ?? date("Y-m-d"), 
            $desativado_por ?? $_SESSION["user_id"] ?? 1, 
            $observacoes ?? "Desativado", 
            $vinculo_id
        ]);
        
        if ($stmt->rowCount() > 0) {
            return [
                "sucesso" => true,
                "mensagem" => "Vínculo desativado com sucesso"
            ];
        } else {
            return [
                "sucesso" => false,
                "erro" => "Vínculo não encontrado ou já inativo"
            ];
        }
        
    } catch (Exception $e) {
        return [
            "sucesso" => false,
            "erro" => $e->getMessage()
        ];
    }
}

function buscarHistoricoUsuario($usuario_api_id) {
    global $pdo;
    
    try {
        $sql = "
            SELECT 
                v.*,
                p.nome as pa_nome,
                p.numero as pa_numero
            FROM acd_usuario_pa v
            INNER JOIN pontos_atendimento p ON v.pa_id = p.id
            WHERE v.usuario_api_id = ?
            ORDER BY v.data_inicio DESC, v.criado_em DESC
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$usuario_api_id]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Erro em buscarHistoricoUsuario: " . $e->getMessage());
        return [];
    }
}

function buscarHistoricoPA($pa_id) {
    global $pdo;
    
    try {
        $sql = "
            SELECT 
                v.*,
                uc.nome_completo as criado_por_nome,
                ud.nome_completo as desativado_por_nome
            FROM acd_usuario_pa v
            LEFT JOIN usuarios uc ON v.criado_por = uc.id
            LEFT JOIN usuarios ud ON v.desativado_por = ud.id
            WHERE v.pa_id = ?
            ORDER BY v.data_inicio DESC, v.criado_em DESC
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$pa_id]);
        $historico = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Enriquecer com dados da API
        $usuarios_api = buscarTodosUsuarios();
        $usuarios_map = [];
        foreach ($usuarios_api as $usuario) {
            $usuarios_map[$usuario["id"]] = $usuario;
        }
        
        foreach ($historico as &$item) {
            $usuario_api = $usuarios_map[$item["usuario_api_id"]] ?? null;
            if ($usuario_api) {
                $item["usuario_nome"] = $usuario_api["nome"] ?? "Nome não disponível";
                $item["usuario_email"] = $usuario_api["email"] ?? "";
            } else {
                $item["usuario_nome"] = "Usuário não encontrado (ID: " . $item["usuario_api_id"] . ")";
                $item["usuario_email"] = "";
            }
        }
        
        return $historico;
    } catch (Exception $e) {
        error_log("Erro em buscarHistoricoPA: " . $e->getMessage());
        return [];
    }
}
?>';
        
        // Salvar funções
        file_put_contents('vinculos_functions.php', $funcoes_nova);
        echo "<p>✅ Funções atualizadas para estrutura nova!</p>";
        
        // Testar as novas funções
        echo "<h3>🧪 Testando Funções</h3>";
        
        include 'vinculos_functions.php';
        
        // Teste 1: buscarVinculosAtivos
        echo "<p>🔍 Testando buscarVinculosAtivos()...</p>";
        try {
            $vinculos = buscarVinculosAtivos();
            echo "<p>✅ Sucesso: " . count($vinculos) . " vínculos encontrados</p>";
        } catch (Exception $e) {
            echo "<p>❌ Erro: " . $e->getMessage() . "</p>";
        }
        
        // Teste 2: buscarPAsSemResponsavel
        echo "<p>🔍 Testando buscarPAsSemResponsavel()...</p>";
        try {
            $pas_sem_responsavel = buscarPAsSemResponsavel();
            echo "<p>✅ Sucesso: " . count($pas_sem_responsavel) . " PAs sem responsável</p>";
        } catch (Exception $e) {
            echo "<p>❌ Erro: " . $e->getMessage() . "</p>";
        }
        
        echo "<h3>✅ Correção Concluída!</h3>";
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>🎉 Sucesso!</h4>";
        echo "<p>As funções foram corrigidas para trabalhar com a estrutura nova:</p>";
        echo "<ul>";
        echo "<li>✅ Funções compatíveis com usuario_api_id aplicadas</li>";
        echo "<li>✅ Integração com API configurada</li>";
        echo "<li>✅ Backup do arquivo original criado</li>";
        echo "<li>✅ Testes realizados</li>";
        echo "</ul>";
        echo "<p><strong>Agora você pode usar o sistema normalmente!</strong></p>";
        echo "</div>";
        
        echo "<p><a href='gerenciar_vinculos.php' class='btn btn-success'>🔗 Testar Gerenciar Vínculos</a></p>";
        
    } else {
        echo "<p>As funções serão atualizadas para trabalhar com a estrutura nova onde:</p>";
        echo "<ul>";
        echo "<li>✅ usuario_api_id referencia usuários da API</li>";
        echo "<li>✅ pa_id referencia a tabela pontos_atendimento local</li>";
        echo "<li>✅ Integração completa com API</li>";
        echo "<li>✅ Suporte a histórico de vínculos</li>";
        echo "</ul>";
        
        echo "<form method='POST'>";
        echo "<button type='submit' name='aplicar_correcao' class='btn btn-primary'>🔄 Aplicar Correção</button>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Erro:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "<br><a href='gerenciar_vinculos.php'>← Voltar para Gerenciar Vínculos</a>";
?>

<style>
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    color: white; 
    text-decoration: none; 
    border-radius: 4px; 
    border: none;
    cursor: pointer;
    margin: 5px;
}
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn:hover { opacity: 0.8; }
</style>
