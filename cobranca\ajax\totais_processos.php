<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    // Total de processos
    $total_processos = $pdo->query("SELECT COUNT(*) FROM cbp_processos_judiciais")->fetchColumn();

    // Valor total dos processos
    $valor_total = $pdo->query("
        SELECT COALESCE(SUM(pc.valor_contrato), 0) as total
        FROM cbp_processos_contratos pc
        INNER JOIN cbp_processos_judiciais p ON pc.processo_id = p.id
        WHERE p.status_id NOT IN (3, 6) -- Excluir quitados e inativos
    ")->fetchColumn();

    // Total de processos em andamento (vigentes, acordo judicial, retomado)
    $total_andamento = $pdo->query("
        SELECT COUNT(*) 
        FROM cbp_processos_judiciais 
        WHERE status_id IN (1, 2, 4)
    ")->fetchColumn();

    // Total de processos concluídos (quitados)
    $total_concluidos = $pdo->query("
        SELECT COUNT(*) 
        FROM cbp_processos_judiciais 
        WHERE status_id = 3
    ")->fetchColumn();

    echo json_encode([
        'success' => true,
        'total_processos' => $total_processos,
        'valor_total' => $valor_total,
        'total_andamento' => $total_andamento,
        'total_concluidos' => $total_concluidos
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao buscar totais: ' . $e->getMessage()
    ]);
} 