-- Script para diagnosticar e corrigir problemas com JOINs na consulta de honorários

-- 1. Verificar honorários sem associados válidos
SELECT 'Verificando honorários sem associados válidos' as mensagem;
SELECT h.id, h.processo_id, h.associado_id, h.tipo, h.status
FROM cbp_honorarios h
LEFT JOIN cbp_associados a ON h.associado_id = a.id
WHERE a.id IS NULL;

-- 2. Verificar honorários sem processos válidos
SELECT 'Verificando honorários sem processos válidos' as mensagem;
SELECT h.id, h.processo_id, h.associado_id, h.tipo, h.status
FROM cbp_honorarios h
LEFT JOIN cbp_processos_judiciais p ON h.processo_id = p.id
WHERE p.id IS NULL;

-- 3. Verificar a estrutura e JOINs em etapas

-- 3.1. Apenas honorários
SELECT 'Contagem de honorários' as mensagem;
SELECT COUNT(*) as total FROM cbp_honorarios;

-- 3.2. Honorários com associados
SELECT 'Contagem de honorários com associados' as mensagem;
SELECT COUNT(*) as total_com_associados
FROM cbp_honorarios h
INNER JOIN cbp_associados a ON h.associado_id = a.id;

-- 3.3. Honorários com processos
SELECT 'Contagem de honorários com processos' as mensagem;
SELECT COUNT(*) as total_com_processos
FROM cbp_honorarios h
INNER JOIN cbp_processos_judiciais p ON h.processo_id = p.id;

-- 3.4. Honorários com processos e associados
SELECT 'Contagem de honorários com processos e associados' as mensagem;
SELECT COUNT(*) as total_com_processos_e_associados
FROM cbp_honorarios h
INNER JOIN cbp_processos_judiciais p ON h.processo_id = p.id
INNER JOIN cbp_associados a ON h.associado_id = a.id;

-- 3.5. Consulta completa
SELECT 'Contagem com a consulta completa' as mensagem;
SELECT COUNT(*) as total_consulta_completa
FROM cbp_honorarios h
INNER JOIN cbp_associados a ON h.associado_id = a.id
INNER JOIN cbp_processos_judiciais p ON h.processo_id = p.id
LEFT JOIN pontos_atendimento pa ON p.pa_id = pa.id
LEFT JOIN cbp_advogados adv ON h.advogado_id = adv.id;

-- 4. Tentativa de correção automática de IDs inválidos

-- 4.1. Verificar processos sem associados 
SELECT 'Verificando processos sem associados' as mensagem;
SELECT id FROM cbp_processos_judiciais WHERE associado_id IS NULL;

-- 4.2. Verificar honorários com processos ou associados nulos
SELECT 'Verificando honorários com IDs nulos' as mensagem;
SELECT id, tipo, status FROM cbp_honorarios 
WHERE processo_id IS NULL OR associado_id IS NULL;

-- 5. Amostra final para verificação
SELECT 'Amostra de honorários após diagnóstico' as mensagem;
SELECT 
    h.id, h.tipo, h.status,
    a.nome as nome_associado,
    p.numero_processo
FROM 
    cbp_honorarios h
    LEFT JOIN cbp_associados a ON h.associado_id = a.id
    LEFT JOIN cbp_processos_judiciais p ON h.processo_id = p.id
LIMIT 10; 