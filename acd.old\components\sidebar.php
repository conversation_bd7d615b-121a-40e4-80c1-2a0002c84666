<?php
// Get current page for active menu highlighting
$current_page = basename($_SERVER['PHP_SELF']);

// Verificar permissões do usuário
$tem_acesso_acd = false;
$eh_admin_gestor = false;

if (isset($_SESSION['user_id'])) {
    // Incluir arquivo de verificação de permissões se não estiver incluído
    if (!function_exists('checkACDPermission')) {
        require_once __DIR__ . '/../check_acd_permission.php';
    }

    $tem_acesso_acd = checkACDPermission($_SESSION['user_id']);
    $eh_admin_gestor = checkAdminOrManagerPermission($_SESSION['user_id']);
}
?>
<!-- Sidebar Styles -->
<style>
    :root {
        --sidebar-width: 250px;
        /* Cores do Sicoob */
        --sicoob-turquesa: #00AE9D;
        --sicoob-verde-escuro: #003641;
        --sicoob-branco: #FFFFFF;
        --sicoob-verde-claro: #C9D200;
        --sicoob-verde-medio: #7DB61C;
        --sicoob-roxo: #49479D;
    }

    .sidebar {
        width: var(--sidebar-width);
        background-color: var(--sicoob-verde-escuro);
        color: var(--sicoob-branco);
        position: fixed;
        height: 100vh;
        overflow-y: auto;
        transition: all 0.3s ease;
        z-index: 1000;
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    }

    .sidebar-header {
        padding: 1.5rem;
        background-color: rgba(0, 0, 0, 0.1);
        text-align: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .sidebar-header img {
        max-width: 120px;
        margin-bottom: 1rem;
    }

    .sidebar-header h4 {
        margin: 0;
        font-size: 1.2rem;
        font-weight: 600;
        color: var(--sicoob-branco);
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-menu li {
        margin: 0;
        padding: 0;
    }

    .sidebar-menu a {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        color: var(--sicoob-branco);
        text-decoration: none;
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
        font-weight: 500;
    }

    .sidebar-menu a:hover {
        background-color: rgba(0, 174, 157, 0.1);
        border-left-color: var(--sicoob-turquesa);
    }

    .sidebar-menu a.active {
        background-color: var(--sicoob-turquesa);
        border-left-color: var(--sicoob-branco);
        font-weight: 600;
    }

    /* Submenu Styles */
    .sidebar-submenu {
        list-style: none;
        padding: 0;
        margin: 0;
        background-color: rgba(0, 0, 0, 0.1);
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
    }

    .sidebar-submenu.active {
        max-height: 500px; /* Altura suficiente para mostrar todos os itens */
        transition: max-height 0.5s ease-in;
    }

    .sidebar-submenu a {
        padding-left: 3.5rem;
        font-size: 0.95rem;
        opacity: 0.9;
    }

    .sidebar-submenu a:hover {
        opacity: 1;
    }

    .sidebar-menu .has-submenu {
        position: relative;
    }

    .sidebar-menu .has-submenu > a::after {
        content: '\f107';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        position: absolute;
        right: 1.5rem;
        transition: transform 0.3s ease;
    }

    .sidebar-menu .has-submenu.active > a::after {
        transform: rotate(180deg);
    }

    .sidebar-menu i {
        width: 20px;
        margin-right: 10px;
        text-align: center;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .sidebar {
            margin-left: calc(-1 * var(--sidebar-width));
        }

        .sidebar.active {
            margin-left: 0;
        }

        .main-content {
            margin-left: 0;
        }

        .main-content.active {
            margin-left: var(--sidebar-width);
        }
    }

    /* Toggle Button */
    .sidebar-toggle {
        display: none;
        position: fixed;
        top: 1rem;
        left: 1rem;
        z-index: 1001;
        background: var(--sicoob-verde-escuro);
        color: var(--sicoob-branco);
        border: none;
        padding: 0.5rem;
        border-radius: 4px;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .sidebar-toggle:hover {
        background: var(--sicoob-turquesa);
    }

    @media (max-width: 768px) {
        .sidebar-toggle {
            display: block;
        }
    }
</style>

<!-- Sidebar Toggle Button -->
<button class="sidebar-toggle d-md-none" id="sidebarToggle">
    <i class="fas fa-bars"></i>
</button>

<!-- Sidebar Navigation -->
<nav class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <img src="../assets/images/logo1.png" alt="Sicoob Logo">
        <h4>Análise de Crédito</h4>
    </div>
    <ul class="sidebar-menu">
        <!-- Dashboard Público - Sempre visível -->
        <li>
            <a href="dashboard.php" class="<?php echo $current_page === 'dashboard.php' ? 'active' : ''; ?>">
                <i class="fas fa-home"></i>
                <span>Dashboard Público</span>
            </a>
        </li>

        <?php if ($tem_acesso_acd && $eh_admin_gestor): ?>
        <!-- Dashboard ACD - Apenas Admin/Gestor -->
        <li>
            <a href="index.php" class="<?php echo $current_page === 'index.php' ? 'active' : ''; ?>">
                <i class="fas fa-chart-line"></i>
                <span>Dashboard</span>
            </a>
        </li>
        <?php endif; ?>

        <?php if ($tem_acesso_acd): ?>
        <!-- Painel do Usuário - Qualquer usuário ACD -->
        <li>
            <a href="painelusu.php" class="<?php echo $current_page === 'painelusu.php' ? 'active' : ''; ?>">
                <i class="fas fa-user-chart"></i>
                <span>Painel do Usuário</span>
            </a>
        </li>
        <?php endif; ?>

        <?php if ($tem_acesso_acd && $eh_admin_gestor): ?>
        <!-- Rankings - Apenas Admin/Gestor -->
        <li>
            <a href="rankings.php" class="<?php echo $current_page === 'rankings.php' ? 'active' : ''; ?>">
                <i class="fas fa-trophy"></i>
                <span>Rankings de PAs</span>
            </a>
        </li>

        <!-- Relatórios - Apenas Admin/Gestor -->
        <li>
            <a href="relatorios.php" class="<?php echo $current_page === 'relatorios.php' ? 'active' : ''; ?>">
                <i class="fas fa-chart-line"></i>
                <span>Relatórios</span>
            </a>
        </li>
        <?php endif; ?>

        <?php if ($tem_acesso_acd): ?>
        <!-- Formulário - Qualquer usuário ACD -->
        <li>
            <a href="formsacd.php" class="<?php echo $current_page === 'formsacd.php' ? 'active' : ''; ?>">
                <i class="fas fa-file-alt"></i>
                <span>Formulário</span>
            </a>
        </li>
        <?php endif; ?>
        <?php if ($tem_acesso_acd && $eh_admin_gestor): ?>
        <!-- Menu Importar - Apenas Admin/Gestor -->
        <li class="has-submenu <?php echo strpos($current_page, 'importar_') !== false ? 'active' : ''; ?>">
            <a href="#" class="submenu-toggle">
                <i class="fas fa-file-import"></i>
                <span>Importar</span>
            </a>
            <ul class="sidebar-submenu <?php echo strpos($current_page, 'importar_') !== false ? 'active' : ''; ?>">
                <li>
                    <a href="importar_limite.php" class="<?php echo $current_page === 'importar_limite.php' ? 'active' : ''; ?>">
                        <i class="fas fa-credit-card"></i>
                        <span>Limite</span>
                    </a>
                </li>
                <li>
                    <a href="importar_emprestimo.php" class="<?php echo $current_page === 'importar_emprestimo.php' ? 'active' : ''; ?>">
                        <i class="fas fa-hand-holding-usd"></i>
                        <span>Empréstimo</span>
                    </a>
                </li>
                <li>
                    <a href="importar_financiamento.php" class="<?php echo $current_page === 'importar_financiamento.php' ? 'active' : ''; ?>">
                        <i class="fas fa-home"></i>
                        <span>Financiamento</span>
                    </a>
                </li>
                <li>
                    <a href="importar_rural.php" class="<?php echo $current_page === 'importar_rural.php' ? 'active' : ''; ?>">
                        <i class="fas fa-tractor"></i>
                        <span>Crédito Rural</span>
                    </a>
                </li>
                <li>
                    <a href="importar_proposta.php" class="<?php echo $current_page === 'importar_proposta.php' ? 'active' : ''; ?>">
                        <i class="fas fa-file-contract"></i>
                        <span>Proposta</span>
                    </a>
                </li>
            </ul>
        </li>

        <!-- Gerenciar Vínculos - Apenas Admin/Gestor -->
        <li>
            <a href="gerenciar_vinculos.php" class="<?php echo $current_page === 'gerenciar_vinculos.php' ? 'active' : ''; ?>">
                <i class="fas fa-link"></i>
                <span>Vínculos Usuários/PA</span>
            </a>
        </li>

        <!-- Mapa de Responsabilidades - Apenas Admin/Gestor -->
        <li>
            <a href="mapa_responsabilidades.php" class="<?php echo $current_page === 'mapa_responsabilidades.php' ? 'active' : ''; ?>">
                <i class="fas fa-map"></i>
                <span>Mapa de Responsabilidades</span>
            </a>
        </li>
        <?php endif; ?>

        <?php if (!$tem_acesso_acd): ?>
        <!-- Mensagem para usuários sem acesso ACD -->
        <li style="padding: 1rem 1.5rem; color: rgba(255,255,255,0.6); font-style: italic; font-size: 0.9rem;">
            <i class="fas fa-info-circle"></i>
            <span>Solicite acesso ACD ao administrador para ver mais opções</span>
        </li>
        <?php endif; ?>
    </ul>
</nav>

<!-- Sidebar JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle Sidebar on Mobile
        const sidebarToggle = document.getElementById('sidebarToggle');
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');

        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('active');
                if (mainContent) {
                    mainContent.classList.toggle('active');
                }
            });
        }

        // Toggle Submenu
        const submenuToggles = document.querySelectorAll('.submenu-toggle');
        submenuToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const submenu = this.parentElement;
                const submenuList = submenu.querySelector('.sidebar-submenu');
                
                // Fecha outros submenus
                document.querySelectorAll('.sidebar-submenu.active').forEach(function(activeSubmenu) {
                    if (activeSubmenu !== submenuList) {
                        activeSubmenu.classList.remove('active');
                        activeSubmenu.parentElement.classList.remove('active');
                    }
                });

                // Toggle do submenu atual
                submenu.classList.toggle('active');
                submenuList.classList.toggle('active');
            });
        });

        // Mantém o submenu aberto se estiver em uma página de importação
        const currentPage = '<?php echo $current_page; ?>';
        if (currentPage.startsWith('importar_')) {
            const importSubmenu = document.querySelector('.has-submenu');
            const importSubmenuList = importSubmenu.querySelector('.sidebar-submenu');
            if (importSubmenu && importSubmenuList) {
                importSubmenu.classList.add('active');
                importSubmenuList.classList.add('active');
            }
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768 && 
                sidebar && 
                !sidebar.contains(e.target) && 
                !sidebarToggle.contains(e.target) && 
                sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
                if (mainContent) {
                    mainContent.classList.remove('active');
                }
            }
        });
    });
</script> 