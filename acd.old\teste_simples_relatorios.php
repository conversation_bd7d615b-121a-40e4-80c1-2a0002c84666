<?php
session_start();
require_once '../config/database.php';

echo "=== TESTE SIMPLES RELATÓRIOS ===\n\n";

try {
    // Teste básico da consulta
    echo "1. 📊 TESTANDO CONSULTA BÁSICA:\n";
    
    $sql = "
        SELECT 
            f.id,
            f.pa,
            f.usuario_id,
            f.acao,
            f.data_criacao,
            f.prazo_dia,
            f.observacoes
        FROM acd_formularios f
        WHERE f.pa != '0' AND f.pa IS NOT NULL
        ORDER BY f.data_criacao DESC
        LIMIT 10
    ";
    
    $stmt = $pdo->query($sql);
    $propostas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "   Propostas encontradas: " . count($propostas) . "\n\n";
    
    if (!empty($propostas)) {
        echo "   Primeiras propostas:\n";
        foreach ($propostas as $index => $proposta) {
            $data = new DateTime($proposta['data_criacao']);
            $prazo_real = max(0, intval($proposta['prazo_dia']) - 1);
            
            echo "   " . ($index + 1) . ". ID: {$proposta['id']}\n";
            echo "      PA: {$proposta['pa']}\n";
            echo "      Usuário ID: {$proposta['usuario_id']}\n";
            echo "      Ação: {$proposta['acao']}\n";
            echo "      Data: " . $data->format('d/m/Y H:i') . "\n";
            echo "      Prazo: {$prazo_real} dias\n";
            echo "      Observações: " . (strlen($proposta['observacoes']) > 50 ? substr($proposta['observacoes'], 0, 50) . '...' : $proposta['observacoes']) . "\n\n";
        }
    } else {
        echo "   ❌ Nenhuma proposta encontrada!\n";
        
        // Verificar se existem dados na tabela
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM acd_formularios");
        $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        echo "   Total de registros na tabela: $total\n";
        
        if ($total > 0) {
            // Verificar valores de PA
            $stmt = $pdo->query("SELECT DISTINCT pa, COUNT(*) as count FROM acd_formularios GROUP BY pa");
            $pas = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "   Valores de PA encontrados:\n";
            foreach ($pas as $pa) {
                echo "   - '{$pa['pa']}': {$pa['count']} registros\n";
            }
        }
    }
    
    // Testar contagem total
    echo "\n2. 🔢 TESTANDO CONTAGEM TOTAL:\n";
    
    $count_sql = "
        SELECT COUNT(*) as total
        FROM acd_formularios f
        WHERE f.pa != '0' AND f.pa IS NOT NULL
    ";
    
    $stmt = $pdo->query($count_sql);
    $total_records = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "   Total de registros que atendem aos critérios: $total_records\n";
    
    // Testar busca de PAs
    echo "\n3. 🏢 TESTANDO BUSCA DE PAs:\n";
    
    $stmt = $pdo->query("
        SELECT DISTINCT nome 
        FROM pontos_atendimento 
        WHERE nome != '0' AND nome IS NOT NULL AND nome != 'UAD'
        ORDER BY nome
        LIMIT 10
    ");
    $pas_disponiveis = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "   PAs disponíveis para filtro: " . count($pas_disponiveis) . "\n";
    foreach ($pas_disponiveis as $pa) {
        echo "   - {$pa['nome']}\n";
    }
    
    // Testar busca de usuários
    echo "\n4. 👥 TESTANDO BUSCA DE USUÁRIOS:\n";
    
    $stmt = $pdo->query("
        SELECT DISTINCT usuario_id as id, usuario_id as username
        FROM acd_formularios
        WHERE usuario_id IS NOT NULL
        ORDER BY usuario_id
        LIMIT 10
    ");
    $usuarios_disponiveis = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "   Usuários disponíveis para filtro: " . count($usuarios_disponiveis) . "\n";
    foreach ($usuarios_disponiveis as $usuario) {
        echo "   - ID: {$usuario['id']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERRO: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== FIM DO TESTE ===\n";
?>
