<?php
session_start();
require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

header('Content-Type: application/json');

// Verificar se o usuário está logado e tem acesso ACD
if (!isset($_SESSION['user_id']) || !checkACDPermission($_SESSION['user_id'])) {
    echo json_encode(['error' => 'Acesso negado']);
    exit;
}

// Verificar se a conexão está ativa
if (!isset($pdo) || !($pdo instanceof PDO)) {
    error_log('Erro: Conexão com o banco de dados não está disponível');
    echo json_encode(['error' => 'Erro de conexão com o banco de dados']);
    exit;
}

if (!isset($_POST['documento'])) {
    error_log('Erro: Parâmetro documento não fornecido');
    echo json_encode(['error' => 'Parâmetro documento não fornecido']);
    exit;
}

$documento = preg_replace('/[^0-9]/', '', $_POST['documento']);
error_log('Documento recebido (após limpeza): ' . $documento);

// Buscar mesmo com menos de 11 dígitos para autocompletar
if (strlen($documento) < 3) {
    error_log('Documento muito curto: ' . strlen($documento) . ' dígitos');
    echo json_encode([]);
    exit;
}

try {
    // Primeiro, vamos verificar se a tabela e a coluna existem
    $checkTable = $pdo->query("SHOW TABLES LIKE 'parecer_base'");
    if ($checkTable->rowCount() === 0) {
        error_log('Erro: Tabela parecer_base não existe');
        echo json_encode(['error' => 'Tabela não encontrada']);
        exit;
    }

    $checkColumn = $pdo->query("SHOW COLUMNS FROM parecer_base LIKE 'cpf_cnpj'");
    if ($checkColumn->rowCount() === 0) {
        error_log('Erro: Coluna cpf_cnpj não existe na tabela parecer_base');
        echo json_encode(['error' => 'Coluna não encontrada']);
        exit;
    }

    $sql = "SELECT DISTINCT 
                cpf_cnpj as documento,
                nome_cliente as nome 
            FROM parecer_base 
            WHERE cpf_cnpj LIKE ? 
            AND LENGTH(cpf_cnpj) >= 11
            ORDER BY 
                CASE 
                    WHEN cpf_cnpj LIKE ? THEN 1  -- Exatos primeiro
                    WHEN cpf_cnpj LIKE ? THEN 2  -- Começando com o número
                    ELSE 3                  -- Contendo o número
                END,
                nome_cliente ASC
            LIMIT 10";
    
    error_log('SQL Query: ' . $sql);
    error_log('Parâmetros: ' . implode(', ', [$documento . '%', $documento, $documento . '%']));
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        $documento . '%',           // Para LIKE inicial
        $documento,                 // Para match exato
        $documento . '%'            // Para começa com
    ]);
    
    $resultados = $stmt->fetchAll(PDO::FETCH_ASSOC);
    error_log('Número de resultados encontrados: ' . count($resultados));
    
    // Formatar os documentos encontrados
    foreach ($resultados as &$resultado) {
        $doc = $resultado['documento'];
        if (strlen($doc) === 11) {
            $resultado['documento_formatado'] = substr($doc, 0, 3) . '.' . 
                                               substr($doc, 3, 3) . '.' . 
                                               substr($doc, 6, 3) . '-' . 
                                               substr($doc, 9, 2);
            $resultado['tipo'] = 'CPF';
        } else if (strlen($doc) === 14) {
            $resultado['documento_formatado'] = substr($doc, 0, 2) . '.' . 
                                               substr($doc, 2, 3) . '.' . 
                                               substr($doc, 5, 3) . '/' . 
                                               substr($doc, 8, 4) . '-' . 
                                               substr($doc, 12, 2);
            $resultado['tipo'] = 'CNPJ';
        }
    }
    
    echo json_encode($resultados);
} catch (PDOException $e) {
    error_log('Erro ao buscar documento: ' . $e->getMessage());
    error_log('Código do erro: ' . $e->getCode());
    error_log('Stack trace: ' . $e->getTraceAsString());
    echo json_encode(['error' => 'Erro ao buscar documento: ' . $e->getMessage()]);
} catch (Exception $e) {
    error_log('Erro geral: ' . $e->getMessage());
    error_log('Stack trace: ' . $e->getTraceAsString());
    echo json_encode(['error' => 'Erro geral: ' . $e->getMessage()]);
} 