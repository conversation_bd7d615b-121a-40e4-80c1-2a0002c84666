<?php
session_start();
require_once '../config/database.php';

// Obter o PA da URL
$pa_nome = $_GET['pa'] ?? 'Matriz'; // Usar Matriz como padrão para teste

echo "<h2>Debug Dashboard PA: " . htmlspecialchars($pa_nome) . "</h2>";

// Função para buscar usuários da API
function buscarUsuariosAPI() {
    $apiFields = [
        'api_user' => 'UFL7GXZ14LU9NOR',
        'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
        'api_module' => 'Usuarios',
        'api_action' => 'listarUsuarios'
    ];

    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => http_build_query($apiFields),
    ));

    $response = curl_exec($curl);

    if (curl_errno($curl)) {
        curl_close($curl);
        return [];
    }

    curl_close($curl);

    $usuarios = json_decode($response, true);

    if (!is_array($usuarios)) {
        return [];
    }

    $usuarios_mapeados = [];
    foreach ($usuarios as $user) {
        $usuarios_mapeados[$user['id']] = [
            'id' => $user['id'],
            'nome' => $user['nome'] ?? '',
            'pa' => $user['nomeAgencia'] ?? '',
            'ativo' => (isset($user['status']) && $user['status'] == 1 && 
                       isset($user['bloqueado']) && $user['bloqueado'] == 0)
        ];
    }

    return $usuarios_mapeados;
}

try {
    // Buscar usuários da API
    echo "<h3>1. Testando API de Usuários</h3>";
    $usuarios_api = buscarUsuariosAPI();
    echo "Total de usuários da API: " . count($usuarios_api) . "<br>";
    echo "Primeiros 5 usuários: <pre>" . print_r(array_slice($usuarios_api, 0, 5, true), true) . "</pre>";

    // Verificar se o PA existe
    echo "<h3>2. Verificando PA</h3>";
    $stmt = $pdo->prepare("SELECT id, nome, numero FROM pontos_atendimento WHERE nome = ?");
    $stmt->execute([$pa_nome]);
    $pa_info = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "PA Info: <pre>" . print_r($pa_info, true) . "</pre>";

    // Estatísticas por usuário PA (mês atual)
    echo "<h3>3. Estatísticas por usuário PA</h3>";
    $condicao_data = "AND YEAR(data_criacao) = YEAR(CURDATE()) AND MONTH(data_criacao) = MONTH(CURDATE())";
    
    $sql_usuarios = "
        SELECT
            usuario_pa,
            COUNT(*) as total_propostas,
            SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) as aprovadas,
            SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
            CASE 
                WHEN COUNT(*) > 0 THEN ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1)
                ELSE 0
            END as taxa_devolucao
        FROM acd_formularios
        WHERE pa = ?
        $condicao_data
        AND usuario_pa IS NOT NULL
        AND usuario_pa != ''
        AND usuario_pa != '0'
        GROUP BY usuario_pa
        HAVING COUNT(*) > 0
        ORDER BY total_propostas DESC
        LIMIT 10
    ";
    
    echo "SQL: <pre>" . $sql_usuarios . "</pre>";
    
    $stmt = $pdo->prepare($sql_usuarios);
    $stmt->execute([$pa_nome]);
    $estatisticas_usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Total de usuários com propostas: " . count($estatisticas_usuarios) . "<br>";
    echo "Dados: <pre>" . print_r($estatisticas_usuarios, true) . "</pre>";

    // Distribuição por mesa para pizza
    echo "<h3>4. Distribuição por Mesa (Pizza)</h3>";
    $sql_mesa_pizza = "
        SELECT
            mesa,
            COUNT(*) as total,
            SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
            CASE 
                WHEN COUNT(*) > 0 THEN ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1)
                ELSE 0
            END as taxa_devolucao
        FROM acd_formularios
        WHERE pa = ?
        $condicao_data
        GROUP BY mesa
        ORDER BY total DESC
    ";
    
    echo "SQL: <pre>" . $sql_mesa_pizza . "</pre>";
    
    $stmt = $pdo->prepare($sql_mesa_pizza);
    $stmt->execute([$pa_nome]);
    $distribuicao_mesa_pizza = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Total de mesas: " . count($distribuicao_mesa_pizza) . "<br>";
    echo "Dados: <pre>" . print_r($distribuicao_mesa_pizza, true) . "</pre>";

    // Verificar dados gerais do PA
    echo "<h3>5. Dados Gerais do PA</h3>";
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM acd_formularios WHERE pa = ?");
    $stmt->execute([$pa_nome]);
    $total_geral = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Total de propostas do PA (todos os tempos): " . $total_geral['total'] . "<br>";

    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM acd_formularios WHERE pa = ? $condicao_data");
    $stmt->execute([$pa_nome]);
    $total_mes = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Total de propostas do PA (este mês): " . $total_mes['total'] . "<br>";

    // Verificar usuários únicos
    echo "<h3>6. Usuários Únicos</h3>";
    $stmt = $pdo->prepare("SELECT DISTINCT usuario_pa FROM acd_formularios WHERE pa = ? AND usuario_pa IS NOT NULL AND usuario_pa != '' AND usuario_pa != '0'");
    $stmt->execute([$pa_nome]);
    $usuarios_unicos = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Usuários únicos no PA: " . count($usuarios_unicos) . "<br>";
    echo "IDs: " . implode(', ', array_slice($usuarios_unicos, 0, 10)) . "<br>";

} catch (Exception $e) {
    echo "<h3>ERRO:</h3>";
    echo "Mensagem: " . $e->getMessage() . "<br>";
    echo "Arquivo: " . $e->getFile() . "<br>";
    echo "Linha: " . $e->getLine() . "<br>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #003641; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
</style>
