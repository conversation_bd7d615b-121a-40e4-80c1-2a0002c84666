<?php
function checkACDPermission($usuario_id, $pagina = null) {
    global $pdo;

    // Primeiro, verificar se o usuário é administrador ou gestor
    // Admins e gestores têm acesso automático ao ACD
    if (checkAdminOrManagerPermission($usuario_id)) {
        return true;
    }

    // Verificar se o usuário tem acesso a QUALQUER botão ACD
    // Se tiver acesso a um, tem acesso a toda a pasta acd

    // Verificar se o usuário tem acesso direto a algum botão ACD
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM card_button_usuarios cbu
        INNER JOIN card_buttons cb ON cbu.button_id = cb.id
        WHERE cbu.usuario_id = ?
        AND cb.ativo = 1
        AND cb.link LIKE '%acd/%'
    ");
    $stmt->execute([$usuario_id]);
    $tem_acesso_usuario = $stmt->fetchColumn() > 0;

    // Verificar se algum setor do usuário tem acesso a algum botão ACD
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM card_button_setores cbs
        INNER JOIN card_buttons cb ON cbs.button_id = cb.id
        INNER JOIN usuario_setor us ON cbs.setor_id = us.setor_id
        WHERE us.usuario_id = ?
        AND cb.ativo = 1
        AND cb.link LIKE '%acd/%'
    ");
    $stmt->execute([$usuario_id]);
    $tem_acesso_setor = $stmt->fetchColumn() > 0;

    // Retorna true se tiver acesso por usuário ou por setor a qualquer botão ACD
    return ($tem_acesso_usuario || $tem_acesso_setor);
}

function checkAdminOrManagerPermission($usuario_id) {
    global $pdo;

    try {
        // Verificar se o usuário é administrador ou gestor
        // Primeiro, buscar apenas as colunas que existem
        $stmt = $pdo->prepare("
            SELECT u.nivel_acesso_id
            FROM usuarios u
            WHERE u.id = ?
        ");
        $stmt->execute([$usuario_id]);
        $usuario = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$usuario) {
            return false;
        }

        // Verificar por nível de acesso (PRINCIPAL)
        // 1 = Administrador, 2 = Gestor, 3 = Usuário
        if (isset($usuario['nivel_acesso_id'])) {
            $nivel = intval($usuario['nivel_acesso_id']);
            if ($nivel === 1 || $nivel === 2) {
                return true; // Administrador ou Gestor
            }
        }

        // Se chegou até aqui, não é admin nem gestor
        return false;

    } catch (Exception $e) {
        error_log("Erro ao verificar permissão admin/gestor: " . $e->getMessage());
        return false;
    }
}

function getACDButtonsForUser($usuario_id) {
    global $pdo;

    // Buscar botões ACD que o usuário tem acesso
    $stmt = $pdo->prepare("
        SELECT DISTINCT cb.id, cb.titulo, cb.link, cb.icone
        FROM card_buttons cb
        WHERE cb.ativo = 1
        AND (cb.link LIKE '%painelusu%' OR cb.link LIKE '%rankings%' OR cb.link LIKE '%relatorios%' OR cb.link LIKE '%formsacd%')
        AND (
            cb.id IN (
                SELECT cbu.button_id
                FROM card_button_usuarios cbu
                WHERE cbu.usuario_id = ?
            )
            OR cb.id IN (
                SELECT cbs.button_id
                FROM card_button_setores cbs
                JOIN usuario_setor us ON cbs.setor_id = us.setor_id
                WHERE us.usuario_id = ?
            )
        )
        ORDER BY cb.ordem, cb.titulo
    ");
    $stmt->execute([$usuario_id, $usuario_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>
