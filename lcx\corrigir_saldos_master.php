<?php
session_start();
require_once '../config/database.php';

// Verificar se o usuário está logado e é admin
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se é admin
try {
    $stmt = $pdo->prepare("SELECT nivel_permissao FROM lcx_permissoes WHERE usuario_id = ? AND ativo = 1");
    $stmt->execute([$_SESSION['user_id']]);
    $permissao = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$permissao || $permissao['nivel_permissao'] !== 'admin') {
        echo "Acesso negado. Apenas administradores podem executar esta correção.";
        exit;
    }
} catch (Exception $e) {
    echo "Erro ao verificar permissões: " . $e->getMessage();
    exit;
}

echo "<h2>Correção de Saldos do Livro Master</h2>";

try {
    // Buscar livro master
    $stmt = $pdo->query("SELECT id, nome, saldo_inicial FROM lcx_livros_caixa WHERE tipo = 'master' ORDER BY id DESC LIMIT 1");
    $livro_master = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$livro_master) {
        echo "<p style='color: red;'>Livro master não encontrado!</p>";
        exit;
    }
    
    echo "<h3>Livro Master: {$livro_master['nome']} (ID: {$livro_master['id']})</h3>";
    echo "<p><strong>Saldo Inicial:</strong> R$ " . number_format($livro_master['saldo_inicial'], 2, ',', '.') . "</p>";
    
    // Buscar todas as movimentações do master ordenadas por data
    $stmt = $pdo->prepare("
        SELECT id, tipo, valor, saldo_anterior, saldo_posterior, data_competencia, data_movimentacao, categoria
        FROM lcx_movimentacoes 
        WHERE livro_caixa_id = ? 
        ORDER BY data_competencia ASC, data_movimentacao ASC, id ASC
    ");
    $stmt->execute([$livro_master['id']]);
    $movimentacoes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Análise das Movimentações:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>ID</th><th>Data</th><th>Tipo</th><th>Valor</th><th>Categoria</th>";
    echo "<th>Saldo Anterior</th><th>Saldo Posterior</th><th>Saldo Calculado</th><th>Status</th>";
    echo "</tr>";
    
    $saldo_calculado = floatval($livro_master['saldo_inicial']);
    $erros_encontrados = [];
    
    foreach ($movimentacoes as $mov) {
        // Calcular saldo correto
        if ($mov['tipo'] === 'entrada') {
            $saldo_correto = $saldo_calculado + floatval($mov['valor']);
        } else {
            $saldo_correto = $saldo_calculado - floatval($mov['valor']);
        }
        
        $saldo_posterior_atual = floatval($mov['saldo_posterior']);
        $tem_erro = abs($saldo_correto - $saldo_posterior_atual) > 0.01;
        
        if ($tem_erro) {
            $erros_encontrados[] = [
                'id' => $mov['id'],
                'saldo_atual' => $saldo_posterior_atual,
                'saldo_correto' => $saldo_correto
            ];
        }
        
        echo "<tr" . ($tem_erro ? " style='background: #ffebee;'" : "") . ">";
        echo "<td>{$mov['id']}</td>";
        echo "<td>{$mov['data_competencia']}</td>";
        echo "<td>{$mov['tipo']}</td>";
        echo "<td>R$ " . number_format($mov['valor'], 2, ',', '.') . "</td>";
        echo "<td>" . substr($mov['categoria'], 0, 30) . "...</td>";
        echo "<td>R$ " . number_format($mov['saldo_anterior'], 2, ',', '.') . "</td>";
        echo "<td>R$ " . number_format($saldo_posterior_atual, 2, ',', '.') . "</td>";
        echo "<td>R$ " . number_format($saldo_correto, 2, ',', '.') . "</td>";
        echo "<td>" . ($tem_erro ? "❌ ERRO" : "✅ OK") . "</td>";
        echo "</tr>";
        
        $saldo_calculado = $saldo_correto;
    }
    
    echo "</table>";
    
    if (empty($erros_encontrados)) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h4 style='color: #155724; margin-top: 0;'>✅ Todos os Saldos Estão Corretos!</h4>";
        echo "<p style='color: #155724; margin-bottom: 0;'>Não foram encontrados erros nos cálculos de saldo.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h4 style='color: #721c24; margin-top: 0;'>⚠️ Erros Encontrados: " . count($erros_encontrados) . "</h4>";
        echo "<p style='color: #721c24;'>As seguintes movimentações têm saldos incorretos:</p>";
        echo "<ul style='color: #721c24;'>";
        foreach ($erros_encontrados as $erro) {
            echo "<li>Movimentação ID {$erro['id']}: Saldo atual R$ " . number_format($erro['saldo_atual'], 2, ',', '.') . 
                 " → Deveria ser R$ " . number_format($erro['saldo_correto'], 2, ',', '.') . "</li>";
        }
        echo "</ul>";
        echo "</div>";
        
        if (isset($_GET['corrigir']) && $_GET['corrigir'] === 'sim') {
            echo "<h3>Executando Correções:</h3>";
            
            $pdo->beginTransaction();
            
            try {
                $saldo_recalculado = floatval($livro_master['saldo_inicial']);
                
                foreach ($movimentacoes as $mov) {
                    $saldo_anterior = $saldo_recalculado;
                    
                    if ($mov['tipo'] === 'entrada') {
                        $saldo_recalculado += floatval($mov['valor']);
                    } else {
                        $saldo_recalculado -= floatval($mov['valor']);
                    }
                    
                    // Atualizar movimentação
                    $stmt = $pdo->prepare("
                        UPDATE lcx_movimentacoes 
                        SET saldo_anterior = ?, saldo_posterior = ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([$saldo_anterior, $saldo_recalculado, $mov['id']]);
                }
                
                // Atualizar saldo atual do livro
                $stmt = $pdo->prepare("UPDATE lcx_livros_caixa SET saldo_atual = ? WHERE id = ?");
                $stmt->execute([$saldo_recalculado, $livro_master['id']]);
                
                $pdo->commit();
                
                echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
                echo "<h4 style='color: #155724; margin-top: 0;'>✅ Correção Executada com Sucesso!</h4>";
                echo "<p style='color: #155724;'>Todos os saldos foram recalculados e corrigidos.</p>";
                echo "<p style='color: #155724; margin-bottom: 0;'><strong>Saldo final do livro:</strong> R$ " . number_format($saldo_recalculado, 2, ',', '.') . "</p>";
                echo "</div>";
                
                echo "<p><a href='corrigir_saldos_master.php'>Verificar novamente</a></p>";
                
            } catch (Exception $e) {
                $pdo->rollback();
                echo "<p style='color: red;'>Erro ao executar correção: " . $e->getMessage() . "</p>";
            }
            
        } else {
            echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
            echo "<h4 style='color: #856404; margin-top: 0;'>⚠️ Confirmação Necessária</h4>";
            echo "<p style='color: #856404;'>Esta operação irá recalcular todos os saldos das movimentações. Certifique-se de que:</p>";
            echo "<ul style='color: #856404;'>";
            echo "<li>Você tem backup do banco de dados</li>";
            echo "<li>Não há operações em andamento</li>";
            echo "<li>As movimentações estão na ordem correta</li>";
            echo "</ul>";
            echo "<p style='color: #856404; margin-bottom: 0;'>";
            echo "<a href='?corrigir=sim' style='background: #ffc107; border: 1px solid #ffc107; color: #000; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Executar Correção</a>";
            echo "</p>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Erro:</strong> " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; font-size: 12px; }
th, td { padding: 4px 8px; text-align: left; }
th { background: #f8f9fa; }
</style>
