<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if ($id <= 0) {
    echo '<div class="alert alert-danger">Processo não encontrado.</div>';
    exit;
}

// Buscar dados do processo
$stmt = $pdo->prepare("
    SELECT p.*, 
           p.associado_nome as nome,
           p.associado_documento as cpf_cnpj,
           a.nome as advogado_nome,
           a.oab as advogado_oab,
           pa.nome as pa_nome,
           pa.numero as pa_numero,
           m.nome as modalidade_nome,
           s.nome as status_nome,
           ass.numero_contato
    FROM cbp_processos_judiciais p
    LEFT JOIN cbp_advogados a ON p.advogado_id = a.id
    LEFT JOIN pontos_atendimento pa ON p.pa_id = pa.id
    LEFT JOIN cbp_modalidades_processo m ON p.modalidade_id = m.id
    LEFT JOIN cbp_status_processo s ON p.status_id = s.id
    LEFT JOIN cbp_associados ass ON ass.cpf_cnpj COLLATE utf8mb4_general_ci = p.associado_documento COLLATE utf8mb4_general_ci
    WHERE p.id = ?
");
$stmt->execute([$id]);
$processo = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$processo) {
    echo '<div class="alert alert-danger">Processo não encontrado.</div>';
    exit;
}

// Buscar contratos vinculados ao processo
$stmt = $pdo->prepare("
    SELECT c.numero_contrato, pc.valor_contrato, m.nome as modalidade_nome
    FROM cbp_contratos c
    INNER JOIN cbp_processos_contratos pc ON c.id = pc.contrato_id
    LEFT JOIN cbp_modalidades_processo m ON c.modalidade_id = m.id
    WHERE pc.processo_id = ?
");
$stmt->execute([$id]);
$contratos = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Buscar acordos do processo
$stmt = $pdo->prepare("
    SELECT a.*, 
           a.valor_acordo as valor_total,
           a.quantidade_parcelas as numero_parcelas,
           CASE 
               WHEN a.quantidade_parcelas > 0 THEN a.valor_acordo / a.quantidade_parcelas
               ELSE 0
           END as valor_parcela,
           CASE
               WHEN a.ativo = 0 THEN 'INATIVO'
               WHEN NOT EXISTS (
                   SELECT 1
                   FROM cbp_parcelas_acordo pa2
                   WHERE pa2.acordo_id = a.id
                   AND pa2.status != 'PAGO'
               ) AND NOT EXISTS (
                   SELECT 1
                   FROM cbp_alvaras_acordo aa
                   WHERE aa.acordo_id = a.id
                   AND aa.status = 'PENDENTE'
               ) THEN 'QUITADO'
               WHEN EXISTS (
                   SELECT 1
                   FROM cbp_parcelas_acordo pa2
                   WHERE pa2.acordo_id = a.id
                   AND pa2.status = 'CANCELADO'
               ) THEN 'CANCELADO'
               WHEN EXISTS (
                   SELECT 1
                   FROM cbp_parcelas_acordo pa2
                   WHERE pa2.acordo_id = a.id
                   AND pa2.status = 'PENDENTE'
                   AND pa2.data_vencimento < CURRENT_DATE
               ) THEN 'INADIMPLENTE'
               ELSE 'VIGENTE'
           END as status_nome
    FROM cbp_acordos a
    LEFT JOIN cbp_status_acordo s ON a.status_id = s.id
    WHERE a.processo_id = ?
    ORDER BY a.data_acordo DESC
");
$stmt->execute([$id]);
$acordos = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Buscar alvarás do processo
$stmt = $pdo->prepare("
    SELECT a.*, ac.numero_repactuacao
    FROM cbp_alvaras a
    LEFT JOIN cbp_acordos ac ON a.id = ac.alvara_id
    WHERE a.processo_id = ?
    ORDER BY a.data_recebimento DESC
");
$stmt->execute([$id]);
$alvaras = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Função para formatar documento
function formatarDocumento($doc) {
    $doc = preg_replace('/[^0-9]/', '', $doc);
    if (strlen($doc) === 11) {
        return substr($doc, 0, 3) . '.' . 
               substr($doc, 3, 3) . '.' . 
               substr($doc, 6, 3) . '-' . 
               substr($doc, 9, 2);
    } else if (strlen($doc) === 14) {
        return substr($doc, 0, 2) . '.' . 
               substr($doc, 2, 3) . '.' . 
               substr($doc, 5, 3) . '/' . 
               substr($doc, 8, 4) . '-' . 
               substr($doc, 12, 2);
    }
    return $doc;
}

// Função para formatar valor monetário
function formatarValor($valor) {
    return 'R$ ' . number_format($valor, 2, ',', '.');
}

// Função para formatar data
function formatarData($data) {
    return date('d/m/Y', strtotime($data));
}

// Array de status possíveis
$status_list = [
    'VIGENTE' => 'success',
    'QUITADO' => 'info',
    'CANCELADO' => 'danger',
    'INADIMPLENTE' => 'warning',
    'INATIVO' => 'secondary'
];
?>

<div class="container-fluid p-0">
    <!-- Informações do Processo -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Informações do Processo</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless mb-0">
                        <tbody>
                            <tr>
                                <td style="width: 140px;"><strong>Nome:</strong></td>
                                <td><?= htmlspecialchars($processo['nome'] ?? 'Não informado') ?></td>
                            </tr>
                            <tr>
                                <td><strong>CPF/CNPJ:</strong></td>
                                <td><?= !empty($processo['cpf_cnpj']) ? formatarDocumento($processo['cpf_cnpj']) : 'Não informado' ?></td>
                            </tr>
                            <tr>
                                <td><strong>Nº Processo:</strong></td>
                                <td><?= $processo['numero_processo'] ?? 'Não informado' ?></td>
                            </tr>
                            <tr>
                                <td><strong>Data de Envio:</strong></td>
                                <td><?= isset($processo['data_envio']) && !empty($processo['data_envio']) ? date('d/m/Y', strtotime($processo['data_envio'])) : 'Não informada' ?></td>
                            </tr>
                            <tr>
                                <td><strong>Conta Corrente:</strong></td>
                                <td>Informação disponível nos acordos</td>
                            </tr>
                            <tr>
                                <td><strong>Número de Contato:</strong></td>
                                <td><?= !empty($processo['numero_contato']) ? $processo['numero_contato'] : 'Não informado' ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless mb-0">
                        <tbody>
                            <tr>
                                <td style="width: 140px;"><strong>Status:</strong></td>
                                <td><span class="badge" style="background-color: <?= $status_colors[$processo['status_nome']] ?? '#C9D200' ?>"><?= $processo['status_nome'] ?></span></td>
                            </tr>
                            <tr>
                                <td><strong>Advogado:</strong></td>
                                <td><?= $processo['advogado_nome'] ?? 'Não informado' ?></td>
                            </tr>
                            <tr>
                                <td><strong>PA:</strong></td>
                                <td><?= ($processo['pa_nome'] ?? '') . (!empty($processo['pa_numero']) ? ' (' . $processo['pa_numero'] . ')' : '') ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php if (!empty($processo['observacoes'])): ?>
                <div class="row mt-3">
                    <div class="col-12">
                        <strong>Observações:</strong>
                        <p class="text-muted mt-2 mb-0"><?= nl2br(htmlspecialchars($processo['observacoes'])) ?></p>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Informações de Ajuizamento -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Informações de Ajuizamento</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <table class="table table-borderless mb-0">
                        <tbody>
                            <tr>
                                <td style="width: 140px;"><strong>Data:</strong></td>
                                <td><?= isset($processo['data_ajuizamento']) && !empty($processo['data_ajuizamento']) ? date('d/m/Y', strtotime($processo['data_ajuizamento'])) : 'Não ajuizado' ?></td>
                            </tr>
                            <tr>
                                <td><strong>Valor:</strong></td>
                                <td><?= isset($processo['valor_ajuizado']) && $processo['valor_ajuizado'] > 0 ? formatarValor($processo['valor_ajuizado']) : 'Não informado' ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Contratos Vinculados -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Contratos Vinculados</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Contrato</th>
                            <th>Modalidade</th>
                            <th class="text-end">Valor</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        $valor_total = 0;
                        foreach ($contratos as $contrato): 
                            $valor_total += $contrato['valor_contrato'];
                        ?>
                            <tr>
                                <td><?= htmlspecialchars($contrato['numero_contrato']) ?></td>
                                <td><?= htmlspecialchars($contrato['modalidade_nome']) ?></td>
                                <td class="text-end"><?= formatarValor($contrato['valor_contrato']) ?></td>
                            </tr>
                        <?php endforeach; ?>
                        <tr class="table-light">
                            <td colspan="2" class="text-end"><strong>Total:</strong></td>
                            <td class="text-end"><strong><?= formatarValor($valor_total) ?></strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Alvarás -->
    <?php if (!empty($alvaras)): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Alvarás</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Data de Recebimento</th>
                                <th>Valor</th>
                                <th>Observações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($alvaras as $alvara): ?>
                                <tr>
                                    <td><?= formatarData($alvara['data_recebimento']) ?></td>
                                    <td><?= formatarValor($alvara['valor']) ?></td>
                                    <td>
                                        <?php
                                        if (!empty($alvara['observacoes'])) {
                                            // Se a observação contém "Utilizado no acordo #", substituir pelo número de repactuação
                                            if (strpos($alvara['observacoes'], 'Utilizado no acordo #') !== false && !empty($alvara['numero_repactuacao'])) {
                                                echo preg_replace('/Utilizado no acordo #\d+/', 'Utilizado no acordo #' . $alvara['numero_repactuacao'], $alvara['observacoes']);
                                            } else {
                                                echo $alvara['observacoes'];
                                            }
                                        } else {
                                            echo '-';
                                        }
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Alvarás</h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-0">Nenhum alvará registrado para este processo.</p>
            </div>
        </div>
    <?php endif; ?>

    <!-- Acordos -->
    <?php if (!empty($acordos)): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Acordos</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Data</th>
                                <th>Nº Repactuação</th>
                                <th>Valor Total</th>
                                <th>Status</th>
                                <th class="text-center">Ações</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($acordos as $acordo): ?>
                                <tr>
                                    <td><?= isset($acordo['data_acordo']) ? formatarData($acordo['data_acordo']) : '-' ?></td>
                                    <td><?= $acordo['numero_repactuacao'] ?? '-' ?></td>
                                    <td><?= isset($acordo['valor_total']) ? formatarValor($acordo['valor_total']) : formatarValor(0) ?></td>
                                    <td>
                                        <span class="badge bg-<?= isset($acordo['status_nome']) && isset($status_list[$acordo['status_nome']]) ? $status_list[$acordo['status_nome']] : 'secondary' ?>">
                                            <?= $acordo['status_nome'] ?? 'Não definido' ?>
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <button type="button" class="btn btn-sm" style="background-color: #00AE9D; color: white;" onclick="visualizarAcordo(<?= $acordo['id'] ?>)" title="Visualizar">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php else: ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">Acordos</h5>
            </div>
            <div class="card-body">
                <p class="text-muted mb-0">Nenhum acordo registrado para este processo.</p>
            </div>
        </div>
    <?php endif; ?>
</div>

<script>
$(document).ready(function() {
    // Inicializar tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();

    // Configura os modais
    $('.modal').on('hidden.bs.modal', function() {
        console.log('Modal fechado:', this.id);
        // Não reabrir o modal de edição automaticamente
    });
});
</script> 