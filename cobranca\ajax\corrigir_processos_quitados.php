<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    // Buscar IDs dos status
    $stmt = $pdo->prepare("SELECT id, nome FROM cbp_status_processo WHERE nome IN ('QUITADO', 'ACORDO JUDICIAL')");
    $stmt->execute();
    $status = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $status[$row['nome']] = $row['id'];
    }

    if (!isset($status['QUITADO']) || !isset($status['ACORDO JUDICIAL'])) {
        throw new Exception('Status QUITADO ou ACORDO JUDICIAL não encontrado');
    }

    $status_quitado_id = $status['QUITADO'];
    $status_acordo_judicial_id = $status['ACORDO JUDICIAL'];

    // Iniciar transação
    $pdo->beginTransaction();

    // Buscar processos com status QUITADO que têm acordos ativos não quitados
    $stmt = $pdo->prepare("
        SELECT DISTINCT p.id, p.numero_processo
        FROM cbp_processos_judiciais p
        INNER JOIN cbp_acordos a ON a.processo_id = p.id
        WHERE p.status_id = ?
        AND a.ativo = 1
        AND (
            EXISTS (
                SELECT 1 
                FROM cbp_parcelas_acordo pa 
                WHERE pa.acordo_id = a.id 
                AND pa.status != 'PAGO'
            )
            OR EXISTS (
                SELECT 1 
                FROM cbp_alvaras_acordo aa 
                WHERE aa.acordo_id = a.id 
                AND aa.status = 'PENDENTE'
            )
        )
    ");
    $stmt->execute([$status_quitado_id]);
    $processos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $processos_atualizados = [];

    foreach ($processos as $processo) {
        // Atualizar status do processo para ACORDO JUDICIAL
        $stmt = $pdo->prepare("
            UPDATE cbp_processos_judiciais 
            SET status_id = ?,
                updated_at = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$status_acordo_judicial_id, $processo['id']]);

        // Registrar no histórico
        $stmt = $pdo->prepare("
            INSERT INTO cbp_historico_status 
            (processo_id, status_id, data_alteracao, observacoes) 
            VALUES (?, ?, NOW(), ?)
        ");
        $stmt->execute([
            $processo['id'],
            $status_acordo_judicial_id,
            'Status atualizado de QUITADO para ACORDO JUDICIAL porque existem acordos não quitados'
        ]);

        $processos_atualizados[] = [
            'id' => $processo['id'],
            'numero_processo' => $processo['numero_processo']
        ];
    }

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'atualizados' => count($processos_atualizados),
        'processos' => $processos_atualizados
    ]);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 