<?php
/**
 * Script para corrigir a arquitetura de vínculos
 * O problema é que o sistema está misturando usuários locais com usuários da API
 */

require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

// Verificar se o usuário está logado
session_start();
if (!isset($_SESSION['user_id'])) {
    die("Acesso negado. Faça login para continuar.");
}

// Verificar se o usuário tem permissão para acessar a pasta ACD
if (!checkACDPermission($_SESSION['user_id'])) {
    die("Acesso negado. Você não tem permissão para acessar o sistema ACD.");
}

// Verificar se o usuário é administrador ou gestor
if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    die("Acesso negado. Apenas administradores e gestores podem executar esta correção.");
}

// Função para buscar usuários da API
function buscarTodosUsuarios() {
    $apiFields = [
        'api_user' => 'UFL7GXZ14LU9NOR',
        'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
        'api_module' => 'Usuarios',
        'api_action' => 'listarUsuarios'
    ];

    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => http_build_query($apiFields),
    ));
    
    $response = curl_exec($curl);
    
    if (curl_errno($curl)) {
        curl_close($curl);
        return [];
    }
    
    curl_close($curl);
    
    $usuarios = json_decode($response, true);
    
    if (!is_array($usuarios)) {
        return [];
    }
    
    return $usuarios;
}

echo "<h2>🔧 Correção da Arquitetura de Vínculos</h2>";
echo "<p>Este script corrige a confusão entre usuários locais e usuários da API.</p>";

try {
    echo "<h3>📋 Diagnóstico do Problema</h3>";
    
    // 1. Verificar estrutura atual
    $stmt = $pdo->query("DESCRIBE acd_usuario_pa");
    $colunas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $campos = array_column($colunas, 'Field');
    
    $tem_usuario_vinculo = in_array('usuario_vinculo', $campos);
    $tem_criado_por = in_array('criado_por', $campos);
    
    echo "<p><strong>Estrutura atual:</strong> " . implode(', ', $campos) . "</p>";
    
    if ($tem_usuario_vinculo) {
        echo "<p>⚠️ <strong>PROBLEMA IDENTIFICADO:</strong> Tabela usa estrutura antiga com referências a usuários locais.</p>";
        
        // 2. Buscar usuários da API
        echo "<h3>🌐 Buscando Usuários da API</h3>";
        $usuarios_api = buscarTodosUsuarios();
        echo "<p>Usuários encontrados na API: " . count($usuarios_api) . "</p>";
        
        // 3. Verificar usuários locais
        echo "<h3>🏠 Verificando Usuários Locais</h3>";
        $stmt = $pdo->query("SELECT COUNT(*) FROM usuarios");
        $usuarios_locais = $stmt->fetchColumn();
        echo "<p>Usuários na tabela local: $usuarios_locais</p>";
        
        // 4. Propor nova estrutura
        echo "<h3>🏗️ Nova Estrutura Proposta</h3>";
        echo "<p>A solução é criar uma nova estrutura que trabalhe diretamente com IDs da API:</p>";
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 10px 0;'>";
        echo "<h4>📋 Nova Estrutura da Tabela</h4>";
        echo "<pre>";
        echo "CREATE TABLE acd_usuario_pa_nova (\n";
        echo "    id INT AUTO_INCREMENT PRIMARY KEY,\n";
        echo "    usuario_api_id VARCHAR(50) NOT NULL,  -- ID do usuário da API\n";
        echo "    pa_id INT NOT NULL,                   -- ID do PA (local)\n";
        echo "    data_inicio DATE NOT NULL,\n";
        echo "    data_fim DATE NULL,\n";
        echo "    status ENUM('ativo', 'inativo') DEFAULT 'ativo',\n";
        echo "    criado_por INT NOT NULL,              -- Usuário local que criou\n";
        echo "    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n";
        echo "    desativado_por INT NULL,              -- Usuário local que desativou\n";
        echo "    desativado_em TIMESTAMP NULL,\n";
        echo "    observacoes TEXT NULL,\n";
        echo "    \n";
        echo "    INDEX idx_usuario_api_id (usuario_api_id),\n";
        echo "    INDEX idx_pa_id (pa_id),\n";
        echo "    INDEX idx_data_inicio (data_inicio),\n";
        echo "    INDEX idx_status (status),\n";
        echo "    \n";
        echo "    FOREIGN KEY (pa_id) REFERENCES pontos_atendimento(id),\n";
        echo "    FOREIGN KEY (criado_por) REFERENCES usuarios(id),\n";
        echo "    FOREIGN KEY (desativado_por) REFERENCES usuarios(id)\n";
        echo ");";
        echo "</pre>";
        echo "</div>";
        
        // 5. Opções de correção
        echo "<h3>🛠️ Opções de Correção</h3>";
        
        echo "<div class='row'>";
        
        // Opção 1: Correção Rápida
        echo "<div class='col-md-6'>";
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>⚡ Correção Rápida</h4>";
        echo "<p><strong>O que faz:</strong></p>";
        echo "<ul>";
        echo "<li>Remove a constraint problemática</li>";
        echo "<li>Corrige dados órfãos</li>";
        echo "<li>Mantém estrutura atual</li>";
        echo "</ul>";
        echo "<p><strong>Prós:</strong> Rápido, sem perda de dados</p>";
        echo "<p><strong>Contras:</strong> Não resolve o problema arquitetural</p>";
        echo "<p><a href='#' onclick='aplicarCorrecaoRapida()' class='btn btn-warning'>🩹 Aplicar Correção Rápida</a></p>";
        echo "</div>";
        echo "</div>";
        
        // Opção 2: Reestruturação Completa
        echo "<div class='col-md-6'>";
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>🏗️ Reestruturação Completa</h4>";
        echo "<p><strong>O que faz:</strong></p>";
        echo "<ul>";
        echo "<li>Cria nova estrutura otimizada</li>";
        echo "<li>Migra dados existentes</li>";
        echo "<li>Integra com API corretamente</li>";
        echo "</ul>";
        echo "<p><strong>Prós:</strong> Solução definitiva e robusta</p>";
        echo "<p><strong>Contras:</strong> Mais complexo, requer testes</p>";
        echo "<p><a href='#' onclick='aplicarReestruturacao()' class='btn btn-success'>🔄 Aplicar Reestruturação</a></p>";
        echo "</div>";
        echo "</div>";
        
        echo "</div>";
        
        // 6. Análise de impacto
        echo "<h3>📊 Análise de Impacto</h3>";
        
        // Verificar dados existentes
        $stmt = $pdo->query("SELECT COUNT(*) FROM acd_usuario_pa");
        $total_vinculos = $stmt->fetchColumn();
        
        if ($total_vinculos > 0) {
            echo "<p>⚠️ <strong>Dados existentes:</strong> $total_vinculos vínculos na tabela atual</p>";
            
            // Verificar dados órfãos
            $stmt = $pdo->query("
                SELECT COUNT(*) 
                FROM acd_usuario_pa 
                WHERE usuario_vinculo IS NOT NULL 
                AND usuario_vinculo NOT IN (SELECT id FROM usuarios)
            ");
            $orfaos = $stmt->fetchColumn();
            
            if ($orfaos > 0) {
                echo "<p>❌ <strong>Dados órfãos:</strong> $orfaos registros com usuario_vinculo inválido</p>";
            } else {
                echo "<p>✅ <strong>Dados válidos:</strong> Nenhum registro órfão encontrado</p>";
            }
        } else {
            echo "<p>✅ <strong>Tabela vazia:</strong> Nenhum dado existente, migração será simples</p>";
        }
        
    } else {
        echo "<p>✅ Estrutura parece estar correta ou já foi migrada.</p>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Erro durante o diagnóstico:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

?>

<script>
function aplicarCorrecaoRapida() {
    if (confirm('Aplicar correção rápida? Isso irá:\n\n- Remover constraint problemática\n- Corrigir dados órfãos\n- Manter estrutura atual\n\nContinuar?')) {
        window.location.href = 'corrigir_constraint_usuario_pa.php';
    }
}

function aplicarReestruturacao() {
    if (confirm('Aplicar reestruturação completa? Isso irá:\n\n- Criar nova estrutura otimizada\n- Migrar todos os dados\n- Integrar com API\n\nEsta operação é mais complexa. Continuar?')) {
        window.location.href = 'reestruturar_vinculos_api.php';
    }
}
</script>

<style>
.btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 4px 2px;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn:hover {
    opacity: 0.8;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: -5px;
}

.col-md-6 {
    flex: 0 0 50%;
    padding: 5px;
}

@media (max-width: 768px) {
    .col-md-6 {
        flex: 0 0 100%;
    }
}
</style>

<br><a href='gerenciar_vinculos.php'>← Voltar para Gerenciar Vínculos</a>
