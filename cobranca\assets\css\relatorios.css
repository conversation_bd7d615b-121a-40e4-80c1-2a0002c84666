/* Identidade visual baseada no projeto */
/* Identidade visual baseada no projeto */
.relatorios-container {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
    padding: 32px 24px;
    margin-top: 32px;
    font-family: Arial, sans-serif;
}

.relatorios-container .h2, .relatorios-container h1.page-title {
    color: var(--sicoob-verde-escuro, #003641);
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

.row.g-4 {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.relatorio-card {
    background: #f8f9fa;
    border-radius: 10px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.04);
    padding: 24px 18px 18px 18px;
    margin-bottom: 0;
    min-height: 220px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
}

.relatorio-card h2 {
    color: var(--sicoob-turquesa, #00AE9D);
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 18px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 4px;
    width: 100%;
}

.relatorio-links {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 8px;
    font-size: 0.98rem;
}

.btn-relatorio {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 34px;
    height: 34px;
    border-radius: 6px;
    font-size: 1.3rem;
    border: none;
    background: none;
    color: #2980b9;
    transition: background 0.2s, color 0.2s;
    margin-left: 2px;
}

.btn-relatorio.excel {
    color: #218838;
}
.btn-relatorio.pdf {
    color: #c0392b;
}
.btn-relatorio:hover {
    background: #eaf6f3;
    color: #1abc9c;
    text-decoration: none;
}

@media (max-width: 991px) {
    .row.g-4 { flex-direction: column; gap: 1.2rem; }
    .relatorio-card { min-height: 0; }
}
