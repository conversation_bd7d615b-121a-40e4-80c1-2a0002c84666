{"name": "composer/pcre", "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "type": "library", "license": "MIT", "keywords": ["pcre", "regex", "preg", "regular expression"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^8 || ^9", "phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-strict-rules": "^1 || ^2"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "autoload-dev": {"psr-4": {"Composer\\Pcre\\": "tests"}}, "extra": {"branch-alias": {"dev-main": "3.x-dev"}, "phpstan": {"includes": ["extension.neon"]}}, "scripts": {"test": "@php vendor/bin/phpunit", "phpstan": "@php phpstan analyse"}}