<?php
/**
 * Script para criar funções com histórico para estrutura antiga
 * Garante que cada PA tenha apenas UM responsável ativo
 */

require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

session_start();
if (!isset($_SESSION['user_id'])) {
    die("Acesso negado. Faça login para continuar.");
}

if (!checkACDPermission($_SESSION['user_id'])) {
    die("Acesso negado. Você não tem permissão para acessar o sistema ACD.");
}

if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    die("Acesso negado. Apenas administradores e gestores podem executar esta operação.");
}

echo "<h2>🔧 Criar Funções com Histórico (Um Responsável por PA)</h2>";
echo "<p>Este script cria funções que garantem apenas UM responsável ativo por PA.</p>";

try {
    echo "<h3>📋 Verificando Estrutura</h3>";
    
    // Verificar estrutura da tabela
    $stmt = $pdo->query("DESCRIBE acd_usuario_pa");
    $colunas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $campos = array_column($colunas, 'Field');
    
    echo "<p>Campos disponíveis: " . implode(', ', $campos) . "</p>";
    
    $tem_usuario_id = in_array('usuario_id', $campos);
    $tem_pa_id = in_array('pa_id', $campos);
    $tem_data_inicio = in_array('data_inicio', $campos);
    $tem_data_fim = in_array('data_fim', $campos);
    $tem_criado_por = in_array('criado_por', $campos);
    $tem_status = in_array('status', $campos);
    
    if (!$tem_usuario_id || !$tem_pa_id) {
        echo "<p>❌ Estrutura base não encontrada!</p>";
        exit;
    }
    
    echo "<p>✅ Estrutura base confirmada</p>";
    
    // Verificar problemas atuais
    echo "<h3>🔍 Verificando Problemas Atuais</h3>";
    
    $sql_duplicados = "
        SELECT 
            pa_id,
            COUNT(*) as total_responsaveis,
            GROUP_CONCAT(CONCAT(u.nome_completo, ' (ID:', v.usuario_id, ')') SEPARATOR ', ') as responsaveis
        FROM acd_usuario_pa v
        INNER JOIN usuarios u ON v.usuario_id = u.id
        WHERE (v.status = 'ativo' OR v.status = 1)
        AND (v.data_fim IS NULL OR v.data_fim > CURDATE())
        GROUP BY pa_id
        HAVING COUNT(*) > 1
    ";
    
    $stmt = $pdo->prepare($sql_duplicados);
    $stmt->execute();
    $duplicados = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($duplicados)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>⚠️ PAs com Múltiplos Responsáveis Detectados</h4>";
        echo "<p>Os seguintes PAs têm mais de um responsável ativo:</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'><th>PA ID</th><th>Total</th><th>Responsáveis</th></tr>";
        
        foreach ($duplicados as $dup) {
            echo "<tr>";
            echo "<td>{$dup['pa_id']}</td>";
            echo "<td style='color: red; font-weight: bold;'>{$dup['total_responsaveis']}</td>";
            echo "<td>{$dup['responsaveis']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        if (isset($_POST['corrigir_duplicados'])) {
            echo "<p>🔧 Corrigindo duplicados...</p>";
            
            foreach ($duplicados as $dup) {
                // Manter apenas o mais recente, desativar os outros
                $sql_corrigir = "
                    UPDATE acd_usuario_pa 
                    SET status = 'inativo',
                        data_fim = CURDATE(),
                        desativado_por = ?,
                        desativado_em = NOW(),
                        observacoes = CONCAT(IFNULL(observacoes, ''), ' | Desativado automaticamente - múltiplos responsáveis')
                    WHERE pa_id = ?
                    AND (status = 'ativo' OR status = 1)
                    AND id NOT IN (
                        SELECT id FROM (
                            SELECT id FROM acd_usuario_pa 
                            WHERE pa_id = ? 
                            AND (status = 'ativo' OR status = 1)
                            ORDER BY COALESCE(criado_em, data_vinculo) DESC 
                            LIMIT 1
                        ) as temp
                    )
                ";
                
                $stmt = $pdo->prepare($sql_corrigir);
                $stmt->execute([$_SESSION['user_id'], $dup['pa_id'], $dup['pa_id']]);
                $desativados = $stmt->rowCount();
                
                echo "<p>✅ PA {$dup['pa_id']}: $desativados responsáveis desativados</p>";
            }
            
            echo "<p>✅ Duplicados corrigidos!</p>";
        } else {
            echo "<form method='POST' style='margin: 10px 0;'>";
            echo "<button type='submit' name='corrigir_duplicados' class='btn btn-warning'>🔧 Corrigir Duplicados Automaticamente</button>";
            echo "</form>";
        }
    } else {
        echo "<p>✅ Nenhum PA com múltiplos responsáveis encontrado</p>";
    }
    
    echo "<h3>🔄 Criando Funções</h3>";
    
    if (isset($_POST['criar_funcoes'])) {
        // Fazer backup do arquivo atual
        if (file_exists('vinculos_functions.php')) {
            $backup_name = 'vinculos_functions_backup_' . date('Y_m_d_H_i_s') . '.php';
            copy('vinculos_functions.php', $backup_name);
            echo "<p>💾 Backup criado: $backup_name</p>";
        }
        
        // Criar funções com regra de um responsável por PA
        $funcoes_content = '<?php
/**
 * Funções para estrutura antiga com histórico
 * REGRA: Apenas UM responsável ativo por PA
 * Gerado automaticamente em ' . date('Y-m-d H:i:s') . '
 */

function buscarVinculosAtivos() {
    global $pdo;
    
    try {
        $sql = "
            SELECT 
                v.id,
                v.usuario_id,
                v.pa_id,
                COALESCE(v.data_inicio, DATE(v.data_vinculo), CURDATE()) as data_inicio,
                v.data_fim,
                u.nome_completo as usuario_nome,
                u.email as usuario_email,
                p.nome as pa_nome,
                p.numero as pa_numero,
                COALESCE(v.observacoes, \'\') as observacoes
            FROM acd_usuario_pa v
            INNER JOIN usuarios u ON v.usuario_id = u.id
            INNER JOIN pontos_atendimento p ON v.pa_id = p.id
            WHERE (v.status = \'ativo\' OR v.status = 1)
            AND (v.data_fim IS NULL OR v.data_fim > CURDATE())
            ORDER BY p.nome, u.nome_completo
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Erro em buscarVinculosAtivos: " . $e->getMessage());
        return [];
    }
}

function buscarPAsSemResponsavel() {
    global $pdo;
    
    try {
        $sql = "
            SELECT
                pa.id,
                pa.numero,
                pa.nome
            FROM pontos_atendimento pa
            LEFT JOIN acd_usuario_pa v ON (
                pa.id = v.pa_id
                AND (v.status = \'ativo\' OR v.status = 1)
                AND (v.data_fim IS NULL OR v.data_fim > CURDATE())
            )
            WHERE v.id IS NULL
            ORDER BY pa.numero
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Erro em buscarPAsSemResponsavel: " . $e->getMessage());
        return [];
    }
}

function criarVinculo($usuario_id, $pa_id, $data_inicio = null, $criado_por = null, $observacoes = null) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Verificar se o usuário existe
        $stmt = $pdo->prepare("SELECT id, nome_completo FROM usuarios WHERE id = ?");
        $stmt->execute([$usuario_id]);
        $usuario = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$usuario) {
            $pdo->rollBack();
            return [
                "sucesso" => false,
                "erro" => "Usuário não encontrado (ID: " . $usuario_id . ")"
            ];
        }
        
        // Verificar se o PA existe
        $stmt = $pdo->prepare("SELECT id, nome FROM pontos_atendimento WHERE id = ?");
        $stmt->execute([$pa_id]);
        $pa = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$pa) {
            $pdo->rollBack();
            return [
                "sucesso" => false,
                "erro" => "Ponto de Atendimento não encontrado (ID: " . $pa_id . ")"
            ];
        }
        
        // REGRA: Verificar se já existe responsável ativo para este PA
        $stmt = $pdo->prepare("
            SELECT v.id, u.nome_completo, v.data_inicio
            FROM acd_usuario_pa v
            INNER JOIN usuarios u ON v.usuario_id = u.id
            WHERE v.pa_id = ? 
            AND (v.status = \'ativo\' OR v.status = 1)
            AND (v.data_fim IS NULL OR v.data_fim > CURDATE())
        ");
        $stmt->execute([$pa_id]);
        $responsavel_atual = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($responsavel_atual) {
            // Se é o mesmo usuário, não fazer nada
            if ($responsavel_atual["usuario_id"] == $usuario_id) {
                $pdo->rollBack();
                return [
                    "sucesso" => false,
                    "erro" => "Este usuário já é responsável por este PA desde " . $responsavel_atual["data_inicio"]
                ];
            }
            
            // Desativar responsável atual
            $data_fim = $data_inicio ?? date("Y-m-d");
            $stmt = $pdo->prepare("
                UPDATE acd_usuario_pa 
                SET status = \'inativo\',
                    data_fim = ?,
                    desativado_por = ?,
                    desativado_em = NOW(),
                    observacoes = CONCAT(IFNULL(observacoes, \'\'), \' | Substituído por " . $usuario["nome_completo"] . "\')
                WHERE pa_id = ? 
                AND (status = \'ativo\' OR status = 1)
                AND (data_fim IS NULL OR data_fim > CURDATE())
            ");
            $stmt->execute([$data_fim, $criado_por ?? $_SESSION["user_id"] ?? 1, $pa_id]);
        }
        
        // Criar novo vínculo
        $campos_insert = ["usuario_id", "pa_id"];
        $valores = [$usuario_id, $pa_id];
        $placeholders = ["?", "?"];
        
        if (in_array("data_inicio", ' . json_encode($campos) . ')) {
            $campos_insert[] = "data_inicio";
            $valores[] = $data_inicio ?? date("Y-m-d");
            $placeholders[] = "?";
        }
        
        if (in_array("data_vinculo", ' . json_encode($campos) . ')) {
            $campos_insert[] = "data_vinculo";
            $valores[] = date("Y-m-d H:i:s");
            $placeholders[] = "?";
        }
        
        if (in_array("status", ' . json_encode($campos) . ')) {
            $campos_insert[] = "status";
            $valores[] = "ativo";
            $placeholders[] = "?";
        }
        
        if (in_array("criado_por", ' . json_encode($campos) . ')) {
            $campos_insert[] = "criado_por";
            $valores[] = $criado_por ?? $_SESSION["user_id"] ?? 1;
            $placeholders[] = "?";
        }
        
        if (in_array("criado_em", ' . json_encode($campos) . ')) {
            $campos_insert[] = "criado_em";
            $valores[] = date("Y-m-d H:i:s");
            $placeholders[] = "?";
        }
        
        if (in_array("observacoes", ' . json_encode($campos) . ') && $observacoes) {
            $campos_insert[] = "observacoes";
            $valores[] = $observacoes;
            $placeholders[] = "?";
        }
        
        $sql = "INSERT INTO acd_usuario_pa (" . implode(", ", $campos_insert) . ") VALUES (" . implode(", ", $placeholders) . ")";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($valores);
        
        $novo_vinculo_id = $pdo->lastInsertId();
        
        $pdo->commit();
        
        $mensagem = "Vínculo criado: " . $usuario["nome_completo"] . " → " . $pa["nome"];
        if ($responsavel_atual) {
            $mensagem .= " (substituindo " . $responsavel_atual["nome_completo"] . ")";
        }
        
        return [
            "sucesso" => true,
            "vinculo_id" => $novo_vinculo_id,
            "mensagem" => $mensagem
        ];
        
    } catch (Exception $e) {
        $pdo->rollBack();
        return [
            "sucesso" => false,
            "erro" => $e->getMessage()
        ];
    }
}

function desativarVinculo($vinculo_id, $data_fim = null, $desativado_por = null, $observacoes = null) {
    global $pdo;
    
    try {
        $campos_update = ["status = ?"];
        $valores = ["inativo"];
        
        if (in_array("data_fim", ' . json_encode($campos) . ')) {
            $campos_update[] = "data_fim = ?";
            $valores[] = $data_fim ?? date("Y-m-d");
        }
        
        if (in_array("desativado_por", ' . json_encode($campos) . ')) {
            $campos_update[] = "desativado_por = ?";
            $valores[] = $desativado_por ?? $_SESSION["user_id"] ?? 1;
        }
        
        if (in_array("desativado_em", ' . json_encode($campos) . ')) {
            $campos_update[] = "desativado_em = NOW()";
        }
        
        if (in_array("observacoes", ' . json_encode($campos) . ') && $observacoes) {
            $campos_update[] = "observacoes = CONCAT(IFNULL(observacoes, \'\'), \' | \', ?)";
            $valores[] = $observacoes;
        }
        
        $valores[] = $vinculo_id;
        
        $sql = "UPDATE acd_usuario_pa SET " . implode(", ", $campos_update) . " WHERE id = ?";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($valores);
        
        if ($stmt->rowCount() > 0) {
            return [
                "sucesso" => true,
                "mensagem" => "Vínculo desativado com sucesso"
            ];
        } else {
            return [
                "sucesso" => false,
                "erro" => "Vínculo não encontrado ou já inativo"
            ];
        }
        
    } catch (Exception $e) {
        return [
            "sucesso" => false,
            "erro" => $e->getMessage()
        ];
    }
}

function verificarResponsavelAtivo($pa_id) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT v.id, u.nome_completo, v.data_inicio
            FROM acd_usuario_pa v
            INNER JOIN usuarios u ON v.usuario_id = u.id
            WHERE v.pa_id = ? 
            AND (v.status = \'ativo\' OR v.status = 1)
            AND (v.data_fim IS NULL OR v.data_fim > CURDATE())
        ");
        $stmt->execute([$pa_id]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        return null;
    }
}

function buscarHistoricoPA($pa_id) {
    global $pdo;
    
    try {
        $sql = "
            SELECT 
                v.*,
                u.nome_completo as usuario_nome,
                uc.nome_completo as criado_por_nome,
                ud.nome_completo as desativado_por_nome
            FROM acd_usuario_pa v
            INNER JOIN usuarios u ON v.usuario_id = u.id
            LEFT JOIN usuarios uc ON v.criado_por = uc.id
            LEFT JOIN usuarios ud ON v.desativado_por = ud.id
            WHERE v.pa_id = ?
            ORDER BY COALESCE(v.data_inicio, v.data_vinculo) DESC, v.criado_em DESC
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$pa_id]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Erro em buscarHistoricoPA: " . $e->getMessage());
        return [];
    }
}
?>';
        
        // Salvar funções
        file_put_contents('vinculos_functions.php', $funcoes_content);
        echo "<p>✅ Funções criadas com regra de um responsável por PA!</p>";
        
        // Testar funções
        echo "<h3>🧪 Testando Funções</h3>";
        
        include 'vinculos_functions.php';
        
        // Teste 1: buscarVinculosAtivos
        echo "<p>🔍 Testando buscarVinculosAtivos()...</p>";
        try {
            $vinculos = buscarVinculosAtivos();
            echo "<p>✅ Sucesso: " . count($vinculos) . " vínculos ativos encontrados</p>";
        } catch (Exception $e) {
            echo "<p>❌ Erro: " . $e->getMessage() . "</p>";
        }
        
        // Teste 2: buscarPAsSemResponsavel
        echo "<p>🔍 Testando buscarPAsSemResponsavel()...</p>";
        try {
            $pas_sem_responsavel = buscarPAsSemResponsavel();
            echo "<p>✅ Sucesso: " . count($pas_sem_responsavel) . " PAs sem responsável</p>";
        } catch (Exception $e) {
            echo "<p>❌ Erro: " . $e->getMessage() . "</p>";
        }
        
        echo "<h3>✅ Funções Criadas com Sucesso!</h3>";
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>🎉 Sistema Configurado!</h4>";
        echo "<p>As funções agora garantem:</p>";
        echo "<ul>";
        echo "<li>✅ Apenas UM responsável ativo por PA</li>";
        echo "<li>✅ Histórico completo de alterações</li>";
        echo "<li>✅ Transições automáticas de responsabilidade</li>";
        echo "<li>✅ Auditoria de todas as operações</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<p><a href='gerenciar_vinculos.php' class='btn btn-success'>🔗 Testar Sistema</a></p>";
        
    } else {
        echo "<p>As funções implementarão as seguintes regras:</p>";
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>🔒 Regras de Negócio</h4>";
        echo "<ul>";
        echo "<li><strong>Um Responsável por PA:</strong> Cada PA pode ter apenas um responsável ativo</li>";
        echo "<li><strong>Transição Automática:</strong> Ao definir novo responsável, o anterior é desativado automaticamente</li>";
        echo "<li><strong>Histórico Completo:</strong> Todas as alterações são registradas</li>";
        echo "<li><strong>Auditoria:</strong> Quem fez e quando fez cada alteração</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<form method='POST'>";
        echo "<button type='submit' name='criar_funcoes' class='btn btn-primary'>🔧 Criar Funções</button>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Erro:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "<br><a href='gerenciar_vinculos.php'>← Voltar para Gerenciar Vínculos</a>";
?>

<style>
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    color: white; 
    text-decoration: none; 
    border-radius: 4px; 
    border: none;
    cursor: pointer;
    margin: 5px;
}
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-warning { background: #ffc107; color: #212529; }
.btn:hover { opacity: 0.8; }
table { font-size: 12px; }
th, td { padding: 8px; text-align: left; }
</style>
