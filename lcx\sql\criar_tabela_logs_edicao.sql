-- Script para criar tabela de logs de edição retroativa e alterações de usuários LCX
-- Esta tabela registrará todas as ações relacionadas a permissões e edições

USE sicoob_access_control;

-- Criar tabela de logs de edição retroativa
CREATE TABLE IF NOT EXISTS lcx_logs_edicao_retroativa (
    id INT AUTO_INCREMENT PRIMARY KEY,
    livro_caixa_id INT NOT NULL,
    usuario_id INT NOT NULL,
    acao ENUM('habilitou_geral', 'habilitou_especifica', 'desabilitou', 'editou_movimentacao') NOT NULL,
    data_especifica DATE NULL COMMENT 'Data específica quando ação for habilitou_especifica',
    movimentacao_id INT NULL COMMENT 'ID da movimentação quando ação for editou_movimentacao',
    detalhes JSON NULL COMMENT 'Detalhes adicionais da ação em formato JSON',
    ip_usuario VARCHAR(45) NULL COMMENT 'IP do usuário que executou a ação',
    user_agent TEXT NULL COMMENT 'User agent do navegador',
    data_acao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices para performance
    INDEX idx_livro_caixa (livro_caixa_id),
    INDEX idx_usuario (usuario_id),
    INDEX idx_acao (acao),
    INDEX idx_data_acao (data_acao),
    INDEX idx_data_especifica (data_especifica),
    
    -- Foreign keys
    FOREIGN KEY (livro_caixa_id) REFERENCES lcx_livros_caixa(id) ON DELETE CASCADE,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id),
    FOREIGN KEY (movimentacao_id) REFERENCES lcx_movimentacoes(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Criar tabela de logs de alterações de usuários LCX
CREATE TABLE IF NOT EXISTS lcx_logs_usuarios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_afetado_id INT NOT NULL COMMENT 'Usuário que foi alterado',
    usuario_executor_id INT NOT NULL COMMENT 'Usuário que executou a alteração',
    acao ENUM('criou_permissao', 'alterou_permissao', 'removeu_permissao', 'designou_tesoureiro', 'removeu_tesoureiro') NOT NULL,
    nivel_permissao_anterior VARCHAR(50) NULL,
    nivel_permissao_novo VARCHAR(50) NULL,
    ponto_atendimento_id INT NULL,
    detalhes JSON NULL COMMENT 'Detalhes da alteração em formato JSON',
    ip_usuario VARCHAR(45) NULL,
    user_agent TEXT NULL,
    data_acao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices para performance
    INDEX idx_usuario_afetado (usuario_afetado_id),
    INDEX idx_usuario_executor (usuario_executor_id),
    INDEX idx_acao (acao),
    INDEX idx_data_acao (data_acao),
    INDEX idx_ponto_atendimento (ponto_atendimento_id),
    
    -- Foreign keys
    FOREIGN KEY (usuario_afetado_id) REFERENCES usuarios(id),
    FOREIGN KEY (usuario_executor_id) REFERENCES usuarios(id),
    FOREIGN KEY (ponto_atendimento_id) REFERENCES pontos_atendimento(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Criar view para facilitar consultas de logs de edição retroativa
CREATE OR REPLACE VIEW vw_logs_edicao_retroativa AS
SELECT 
    l.id,
    l.acao,
    l.data_especifica,
    l.data_acao,
    l.ip_usuario,
    lc.nome as livro_nome,
    pa.nome as pa_nome,
    pa.numero as pa_numero,
    u.nome_completo as usuario_nome,
    u.usuario as usuario_login,
    m.valor as movimentacao_valor,
    m.tipo as movimentacao_tipo,
    l.detalhes
FROM lcx_logs_edicao_retroativa l
JOIN lcx_livros_caixa lc ON l.livro_caixa_id = lc.id
JOIN pontos_atendimento pa ON lc.ponto_atendimento_id = pa.id
JOIN usuarios u ON l.usuario_id = u.id
LEFT JOIN lcx_movimentacoes m ON l.movimentacao_id = m.id
ORDER BY l.data_acao DESC;

-- Criar view para facilitar consultas de logs de usuários
CREATE OR REPLACE VIEW vw_logs_usuarios_lcx AS
SELECT 
    l.id,
    l.acao,
    l.nivel_permissao_anterior,
    l.nivel_permissao_novo,
    l.data_acao,
    l.ip_usuario,
    ua.nome_completo as usuario_afetado_nome,
    ua.usuario as usuario_afetado_login,
    ue.nome_completo as usuario_executor_nome,
    ue.usuario as usuario_executor_login,
    pa.nome as pa_nome,
    pa.numero as pa_numero,
    l.detalhes
FROM lcx_logs_usuarios l
JOIN usuarios ua ON l.usuario_afetado_id = ua.id
JOIN usuarios ue ON l.usuario_executor_id = ue.id
LEFT JOIN pontos_atendimento pa ON l.ponto_atendimento_id = pa.id
ORDER BY l.data_acao DESC;

SELECT 'Tabelas de logs criadas com sucesso!' as resultado;
