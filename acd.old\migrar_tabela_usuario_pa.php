<?php
/**
 * Script para migrar a tabela acd_usuario_pa da estrutura antiga para a nova
 * com suporte a histórico de vínculos
 */

require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

// Verificar se o usuário está logado
session_start();
if (!isset($_SESSION['user_id'])) {
    die("Acesso negado. Faça login para continuar.");
}

// Verificar se o usuário tem permissão para acessar a pasta ACD
if (!checkACDPermission($_SESSION['user_id'])) {
    die("Acesso negado. Você não tem permissão para acessar o sistema ACD.");
}

// Verificar se o usuário é administrador ou gestor
if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    die("Acesso negado. Apenas administradores e gestores podem executar esta migração.");
}

echo "<h2>🔄 Migração da Tabela acd_usuario_pa</h2>";
echo "<p><strong>ATENÇÃO:</strong> Este script irá migrar a estrutura da tabela acd_usuario_pa.</p>";
echo "<p>Um backup será criado antes da migração.</p>";

try {
    $pdo->beginTransaction();
    
    echo "<h3>📋 Verificando estrutura atual...</h3>";
    
    // Verificar se a tabela existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'acd_usuario_pa'");
    $tabela_existe = $stmt->rowCount() > 0;
    
    if (!$tabela_existe) {
        echo "<p>✅ Tabela acd_usuario_pa não existe. Criando nova estrutura...</p>";
        
        // Criar nova estrutura diretamente
        $sql_criar = "
        CREATE TABLE acd_usuario_pa (
            id INT AUTO_INCREMENT PRIMARY KEY,
            usuario_id INT NOT NULL,
            pa_id INT NOT NULL,
            data_inicio DATE NOT NULL,
            data_fim DATE NULL,
            status ENUM('ativo', 'inativo') DEFAULT 'ativo',
            criado_por INT NOT NULL,
            criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            desativado_por INT NULL,
            desativado_em TIMESTAMP NULL,
            observacoes TEXT NULL,
            
            INDEX idx_usuario_id (usuario_id),
            INDEX idx_pa_id (pa_id),
            INDEX idx_data_inicio (data_inicio),
            INDEX idx_status (status),
            INDEX idx_criado_por (criado_por),
            INDEX idx_desativado_por (desativado_por),
            
            FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
            FOREIGN KEY (pa_id) REFERENCES pontos_atendimento(id) ON DELETE CASCADE,
            FOREIGN KEY (criado_por) REFERENCES usuarios(id),
            FOREIGN KEY (desativado_por) REFERENCES usuarios(id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($sql_criar);
        echo "<p>✅ Tabela criada com sucesso!</p>";
        
    } else {
        echo "<p>📊 Tabela acd_usuario_pa existe. Verificando estrutura...</p>";
        
        // Verificar se tem a coluna usuario_vinculo (estrutura antiga)
        $stmt = $pdo->query("SHOW COLUMNS FROM acd_usuario_pa LIKE 'usuario_vinculo'");
        $tem_usuario_vinculo = $stmt->rowCount() > 0;
        
        // Verificar se tem a coluna criado_por (estrutura nova)
        $stmt = $pdo->query("SHOW COLUMNS FROM acd_usuario_pa LIKE 'criado_por'");
        $tem_criado_por = $stmt->rowCount() > 0;
        
        if ($tem_usuario_vinculo && !$tem_criado_por) {
            echo "<p>🔄 Estrutura antiga detectada. Iniciando migração...</p>";
            
            // Contar registros existentes
            $stmt = $pdo->query("SELECT COUNT(*) FROM acd_usuario_pa");
            $total_registros = $stmt->fetchColumn();
            echo "<p>📊 Registros encontrados: $total_registros</p>";
            
            // Criar backup
            echo "<p>💾 Criando backup...</p>";
            $pdo->exec("DROP TABLE IF EXISTS acd_usuario_pa_backup");
            $pdo->exec("CREATE TABLE acd_usuario_pa_backup AS SELECT * FROM acd_usuario_pa");
            
            // Verificar backup
            $stmt = $pdo->query("SELECT COUNT(*) FROM acd_usuario_pa_backup");
            $backup_registros = $stmt->fetchColumn();
            echo "<p>✅ Backup criado: $backup_registros registros</p>";
            
            // Remover constraints antigas
            echo "<p>🔧 Removendo constraints antigas...</p>";
            try {
                $pdo->exec("ALTER TABLE acd_usuario_pa DROP FOREIGN KEY acd_usuario_pa_ibfk_3");
            } catch (Exception $e) {
                // Constraint pode não existir
            }
            
            // Remover tabela antiga
            echo "<p>🗑️ Removendo estrutura antiga...</p>";
            $pdo->exec("DROP TABLE acd_usuario_pa");
            
            // Criar nova estrutura
            echo "<p>🏗️ Criando nova estrutura...</p>";
            $sql_criar = "
            CREATE TABLE acd_usuario_pa (
                id INT AUTO_INCREMENT PRIMARY KEY,
                usuario_id INT NOT NULL,
                pa_id INT NOT NULL,
                data_inicio DATE NOT NULL,
                data_fim DATE NULL,
                status ENUM('ativo', 'inativo') DEFAULT 'ativo',
                criado_por INT NOT NULL,
                criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                desativado_por INT NULL,
                desativado_em TIMESTAMP NULL,
                observacoes TEXT NULL,
                
                INDEX idx_usuario_id (usuario_id),
                INDEX idx_pa_id (pa_id),
                INDEX idx_data_inicio (data_inicio),
                INDEX idx_status (status),
                INDEX idx_criado_por (criado_por),
                INDEX idx_desativado_por (desativado_por),
                
                FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
                FOREIGN KEY (pa_id) REFERENCES pontos_atendimento(id) ON DELETE CASCADE,
                FOREIGN KEY (criado_por) REFERENCES usuarios(id),
                FOREIGN KEY (desativado_por) REFERENCES usuarios(id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            $pdo->exec($sql_criar);
            echo "<p>✅ Nova estrutura criada!</p>";
            
            // Migrar dados
            echo "<p>📦 Migrando dados...</p>";
            $sql_migrar = "
            INSERT INTO acd_usuario_pa (
                usuario_id, 
                pa_id, 
                data_inicio, 
                status, 
                criado_por, 
                criado_em,
                observacoes
            )
            SELECT 
                b.usuario_id,
                b.pa_id,
                COALESCE(DATE(b.data_vinculo), CURDATE()) as data_inicio,
                CASE 
                    WHEN b.status = 1 THEN 'ativo'
                    ELSE 'inativo'
                END as status,
                CASE 
                    WHEN b.usuario_vinculo IS NOT NULL AND EXISTS(SELECT 1 FROM usuarios WHERE id = b.usuario_vinculo) 
                    THEN b.usuario_vinculo
                    ELSE 1
                END as criado_por,
                COALESCE(b.data_vinculo, NOW()) as criado_em,
                'Migrado da estrutura anterior' as observacoes
            FROM acd_usuario_pa_backup b
            WHERE EXISTS (SELECT 1 FROM usuarios WHERE id = b.usuario_id)
            AND EXISTS (SELECT 1 FROM pontos_atendimento WHERE id = b.pa_id)";
            
            $stmt = $pdo->prepare($sql_migrar);
            $stmt->execute();
            $migrados = $stmt->rowCount();
            
            echo "<p>✅ Dados migrados: $migrados registros</p>";
            
        } elseif ($tem_criado_por) {
            echo "<p>✅ Estrutura já está atualizada!</p>";
        } else {
            echo "<p>⚠️ Estrutura não reconhecida. Verificação manual necessária.</p>";
        }
    }
    
    // Verificar resultado final
    echo "<h3>📊 Verificação Final</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) FROM acd_usuario_pa");
    $total_final = $stmt->fetchColumn();
    echo "<p>Total de registros na tabela: $total_final</p>";
    
    // Mostrar estrutura atual
    echo "<h4>🏗️ Estrutura Atual:</h4>";
    $stmt = $pdo->query("DESCRIBE acd_usuario_pa");
    $colunas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($colunas as $coluna) {
        echo "<tr>";
        echo "<td>{$coluna['Field']}</td>";
        echo "<td>{$coluna['Type']}</td>";
        echo "<td>{$coluna['Null']}</td>";
        echo "<td>{$coluna['Key']}</td>";
        echo "<td>{$coluna['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    $pdo->commit();
    echo "<h3>✅ Migração concluída com sucesso!</h3>";
    echo "<p>A tabela acd_usuario_pa agora suporta histórico de vínculos.</p>";
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo "<h3>❌ Erro durante a migração:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p>A transação foi revertida. Nenhuma alteração foi feita.</p>";
}

echo "<br><a href='gerenciar_vinculos.php'>← Voltar para Gerenciar Vínculos</a>";
?>
