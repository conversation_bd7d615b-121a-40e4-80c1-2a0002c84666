<?php
class Permissions {
    private $pdo;
    private $user_id;
    private $nivel_acesso;
    private $setor_id;

    public function __construct($pdo, $user_id) {
        $this->pdo = $pdo;
        $this->user_id = $user_id;
        $this->loadUserInfo();
    }

    private function loadUserInfo() {
        $stmt = $this->pdo->prepare("
            SELECT u.nivel_acesso_id, us.setor_id 
            FROM usuarios u 
            LEFT JOIN usuario_setor us ON u.id = us.usuario_id 
            WHERE u.id = ?
        ");
        $stmt->execute([$this->user_id]);
        $info = $stmt->fetch();
        
        $this->nivel_acesso = $info['nivel_acesso_id'];
        $this->setor_id = $info['setor_id'];
    }

    public function isAdmin() {
        return $this->nivel_acesso == 1; // 1 = Administrador
    }

    public function isGestor() {
        return $this->nivel_acesso == 2; // 2 = Gestor
    }

    public function canManageUsers() {
        return in_array($this->nivel_acesso, [1, 2]); // Admin e Gestor
    }

    public function canManageCards() {
        return in_array($this->nivel_acesso, [1, 2]); // Admin e Gestor
    }

    public function canManageSetores() {
        return $this->nivel_acesso == 1; // Apenas Admin
    }

    public function getSetor() {
        return $this->setor_id;
    }

    public function canAccessSetor($setor_id) {
        if ($this->isAdmin()) return true;
        if ($this->isGestor() && $this->setor_id == $setor_id) return true;
        return $this->setor_id == $setor_id;
    }
} 