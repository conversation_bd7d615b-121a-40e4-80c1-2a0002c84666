<?php
session_start();
require_once '../config/database.php';
require_once 'functions/logs.php';
require_once 'funcoes_dias_uteis.php';
require_once 'funcoes_divergencia_saldos.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user_id = $_SESSION['user_id'];
$livro_id = intval($_GET['id'] ?? 0);

if (!$livro_id) {
    header('Location: index.php?erro=livro_nao_encontrado');
    exit;
}

// Verificar permissões LCX do usuário
try {
    $stmt = $pdo->prepare("SELECT nivel_permissao FROM lcx_permissoes WHERE usuario_id = ? AND ativo = 1");
    $stmt->execute([$user_id]);
    $permissao_lcx = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$permissao_lcx) {
        log_acesso_negado($pdo, 'visualizar_livro.php', 'Usuário sem permissão LCX');
        header('Location: ../dashboard.php?erro=sem_permissao_lcx');
        exit;
    }
    
    $nivel_permissao = $permissao_lcx['nivel_permissao'];

    // Registrar acesso ao sistema LCX
    log_acesso_lcx($pdo, 'visualizar_livro.php?id=' . ($_GET['id'] ?? 'N/A'));

} catch (Exception $e) {
    error_log("Erro ao verificar permissões LCX: " . $e->getMessage());
    header('Location: ../dashboard.php?erro=erro_sistema');
    exit;
}

// Buscar dados do livro caixa
try {
    $sql = "
        SELECT lc.*, pa.nome as pa_nome, pa.numero as pa_numero,
               u.nome_completo as criado_por_nome,
               uf.nome_completo as fechado_por_nome
        FROM lcx_livros_caixa lc
        JOIN pontos_atendimento pa ON lc.ponto_atendimento_id = pa.id
        JOIN usuarios u ON lc.criado_por = u.id
        LEFT JOIN usuarios uf ON lc.fechado_por = uf.id
        WHERE lc.id = ?
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$livro_id]);
    $livro = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$livro) {
        header('Location: index.php?erro=livro_nao_encontrado');
        exit;
    }
} catch (Exception $e) {
    error_log("Erro ao buscar livro: " . $e->getMessage());
    header('Location: index.php?erro=erro_sistema');
    exit;
}

// Verificar se o usuário tem acesso a este livro
$tem_acesso = false;
$eh_tesoureiro_pa = false;

// TODOS os usuários (incluindo admin) devem verificar se são tesoureiros do PA
try {
    $stmt = $pdo->prepare("
        SELECT id FROM lcx_tesoureiros_pa
        WHERE usuario_id = ? AND ponto_atendimento_id = ? AND ativo = 1
    ");
    $stmt->execute([$user_id, $livro['ponto_atendimento_id']]);
    $eh_tesoureiro_pa = $stmt->rowCount() > 0;

    if ($nivel_permissao === 'admin') {
        // Admin tem acesso para visualizar todos os livros, mas só pode editar se for tesoureiro
        $tem_acesso = true;
    } else {
        // Outros usuários só têm acesso se forem tesoureiros do PA
        $tem_acesso = $eh_tesoureiro_pa;
    }
} catch (Exception $e) {
    error_log("Erro ao verificar acesso ao livro: " . $e->getMessage());
}

if (!$tem_acesso) {
    header('Location: index.php?erro=sem_acesso_livro');
    exit;
}

// Parâmetros de filtro e paginação para movimentações
$filtro_tipo = $_GET['filtro_tipo'] ?? '';
$filtro_data_inicio = $_GET['filtro_data_inicio'] ?? '';
$filtro_data_fim = $_GET['filtro_data_fim'] ?? '';
$por_pagina = 20;

// Construir filtros para movimentações
$where_conditions = ["m.livro_caixa_id = ?"];
$params = [$livro_id];

if (!empty($filtro_tipo)) {
    $where_conditions[] = "m.tipo = ?";
    $params[] = $filtro_tipo;
}

if (!empty($filtro_data_inicio)) {
    $where_conditions[] = "DATE(m.data_movimentacao) >= ?";
    $params[] = $filtro_data_inicio;
}

if (!empty($filtro_data_fim)) {
    $where_conditions[] = "DATE(m.data_movimentacao) <= ?";
    $params[] = $filtro_data_fim;
}

$where_clause = implode(" AND ", $where_conditions);

// Contar total de movimentações
try {
    $count_sql = "SELECT COUNT(*) as total FROM lcx_movimentacoes m WHERE {$where_clause}";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_movimentacoes = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    $total_paginas = max(1, ceil($total_movimentacoes / $por_pagina));
} catch (Exception $e) {
    error_log("Erro ao contar movimentações: " . $e->getMessage());
    $total_movimentacoes = 0;
    $total_paginas = 1;
}

// Definir página atual: se não informado, mostrar a última página
if (isset($_GET['pagina'])) {
    $pagina = max(1, intval($_GET['pagina']));
} else {
    $pagina = $total_paginas;
}
$offset = ($pagina - 1) * $por_pagina;

// Buscar movimentações
try {
    $sql_movimentacoes = "
        SELECT m.*, u.nome_completo as criado_por_nome, u.username as criado_por_username,
               ue.nome_completo as editado_por_nome, ue.username as editado_por_username
        FROM lcx_movimentacoes m
        JOIN usuarios u ON m.criado_por = u.id
        LEFT JOIN usuarios ue ON m.editado_por = ue.id
        WHERE {$where_clause}
        ORDER BY m.data_competencia ASC, m.data_movimentacao ASC, m.id ASC
        LIMIT {$por_pagina} OFFSET {$offset}
    ";
    
    $stmt = $pdo->prepare($sql_movimentacoes);
    $stmt->execute($params);
    $movimentacoes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    error_log("Erro ao buscar movimentações: " . $e->getMessage());
    $movimentacoes = [];
}

// Buscar dados do usuário logado
try {
    $stmt = $pdo->prepare("SELECT nome_completo, email FROM usuarios WHERE id = ?");
    $stmt->execute([$user_id]);
    $usuario_logado = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    error_log("Erro ao buscar dados do usuário: " . $e->getMessage());
    header('Location: ../login.php');
    exit;
}

// Buscar pontos de atendimento para livros master
$pontos_atendimento = [];
if ($livro['tipo'] === 'master') {
    try {
        $stmt = $pdo->query("SELECT id, nome, numero FROM pontos_atendimento WHERE ativo = 1 ORDER BY nome");
        $pontos_atendimento = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Erro ao buscar pontos de atendimento: " . $e->getMessage());
        $pontos_atendimento = [];
    }
}

// Buscar equipamentos ATM/ATMR do PA deste livro
$equipamentos_atm = [];
try {
    $stmt = $pdo->prepare("
        SELECT id, numero, tipo, localizacao, status
        FROM lcx_atm_atmr
        WHERE ponto_atendimento_id = ? AND status = 'ativo'
        ORDER BY tipo, numero
    ");
    $stmt->execute([$livro['ponto_atendimento_id']]);
    $equipamentos_atm = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    error_log("Erro ao buscar equipamentos ATM/ATMR: " . $e->getMessage());
    $equipamentos_atm = [];
}

// Verificar divergência de saldo (apenas para livros de agência) - DESABILITADO
$divergencia_saldo = null;
// if ($livro['tipo'] === 'normal') {
//     $divergencia_saldo = verificarDivergenciaSaldo($pdo, $livro_id, $livro['ponto_atendimento_id']);
// }

// Sistema de controle de dias removido - pode registrar APENAS se livro estiver aberto E for tesoureiro do PA E não atingiu 98 folhas
//$info_dia = null;
$ano_abertura = date('Y', strtotime($livro['data_abertura'] ?? $livro['created_at']));
$ano_atual = date('Y');
$hoje = date('Y-m-d');
$ultimo_dia_ano = $ano_abertura . '-12-31';

// Permitir fechar se o livro está aberto, já passou de 31/12 do ano de abertura e usuário é admin
$pode_fechar = ($livro['status'] === 'aberto') && ($hoje > $ultimo_dia_ano) && ($nivel_permissao === 'admin');

// Calcular estatísticas
$total_entradas = 0;
$total_saidas = 0;
try {
    $stmt = $pdo->prepare("
        SELECT
            SUM(CASE WHEN tipo = 'entrada' THEN valor ELSE 0 END) as total_entradas,
            SUM(CASE WHEN tipo = 'saida' THEN valor ELSE 0 END) as total_saidas
        FROM lcx_movimentacoes
        WHERE livro_caixa_id = ?
    ");
    $stmt->execute([$livro_id]);
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    $total_entradas = floatval($stats['total_entradas'] ?? 0);
    $total_saidas = floatval($stats['total_saidas'] ?? 0);
} catch (Exception $e) {
    error_log("Erro ao calcular estatísticas: " . $e->getMessage());
}

// Buscar status do dia atual do livro
$info_dia = getInfoDiaLivro($livro_id, $pdo);
$permite_edicao_data = $info_dia['permite_edicao_data'] ?? 0;
$hoje = date('Y-m-d');
$dia_hoje_aberto = ($info_dia && $info_dia['dia_aberto'] === $hoje && $info_dia['status_dia'] === 'aberto');
$dia_hoje_fechado = ($info_dia && $info_dia['dia_aberto'] === $hoje && $info_dia['status_dia'] === 'fechado');
$sem_dia_hoje = (!$info_dia || $info_dia['dia_aberto'] !== $hoje);

// NOVO: Buscar se já houve abertura hoje (mesmo que esteja fechado)
$primeira_abertura_hoje = null;
try {
    $stmt = $pdo->prepare("SELECT data_abertura FROM lcx_controle_dias WHERE livro_caixa_id = ? AND data_dia = ? LIMIT 1");
    $stmt->execute([$livro_id, $hoje]);
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($row && $row['data_abertura']) {
        $primeira_abertura_hoje = $row['data_abertura'];
    }
} catch (Exception $e) {
    $primeira_abertura_hoje = null;
}

// NOVO: Buscar histórico de aberturas/fechamentos/reaberturas do dia
$historico_dia_hoje = [];
try {
    $stmt = $pdo->prepare("SELECT l.*, u.nome_completo FROM lcx_logs_dias l JOIN usuarios u ON l.usuario_id = u.id WHERE l.livro_caixa_id = ? AND l.data_dia = ? ORDER BY l.data_acao ASC");
    $stmt->execute([$livro_id, $hoje]);
    $historico_dia_hoje = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $historico_dia_hoje = [];
}

// Definir $pode_registrar corretamente agora
$pode_registrar = ($livro['status'] === 'aberto') && $eh_tesoureiro_pa && $dia_hoje_aberto;

// ALERTA DE DIVERGÊNCIA DE SALDO PARA LIVRO MASTER
if ($livro['tipo'] === 'master') {
    // PA selecionado para lançamento (padrão: primeiro PA da lista)
    $pa_id = null;
    if (isset($_GET['pa_id'])) {
        $pa_id = intval($_GET['pa_id']);
    } elseif (!empty($pontos_atendimento)) {
        $pa_id = $pontos_atendimento[0]['id'];
    }
    $divergencia_saldo = null;
    if ($pa_id) {
        // Buscar livro aberto do PA
        $stmt = $pdo->prepare("SELECT id FROM lcx_livros_caixa WHERE ponto_atendimento_id = ? AND tipo = 'normal' AND status = 'aberto' LIMIT 1");
        $stmt->execute([$pa_id]);
        $livro_agencia = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($livro_agencia) {
            $divergencia_saldo = verificarDivergenciaSaldo($pdo, $livro_agencia['id'], $pa_id);
        }
    }
}

// Para livro master: identificar o último lançamento de cada PA exibido na página
$ultimo_lancamento_pa = [];
if ($livro['tipo'] === 'master') {
    foreach ($movimentacoes as $idx => $mov) {
        if (!empty($mov['categoria']) && preg_match('/\(PA: (\d+)\)/', $mov['categoria'], $match_pa)) {
            $pa_id = (int)$match_pa[1];
            $ultimo_lancamento_pa[$pa_id] = $idx;
        }
    }
}

// Card de divergência master x PA
$divergencias_por_pa = [];
$total_pas_com_divergencia = 0;
$data_divergencia = $_GET['data_divergencia'] ?? date('Y-m-d'); // Data para verificar divergência

if ($livro['tipo'] === 'master') {
    // Buscar todos os PAs ativos
    $stmt = $pdo->query("SELECT id, nome, numero FROM pontos_atendimento WHERE ativo = 1 ORDER BY nome");
    $todos_pas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($todos_pas as $pa) {
        // Buscar último livro do PA (aberto ou fechado)
        $stmt_livro = $pdo->prepare("SELECT id FROM lcx_livros_caixa WHERE ponto_atendimento_id = ? AND tipo = 'normal' ORDER BY status = 'aberto' DESC, data_fechamento DESC, id DESC LIMIT 1");
        $stmt_livro->execute([$pa['id']]);
        $livro_pa = $stmt_livro->fetch(PDO::FETCH_ASSOC);
        if ($livro_pa) {
            // Usar a nova função que verifica divergência por data
            $div = verificarDivergenciaSaldoPorData($pdo, $livro_pa['id'], $pa['id'], $data_divergencia);
            $tem_divergencia = $div && $div['tem_divergencia'];
            if ($tem_divergencia) {
                $total_pas_com_divergencia++;
                // Só adiciona ao array se tiver divergência
                $divergencias_por_pa[] = [
                    'pa_nome' => $pa['nome'],
                    'pa_numero' => $pa['numero'],
                    'tem_divergencia' => $tem_divergencia,
                    'diferenca' => $div['diferenca'] ?? 0,
                    'saldo_esperado' => $div['saldo_esperado'] ?? null,
                    'saldo_agencia' => $div['saldo_agencia'] ?? null,
                    'data_referencia' => $div['data_referencia'] ?? $data_divergencia,
                ];
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($livro['nome']); ?> - Sicoob Livro Caixa</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            /* Paleta de Cores Oficial Sicoob 2024 - Manual de Marca */
            --sicoob-turquesa: #00A091;        /* RGB: 0, 160, 145 - Cor principal */
            --sicoob-verde-escuro: #003641;    /* RGB: 0, 54, 65 - Verde escuro */
            --sicoob-verde-medio: #7DB61C;     /* RGB: 125, 182, 28 - Verde médio */
            --sicoob-verde-claro: #C9D200;     /* RGB: 201, 210, 0 - Verde claro */
            --sicoob-roxo: #49479D;            /* RGB: 73, 71, 157 - Roxo */
            --sicoob-branco: #FFFFFF;          /* RGB: 255, 255, 255 - Branco */

            /* Cores de Sistema baseadas na identidade oficial */
            --primary-color: var(--sicoob-turquesa);
            --secondary-color: var(--sicoob-verde-escuro);
            --accent-color: var(--sicoob-verde-medio);
            --accent-light: var(--sicoob-verde-claro);
            --accent-purple: var(--sicoob-roxo);
            --success-color: var(--sicoob-verde-medio);
            --warning-color: var(--sicoob-verde-claro);
            --danger-color: #D32F2F;
            --info-color: var(--sicoob-turquesa);
            --dark-color: var(--sicoob-verde-escuro);
            --light-color: #F8FFFE;
            --white-color: var(--sicoob-branco);
            --gray-color: #6B7280;
        }

        /* Layout e Estrutura - Identidade Visual Sicoob Oficial */
        body {
            background: linear-gradient(135deg, var(--light-color) 0%, rgba(0, 160, 145, 0.03) 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            position: relative;
        }

        /* Elemento gráfico sutil de fundo inspirado nos padrões oficiais Sicoob */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            right: 0;
            width: 300px;
            height: 100vh;
            background: linear-gradient(60deg,
                transparent 0%,
                rgba(0, 160, 145, 0.015) 20%,
                rgba(125, 182, 28, 0.01) 40%,
                rgba(201, 210, 0, 0.008) 60%,
                rgba(73, 71, 157, 0.005) 80%,
                transparent 100%);
            z-index: -1;
            opacity: 0.7;
        }

        /* Navbar - Identidade Visual Oficial Sicoob */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 4px 20px rgba(0, 54, 65, 0.15);
            border-bottom: 3px solid var(--accent-color);
            position: relative;
        }

        /* Padrão gráfico inspirado nos grafismos oficiais Sicoob */
        .navbar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 100%;
            background: linear-gradient(60deg,
                transparent 30%,
                rgba(125, 182, 28, 0.12) 45%,
                rgba(201, 210, 0, 0.08) 55%,
                rgba(73, 71, 157, 0.05) 70%,
                transparent 85%);
            opacity: 0.8;
        }

        .navbar-brand {
            font-size: 1.6rem;
            font-weight: 700;
            color: var(--white-color) !important;
        }

        .navbar-brand .accent-text {
            color: var(--accent-light);
            font-weight: 400;
        }

        .navbar-brand i {
            color: var(--accent-color);
            margin-right: 8px;
        }

        .user-info {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 10px 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--white-color);
            font-weight: 500;
        }

        /* Page Header - Estilo Sicoob Compacto */
        .page-header {
            background: linear-gradient(135deg, var(--white-color) 0%, rgba(0, 160, 145, 0.02) 100%);
            border-radius: 12px;
            padding: 20px 25px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0, 54, 65, 0.06);
            border-left: 4px solid var(--primary-color);
            position: relative;
        }

        .page-header h1 {
            color: var(--secondary-color);
            font-weight: 700;
            margin-bottom: 8px;
            font-size: 1.5rem;
        }

        .page-header .subtitle {
            color: var(--gray-color);
            font-size: 0.9rem;
            margin-bottom: 0;
        }

        /* Cards - Design System Sicoob Oficial */
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 3px 12px rgba(0, 54, 65, 0.06);
            overflow: hidden;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            position: relative;
        }

        /* Elemento gráfico inspirado nos padrões oficiais */
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg,
                var(--sicoob-turquesa) 0%,
                var(--sicoob-verde-medio) 33%,
                var(--sicoob-verde-claro) 66%,
                var(--sicoob-roxo) 100%);
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 54, 65, 0.12);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white-color);
            border-radius: 12px 12px 0 0 !important;
            border: none;
            padding: 12px 20px;
            font-weight: 600;
            position: relative;
        }

        /* Grafismo sutil no header dos cards - Padrão Sicoob */
        .card-header::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100%;
            background: linear-gradient(60deg,
                transparent 20%,
                rgba(255,255,255,0.12) 40%,
                rgba(255,255,255,0.08) 60%,
                transparent 80%);
        }

        .card-header h5, .card-header h6 {
            margin: 0;
            font-weight: 600;
            font-size: 0.95rem;
            position: relative;
            z-index: 2;
        }

        /* Garantir que elementos interativos no card-header fiquem acima do pseudo-elemento */
        .card-header form, .card-header .btn, .card-header input, .card-header select {
            position: relative;
            z-index: 3;
            pointer-events: auto;
        }

        /* Garantir que botões sejam sempre clicáveis */
        .card-header .btn {
            cursor: pointer;
        }

        .card-body {
            padding: 15px 20px;
        }

        .card-body.py-3 {
            padding: 10px 20px;
        }

        /* Botões - Padrão Oficial Sicoob */
        .btn {
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        /* Efeito de hover inspirado nos padrões gráficos */
        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: var(--white-color);
            box-shadow: 0 2px 8px rgba(0, 160, 145, 0.15);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 160, 145, 0.25);
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
            color: var(--white-color);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--accent-color) 0%, var(--primary-color) 100%);
            border: none;
            color: var(--white-color);
            box-shadow: 0 2px 8px rgba(125, 182, 28, 0.15);
        }

        .btn-success:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(125, 182, 28, 0.25);
            color: var(--white-color);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #B71C1C 100%);
            border: none;
            color: var(--white-color);
            box-shadow: 0 2px 8px rgba(211, 47, 47, 0.15);
        }

        .btn-danger:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(211, 47, 47, 0.25);
            color: var(--white-color);
        }

        .btn-outline-secondary {
            border: 1px solid var(--gray-color);
            color: var(--gray-color);
            background: transparent;
        }

        .btn-outline-secondary:hover {
            background: var(--gray-color);
            color: var(--white-color);
            transform: translateY(-1px);
        }

        /* Botões outline com cores oficiais Sicoob */
        .btn-outline-primary {
            color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
        }

        .btn-outline-primary:hover,
        .btn-outline-primary:focus,
        .btn-outline-primary.active {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
            color: white;
            transform: translateY(-1px);
        }

        .btn-outline-success {
            color: var(--sicoob-verde-medio);
            border-color: var(--sicoob-verde-medio);
        }

        .btn-outline-success:hover,
        .btn-outline-success:focus,
        .btn-outline-success.active {
            background-color: var(--sicoob-verde-medio);
            border-color: var(--sicoob-verde-medio);
            color: white;
            transform: translateY(-1px);
        }

        .btn-outline-danger {
            color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .btn-outline-danger:hover,
        .btn-outline-danger:focus,
        .btn-outline-danger.active {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            color: white;
            transform: translateY(-1px);
        }

        /* Badges e Elementos Compactos */
        .badge-sm {
            font-size: 0.7rem;
            padding: 0.25em 0.5em;
        }

        .table-sm td, .table-sm th {
            padding: 0.5rem 0.75rem;
            vertical-align: middle;
        }

        .text-truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Espaçamentos compactos */
        .gap-2 {
            gap: 0.5rem !important;
        }

        .mb-15 {
            margin-bottom: 15px;
        }

        /* Animações */
        .fade-in {
            animation: fadeIn 0.4s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Card de informações consolidadas */
        .border-start {
            border-left: 2px solid var(--primary-color) !important;
        }

        /* Cores de texto oficiais Sicoob */
        .text-primary {
            color: var(--sicoob-turquesa) !important;
        }

        .text-success {
            color: var(--sicoob-verde-medio) !important;
        }

        .text-info {
            color: var(--sicoob-turquesa) !important;
        }

        .text-warning {
            color: var(--sicoob-verde-claro) !important;
        }

        .text-danger {
            color: var(--danger-color) !important;
        }

        /* Badges com cores oficiais Sicoob */
        .bg-success {
            background-color: var(--sicoob-verde-medio) !important;
        }

        .bg-info {
            background-color: var(--sicoob-turquesa) !important;
        }

        .bg-warning {
            background-color: var(--sicoob-verde-claro) !important;
            color: var(--sicoob-verde-escuro) !important;
        }

        .bg-primary {
            background-color: var(--sicoob-turquesa) !important;
        }

        .d-flex.flex-column.align-items-center {
            min-height: 60px;
            justify-content: center;
        }

        .d-flex.flex-column.align-items-center i {
            font-size: 1.2rem;
        }

        .d-flex.flex-column.align-items-center small {
            font-size: 0.75rem;
            margin-bottom: 2px;
        }

        .d-flex.flex-column.align-items-center strong {
            font-size: 0.9rem;
            line-height: 1.2;
        }

        /* Responsividade melhorada */
        @media (max-width: 768px) {
            .page-header h1 {
                font-size: 1.25rem;
            }

            .card-body {
                padding: 12px 15px;
            }

            .btn {
                padding: 6px 12px;
                font-size: 0.8rem;
            }

            .col-md-8, .col-md-4 {
                margin-bottom: 15px;
            }

            .border-start {
                border-left: none !important;
                border-top: 2px solid var(--primary-color) !important;
                padding-top: 15px !important;
                padding-left: 0 !important;
            }
        }

        .lcx-dia-actions .btn {
            min-width: 150px;
            font-size: 0.92em;
            padding-left: 0.75em;
            padding-right: 0.75em;
        }
        @media (max-width: 600px) {
          .lcx-dia-actions .btn {
            min-width: 120px;
            font-size: 0.85em;
            padding-left: 0.5em;
            padding-right: 0.5em;
          }
        }
        .btn-sicoob-blue {
            background: #00A091;
            color: #fff;
            border: none;
        }
        .btn-sicoob-blue:hover, .btn-sicoob-blue:focus {
            background: #007b8f;
            color: #fff;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-book me-2"></i>
                <span>Sicoob</span>
                <span class="accent-text"> Livro Caixa</span>
            </a>

            <div class="navbar-nav ms-auto d-flex align-items-center">
                <!-- Informações do Usuário -->
                <div class="user-info">
                    <i class="fas fa-user-circle me-2"></i>
                    <span><?php echo explode(' ', $usuario_logado['nome_completo'])[0]; ?></span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <?php if ($livro['tipo'] === 'master'): ?>
        <div class="card mb-3 fade-in" id="card-divergencia-master-pa">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <span style="cursor:pointer;" data-bs-toggle="collapse" data-bs-target="#divergenciaMasterPA" aria-expanded="false">
                        <i class="fas fa-balance-scale me-2 text-warning"></i>Divergência entre Master e PAs
                        <?php if ($total_pas_com_divergencia > 0): ?>
                            <span class="badge bg-danger ms-2" title="<?php echo $total_pas_com_divergencia; ?> PA(s) com divergência em <?php echo date('d/m/Y', strtotime($data_divergencia)); ?>">
                                <i class="fas fa-exclamation-triangle me-1"></i><?php echo $total_pas_com_divergencia; ?>
                            </span>
                        <?php endif; ?>
                    </span>
                    <div class="d-flex align-items-center gap-2">
                        <form method="GET" action="" class="d-flex align-items-center gap-2 mb-0">
                            <input type="hidden" name="id" value="<?php echo $livro_id; ?>">
                            <label for="data_divergencia" class="form-label mb-0 text-white" style="font-size: 0.875rem;">Data:</label>
                            <input type="date" class="form-control form-control-sm" id="data_divergencia" name="data_divergencia"
                                   value="<?php echo htmlspecialchars($data_divergencia); ?>"
                                   onchange="this.form.submit()" style="width: 140px;">
                        </form>
                        <span class="badge bg-secondary" style="cursor:pointer;" data-bs-toggle="collapse" data-bs-target="#divergenciaMasterPA" aria-expanded="false">
                            Clique para expandir
                        </span>
                    </div>
                </div>
            </div>
            <div class="collapse" id="divergenciaMasterPA">
                <div class="card-body py-2">
                    <?php if (empty($divergencias_por_pa)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <h5 class="text-success">Nenhuma Divergência Encontrada</h5>
                            <p class="text-muted mb-0">
                                Todos os PAs estão com saldos corretos em relação ao livro Master
                                na data de <strong><?php echo date('d/m/Y', strtotime($data_divergencia)); ?></strong>.
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($divergencias_por_pa as $pa): ?>
                                <div class="col-md-4 col-lg-3 mb-3">
                                    <div class="border rounded p-3 h-100 d-flex flex-column justify-content-between align-items-start shadow-sm" style="min-height: 90px;">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="fas fa-university fa-lg me-2 text-primary"></i>
                                            <span class="fw-semibold" style="font-size:1.05rem;"> <?php echo htmlspecialchars($pa['pa_nome']); ?> (PA: <?php echo $pa['pa_numero']; ?></span>
                                        </div>
                                        <div>
                                            <?php if ($pa['diferenca'] < 0): ?>
                                                <span class="badge bg-danger"><i class="fas fa-arrow-down me-1"></i>Falta de R$ <?php echo number_format(abs($pa['diferenca']), 2, ',', '.'); ?></span>
                                            <?php else: ?>
                                                <span class="badge bg-warning text-dark"><i class="fas fa-arrow-up me-1"></i>Sobra de R$ <?php echo number_format($pa['diferenca'], 2, ',', '.'); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <!-- Alerta de Divergência de Saldo para Livro Master -->
        <?php if ($livro['tipo'] === 'master' && isset($divergencia_saldo) && $divergencia_saldo && $divergencia_saldo['tem_divergencia']): ?>
            <div class="alert alert-warning border-warning fade-in mb-4">
                <div class="d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning me-3"></i>
                    <div>
                        <strong>Divergência de Saldo no PA selecionado!</strong><br>
                        <?php if ($divergencia_saldo['diferenca'] < 0): ?>
                            Falta de <strong>R$ <?php echo number_format(abs($divergencia_saldo['diferenca']), 2, ',', '.'); ?></strong> no livro do PA.<br>
                        <?php else: ?>
                            Sobra de <strong>R$ <?php echo number_format($divergencia_saldo['diferenca'], 2, ',', '.'); ?></strong> no livro do PA.<br>
                        <?php endif; ?>
                        <small>Saldo esperado: <strong>R$ <?php echo number_format($divergencia_saldo['saldo_esperado'], 2, ',', '.'); ?></strong> | Saldo atual do PA: <strong>R$ <?php echo number_format($divergencia_saldo['saldo_agencia'], 2, ',', '.'); ?></strong></small>
                    </div>
                </div>
            </div>
        <?php endif; ?>
        <!-- Mensagens de feedback -->
        <?php if (isset($_GET['sucesso'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php
                switch ($_GET['sucesso']) {
                    case 'movimentacao_criada':
                        echo 'Movimentação registrada com sucesso!';
                        break;
                    case 'dia_aberto':
                        echo 'Dia aberto com sucesso! Agora você pode registrar movimentações.';
                        break;
                    case 'dia_reaberto':
                        echo 'Dia reaberto com sucesso!';
                        break;
                    case 'dia_fechado':
                        echo 'Dia fechado com sucesso! Para registrar novas movimentações, abra um novo dia.';
                        break;
                    case 'edicao_data_habilitada':
                        echo 'Edição habilitada! Agora é possível alterar movimentações e usar datas personalizadas.';
                        break;
                    case 'edicao_data_desabilitada':
                        echo 'Edição desabilitada! Datas serão restritas conforme regras do sistema e movimentações não poderão ser alteradas.';
                        break;
                    case 'movimentacao_editada':
                        echo 'Movimentação editada com sucesso! Os saldos foram recalculados automaticamente.';
                        break;
                    case 'livro_fechado':
                        echo 'Livro caixa fechado com sucesso!';
                        break;
                    case 'livro_fechado_completo':
                        echo 'Livro caixa fechado com sucesso! O livro atingiu o limite de 98 folhas e agora pode ser impresso.';
                        break;
                    default:
                        echo 'Operação realizada com sucesso!';
                }
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['erro'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php
                switch ($_GET['erro']) {
                    case 'livro_nao_encontrado':
                        echo 'Livro caixa não encontrado.';
                        break;
                    case 'sem_acesso_livro':
                        echo 'Você não tem acesso a este livro caixa.';
                        break;
                    case 'livro_fechado':
                        echo 'Não é possível fazer movimentações em um livro fechado.';
                        break;
                    case 'saldo_insuficiente':
                        echo 'Saldo insuficiente para esta operação.';
                        break;
                    case 'dados_obrigatorios':
                        echo 'Todos os campos obrigatórios devem ser preenchidos.';
                        break;
                    case 'valor_invalido':
                        echo 'Valor da movimentação deve ser maior que zero.';
                        break;
                    case 'categoria_invalida':
                        echo 'Categoria inválida para o tipo de movimentação selecionado.';
                        break;
                    // Mensagens de controle de dia removidas
                    case 'data_competencia_nao_permitida':
                        echo 'Data de competência não permitida para esta origem de movimentação.';
                        break;
                    case 'movimentacao_nao_encontrada':
                        echo 'Movimentação não encontrada ou não pertence a este livro.';
                        break;
                    case 'edicao_nao_habilitada':
                        echo 'Edição não está habilitada para este livro. Habilite a edição primeiro.';
                        break;
                    case 'sem_acesso_livro':
                        echo 'Você não tem acesso para editar movimentações deste livro.';
                        break;
                    case 'erro_edicao':
                        echo 'Erro ao editar movimentação. Tente novamente.';
                        break;
                    case 'equipamento_atm_obrigatorio':
                        echo 'Equipamento ATM/ATMR é obrigatório quando a origem for ATM/ATMR.';
                        break;
                    case 'origem_invalida':
                        echo 'Origem da movimentação inválida.';
                        break;
                    case 'data_competencia_invalida':
                        echo 'Data de competência inválida.';
                        break;
                    case 'data_movimentacao_invalida':
                        echo 'Data/hora da movimentação inválida.';
                        break;
                    case 'sem_permissao_controle_dias':
                        echo 'Você não tem permissão para controlar abertura/fechamento de dias.';
                        break;
                    case 'erro_controle_dia':
                        echo 'Erro ao processar controle de dia. Tente novamente.';
                        if (isset($_GET['detalhes'])) {
                            echo '<br><small>Detalhes: ' . htmlspecialchars(urldecode($_GET['detalhes'])) . '</small>';
                        }
                        break;
                    case 'erro_integridade_dados':
                        echo 'Erro de integridade dos dados. Verifique se todas as tabelas foram criadas corretamente.';
                        break;
                    case 'erro_movimentacao':
                        echo 'Erro ao registrar movimentação. Tente novamente.';
                        break;
                    case 'sem_permissao_pa':
                        echo 'Você não tem permissão para registrar ou editar movimentações neste ponto de atendimento. Apenas tesoureiros designados podem realizar essas operações.';
                        break;
                    case 'livro_nao_completo':
                        echo 'Este livro ainda não atingiu o limite de 98 folhas necessário para fechamento.';
                        break;
                    case 'livro_ja_fechado':
                        echo 'Este livro já está fechado e não pode ser modificado.';
                        break;
                    case 'sem_permissao_fechar':
                        echo 'Você não tem permissão para fechar livros caixa. Apenas administradores podem realizar esta ação.';
                        break;
                    case 'erro_fechar_livro':
                        echo 'Erro ao fechar o livro caixa. Tente novamente.';
                        break;
                    case 'livro_nao_fechado':
                        echo 'Este livro deve estar fechado para ser impresso.';
                        break;
                    case 'ano_invalido':
                        echo 'Não é possível lançar movimentações com data de outro ano. O livro é anual e só aceita lançamentos de ' . ($ano_abertura ?? 'o ano de abertura') . '.';
                        break;
                    case 'dia_nao_aberto':
                        echo 'Só é possível registrar movimentações se o dia estiver aberto. Solicite a abertura do dia antes de lançar movimentações.';
                        break;
                    case 'dia_ja_aberto':
                        echo 'O dia de hoje já está aberto. Não é possível abrir o mesmo dia novamente.';
                        break;
                    default:
                        echo 'Ocorreu um erro inesperado.';
                }
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Page Header -->
        <div class="page-header fade-in">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1>
                        <i class="fas fa-book-open me-2 text-primary"></i><?php echo htmlspecialchars($livro['nome']); ?>
                    </h1>
                    <div class="subtitle">
                        <i class="fas fa-building me-1"></i>
                        <?php echo htmlspecialchars($livro['pa_nome']); ?>
                        <?php if ($livro['pa_numero']): ?>
                            (PA: <?php echo htmlspecialchars($livro['pa_numero']); ?>)
                        <?php endif; ?>
                        <span class="badge <?php echo $livro['status'] == 'aberto' ? 'bg-success' : 'bg-secondary'; ?> ms-2">
                            <?php echo $livro['status'] == 'aberto' ? 'Aberto' : 'Fechado'; ?>
                        </span>
                        <span class="badge <?php echo $livro['tipo'] === 'master' ? 'bg-warning' : 'bg-info'; ?> ms-1">
                            <?php echo $livro['tipo'] === 'master' ? 'Master' : 'Normal'; ?>
                        </span>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <div class="d-flex justify-content-end align-items-center gap-2 flex-wrap lcx-dia-actions">
                        <a href="index.php" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>Voltar
                        </a>
                        <?php if ($dia_hoje_aberto): ?>
                            <span class="badge bg-success">Dia Aberto (<?php echo formatarData($hoje); ?>)</span>
                            <button class="btn btn-warning btn-sm" onclick="abrirModalFecharDia()">
                                <i class="fas fa-lock me-1"></i>Fechar Dia
                            </button>
                        <?php elseif ($dia_hoje_fechado): ?>
                            <span class="badge bg-secondary">Dia Fechado (<?php echo formatarData($hoje); ?>)</span>
                            <button class="btn btn-sicoob-blue btn-sm" onclick="abrirModalHistoricoDia()">
                                <i class="fas fa-history me-1"></i>Histórico de Aberturas
                            </button>
                            <button class="btn btn-success btn-sm" onclick="abrirModalAbrirDia()">
                                <i class="fas fa-redo me-1"></i>Reabrir Caixa
                            </button>
                            <?php if ($primeira_abertura_hoje): ?>
                                <?php 
                                $dt = new DateTime($primeira_abertura_hoje, new DateTimeZone('America/Sao_Paulo'));
                                $primeira_abertura_formatada = $dt->format('d/m/Y H:i');
                                ?>
                                <span class="badge bg-light text-dark ms-2">Primeira abertura: <?php echo $primeira_abertura_formatada; ?></span>
                            <?php endif; ?>
                        <?php elseif ($sem_dia_hoje && $livro['status'] === 'aberto' && $eh_tesoureiro_pa && !$primeira_abertura_hoje): ?>
                            <span class="badge bg-info">Nenhum dia aberto hoje</span>
                            <button class="btn btn-success btn-sm" onclick="abrirModalAbrirDia()">
                                <i class="fas fa-calendar-plus me-1"></i>Abrir Dia
                            </button>
                        <?php elseif ($sem_dia_hoje && $livro['status'] === 'aberto' && $eh_tesoureiro_pa && $primeira_abertura_hoje): ?>
                            <button class="btn btn-sicoob-blue btn-sm" onclick="abrirModalHistoricoDia()">
                                <i class="fas fa-history me-1"></i>Histórico de Aberturas
                            </button>
                            <button class="btn btn-success btn-sm" onclick="abrirModalAbrirDia()">
                                <i class="fas fa-redo me-1"></i>Reabrir Caixa
                            </button>
                            <?php 
                            $dt = new DateTime($primeira_abertura_hoje, new DateTimeZone('America/Sao_Paulo'));
                            $primeira_abertura_formatada = $dt->format('d/m/Y H:i');
                            ?>
                            <span class="badge bg-light text-dark ms-2">Primeira abertura: <?php echo $primeira_abertura_formatada; ?></span>
                        <?php endif; ?>
                        <!-- Botões de movimentação e fechamento anual já existentes -->
                        <?php if ($pode_registrar): ?>
                            <button class="btn btn-success btn-sm" onclick="novaMovimentacao('entrada')">
                                <i class="fas fa-plus me-1"></i>Entrada
                            </button>
                            <button class="btn btn-sicoob-blue btn-sm" onclick="novaMovimentacao('saida')">
                                <i class="fas fa-minus me-1"></i>Saída
                            </button>
                        <?php elseif ($pode_fechar): ?>
                            <div class="alert alert-warning mb-0 py-2">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Livro anual encerrado!</strong> O ano de <?php echo $ano_abertura; ?> terminou. Feche este livro para abrir um novo.
                                <button class="btn btn-sicoob-blue btn-sm ms-2" onclick="fecharLivroCompleto()">
                                    <i class="fas fa-lock me-1"></i>Fechar Livro
                                </button>
                            </div>
                        <?php endif; ?>
                        <?php if ($livro['status'] === 'fechado'): ?>
                            <button class="btn btn-primary btn-sm" onclick="imprimirLivro()">
                                <i class="fas fa-print me-1"></i>Imprimir Livro Completo
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Informações Consolidadas do Livro -->
        <div class="card fade-in">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>Informações do Livro
                </h6>
                <div class="d-flex align-items-center gap-2">
                    <!-- Status do Livro -->
                    <span class="badge <?php echo $livro['status'] === 'aberto' ? 'bg-success' : 'bg-secondary'; ?> badge-sm">
                        <i class="fas fa-book me-1"></i>
                        Livro <?php echo ucfirst($livro['status']); ?>
                    </span>

                    <!-- Badge de edição removido - edição sempre disponível -->

                    <!-- Controles de edição removidos - edição sempre disponível -->
                </div>
            </div>
            <div class="card-body py-2">
                <div class="row">
                    <!-- Resumo Financeiro -->
                    <div class="col-md-8">
                        <div class="row text-center">
                            <div class="col-3">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="fas fa-wallet text-primary mb-1"></i>
                                    <small class="text-muted">Saldo Atual</small>
                                    <strong class="text-primary">R$ <?php echo number_format($livro['saldo_atual'], 2, ',', '.'); ?></strong>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="fas fa-arrow-up text-success mb-1"></i>
                                    <small class="text-muted">Entradas</small>
                                    <strong class="text-success">R$ <?php echo number_format($total_entradas, 2, ',', '.'); ?></strong>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="fas fa-arrow-down text-danger mb-1"></i>
                                    <small class="text-muted">Saídas</small>
                                    <strong class="text-danger">R$ <?php echo number_format($total_saidas, 2, ',', '.'); ?></strong>
                                </div>
                            </div>
                            <div class="col-3">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="fas fa-list text-info mb-1"></i>
                                    <small class="text-muted">Movimentações</small>
                                    <strong class="text-info"><?php echo $total_movimentacoes; ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informações do Livro -->
                    <div class="col-md-4">
                        <div class="border-start ps-3">
                            <small class="text-muted d-block mb-1">
                                <i class="fas fa-info-circle me-1"></i>Detalhes do Livro
                            </small>
                            <div class="row">
                                <div class="col-4">
                                    <small class="text-muted d-block">Criado em</small>
                                    <strong><?php echo date('d/m/Y', strtotime($livro['created_at'])); ?></strong>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted d-block">Saldo Inicial</small>
                                    <strong class="text-primary">R$ <?php echo number_format($livro['saldo_inicial'], 2, ',', '.'); ?></strong>
                                </div>
                                <div class="col-4">
                                    <small class="text-muted d-block">Ano do Livro</small>
                                    <strong class="text-info"><?php echo $ano_abertura; ?></strong>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alerta de Divergência de Saldo - DESABILITADO -->
        <?php /*
        if ($divergencia_saldo && $divergencia_saldo['tem_divergencia']): ?>
            <div class="alert alert-warning border-warning fade-in mb-4" style="animation-delay: 0.15s;">
                <div class="d-flex align-items-start">
                    <div class="me-3">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="alert-heading mb-2">
                            <i class="fas fa-balance-scale me-2"></i>Divergência de Saldo Detectada
                        </h5>
                        <p class="mb-2">
                            O saldo deste livro está divergente em relação ao livro master.
                            Verifique os lançamentos e entre em contato com a UAD se necessário.
                        </p>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Saldo Atual da Agência:</strong><br>
                                <span class="fs-6 text-info">
                                    <?php echo formatarValorMonetario($divergencia_saldo['saldo_agencia']); ?>
                                </span>
                            </div>
                            <div class="col-md-3">
                                <strong>Saldo Ajustado (sem ATMs):</strong><br>
                                <span class="fs-6 <?php echo getClasseDivergencia($divergencia_saldo['diferenca']); ?>">
                                    <?php echo formatarValorMonetario($divergencia_saldo['saldo_agencia_ajustado']); ?>
                                </span>
                            </div>
                            <div class="col-md-3">
                                <strong>Saldo Esperado (Master):</strong><br>
                                <span class="fs-6 text-primary">
                                    <?php echo formatarValorMonetario($divergencia_saldo['saldo_esperado']); ?>
                                </span>
                            </div>
                            <div class="col-md-3">
                                <strong>Diferença:</strong><br>
                                <span class="fs-6 fw-bold <?php echo getClasseDivergencia($divergencia_saldo['diferenca']); ?>">
                                    <?php echo $divergencia_saldo['diferenca'] > 0 ? '+' : ''; ?>
                                    <?php echo formatarValorMonetario($divergencia_saldo['diferenca']); ?>
                                </span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                <strong>Saldo Ajustado:</strong> Saldo atual menos ATMs lançados retroativamente (considerando que ATMs são enviados no dia seguinte para competência do dia anterior)
                            </small>
                        </div>
                        <hr class="my-2">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>Livro Master:</strong> <?php echo htmlspecialchars($divergencia_saldo['livro_master_nome']); ?> |
                            <strong>Verificação:</strong> <?php echo date('d/m/Y H:i', strtotime($divergencia_saldo['data_verificacao'])); ?>
                        </small>
                    </div>
                    <div class="ms-3">
                        <button type="button" class="btn btn-outline-warning btn-sm" onclick="verificarDivergenciaDetalhada()" title="Ver detalhes da divergência">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        <?php endif;
        */ ?>

        <!-- Filtros -->
        <div class="card fade-in">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0">
                    <i class="fas fa-filter me-2"></i>Movimentações
                </h6>
                <form method="GET" class="d-flex align-items-center gap-2">
                    <input type="hidden" name="id" value="<?php echo $livro_id; ?>">
                    <?php if (isset($_GET['data_divergencia'])): ?>
                        <input type="hidden" name="data_divergencia" value="<?php echo htmlspecialchars($_GET['data_divergencia']); ?>">
                    <?php endif; ?>
                    <select class="form-select form-select-sm" name="filtro_tipo" style="width: auto;">
                        <option value="">Todos</option>
                        <option value="entrada" <?php echo ($filtro_tipo == 'entrada') ? 'selected' : ''; ?>>Entrada</option>
                        <option value="saida" <?php echo ($filtro_tipo == 'saida') ? 'selected' : ''; ?>>Saída</option>
                    </select>
                    <input type="date" class="form-control form-control-sm" name="filtro_data_inicio"
                           value="<?php echo htmlspecialchars($filtro_data_inicio); ?>" placeholder="Data início" style="width: 140px;">
                    <input type="date" class="form-control form-control-sm" name="filtro_data_fim"
                           value="<?php echo htmlspecialchars($filtro_data_fim); ?>" placeholder="Data fim" style="width: 140px;">
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="visualizar_livro.php?id=<?php echo $livro_id; ?>" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times"></i>
                    </a>
                </form>
            </div>

            <!-- Tabela de Movimentações -->
            <?php if (!empty($movimentacoes)): ?>
            <div class="table-responsive">
                <table class="table table-hover table-sm mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th style="width: 12%;">Data</th>
                            <th style="width: 10%;">Origem</th>
                            <th style="width: 20%;">Categoria</th>
                            <th style="width: 12%;">Débito</th>
                            <th style="width: 12%;">Crédito</th>
                            <th style="width: 12%;">Saldo</th>
                            <th style="width: 8%;">Usuário</th>
                            <?php if ($pode_registrar): ?>
                                <th style="width: 6%;">Ações</th>
                            <?php endif; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($movimentacoes as $mov): ?>
                            <tr style="font-size: 0.875rem;">
                                <td>
                                    <div class="fw-bold"><?php echo formatarData($mov['data_competencia'] ?? $mov['data_movimentacao']); ?></div>
                                </td>
                                <td>
                                    <span class="badge badge-sm <?php echo ($mov['origem'] ?? 'caixa') === 'caixa' ? 'bg-primary' : 'bg-warning'; ?>">
                                        <?php echo ($mov['origem'] ?? 'caixa') === 'caixa' ? 'Caixa' : 'ATM/ATMR'; ?>
                                    </span>
                                    <?php if (($mov['origem'] ?? 'caixa') === 'atm' && !empty($mov['equipamento_atm_id'])): ?>
                                        <?php
                                        // Buscar dados do equipamento
                                        try {
                                            $stmt_eq = $pdo->prepare("SELECT numero, tipo, localizacao FROM lcx_atm_atmr WHERE id = ?");
                                            $stmt_eq->execute([$mov['equipamento_atm_id']]);
                                            $equipamento = $stmt_eq->fetch(PDO::FETCH_ASSOC);
                                            if ($equipamento) {
                                                echo '<br><small class="text-muted">' . $equipamento['tipo'] . ' ' . htmlspecialchars($equipamento['numero']);
                                                if ($equipamento['localizacao']) {
                                                    echo ' - ' . htmlspecialchars($equipamento['localizacao']);
                                                }
                                                echo '</small>';
                                            }
                                        } catch (Exception $e) {
                                            // Silencioso em caso de erro
                                        }
                                        ?>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($mov['categoria']): ?>
                                        <span class="text-truncate" style="max-width: 180px;" title="<?php echo htmlspecialchars($mov['categoria']); ?>">
                                            <?php echo htmlspecialchars($mov['categoria']); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                    <?php if ($mov['descricao']): ?>
                                        <br><small class="text-muted text-truncate" style="max-width: 180px;" title="<?php echo htmlspecialchars($mov['descricao']); ?>">
                                            <?php echo htmlspecialchars($mov['descricao']); ?>
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td class="text-end">
                                    <?php if ($mov['tipo'] == 'saida'): ?>
                                        <span class="fw-bold text-danger">
                                            R$ <?php echo number_format($mov['valor'], 2, ',', '.'); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-end">
                                    <?php if ($mov['tipo'] == 'entrada'): ?>
                                        <span class="fw-bold text-success">
                                            R$ <?php echo number_format($mov['valor'], 2, ',', '.'); ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-end">
                                    <span class="fw-bold <?php echo $mov['saldo_posterior'] >= 0 ? 'text-primary' : 'text-danger'; ?>">
                                        R$ <?php echo number_format($mov['saldo_posterior'], 2, ',', '.'); ?>
                                    </span>
                                </td>
                                <td>
                                    <small class="text-truncate" style="max-width: 80px;" title="<?php echo htmlspecialchars($mov['criado_por_username']); ?>">
                                        <?php echo htmlspecialchars($mov['criado_por_username']); ?>
                                    </small>
                                    <?php if ($mov['editado']): ?>
                                        <br><button class="badge bg-warning text-dark border-0 text-decoration-none" onclick="verHistorico(<?php echo $mov['id']; ?>)" title="Ver histórico de edições">
                                            <i class="fas fa-history"></i> Editado
                                        </button>
                                    <?php endif; ?>
                                </td>
                                <?php if (($pode_registrar && (($mov['data_competencia'] ?? $mov['data_movimentacao']) === $info_dia['dia_aberto'])) || $permite_edicao_data): ?>
                                    <td>
                                        <button class="btn btn-outline-primary btn-sm" onclick="editarMovimentacao(<?php echo $mov['id']; ?>)" title="Editar movimentação">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    </td>
                                <?php elseif ($pode_registrar): ?>
                                    <td></td>
                                <?php endif; ?>
                            </tr>
                            <?php
                            // Exibir sublinha de divergência apenas no último lançamento do PA
                            if ($livro['tipo'] === 'master' && !empty($mov['categoria']) && preg_match('/\(PA: (\d+)\)/', $mov['categoria'], $match_pa)) {
                                $pa_id = (int)$match_pa[1];
                                if (isset($ultimo_lancamento_pa[$pa_id]) && $ultimo_lancamento_pa[$pa_id] === $idx) {
                                    // Buscar livro aberto do PA
                                    $stmt_div = $pdo->prepare("SELECT id FROM lcx_livros_caixa WHERE ponto_atendimento_id = ? AND tipo = 'normal' ORDER BY status = 'aberto' DESC, data_fechamento DESC, id DESC LIMIT 1");
                                    $stmt_div->execute([$pa_id]);
                                    $livro_agencia = $stmt_div->fetch(PDO::FETCH_ASSOC);
                                    if ($livro_agencia) {
                                        $divergencia = verificarDivergenciaSaldo($pdo, $livro_agencia['id'], $pa_id);
                                        if ($divergencia && $divergencia['tem_divergencia']) {
                                            echo '<tr class="bg-warning-subtle"><td colspan="8">';
                                            echo '<i class="fas fa-exclamation-triangle text-warning me-2"></i>';
                                            if ($divergencia['diferenca'] < 0) {
                                                echo 'Falta de <strong>R$ ' . number_format(abs($divergencia['diferenca']), 2, ',', '.') . '</strong> no livro do PA.';
                                            } else {
                                                echo 'Sobra de <strong>R$ ' . number_format($divergencia['diferenca'], 2, ',', '.') . '</strong> no livro do PA.';
                                            }
                                            echo ' <small>(Saldo esperado: <strong>R$ ' . number_format($divergencia['saldo_esperado'], 2, ',', '.') . '</strong> | Saldo atual do PA: <strong>R$ ' . number_format($divergencia['saldo_agencia'], 2, ',', '.') . '</strong>)</small>';
                                            echo '</td></tr>';
                                        }
                                    }
                                }
                            }
                            ?>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <!-- Paginação -->
            <?php if ($total_paginas > 1): ?>
                <div class="d-flex justify-content-center mt-3">
                    <nav aria-label="Paginação de movimentações">
                        <ul class="pagination pagination-sm">
                            <?php if ($pagina > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?id=<?php echo $livro_id; ?>&pagina=<?php echo $pagina - 1; ?><?php echo !empty($filtro_tipo) ? '&filtro_tipo=' . urlencode($filtro_tipo) : ''; ?><?php echo !empty($filtro_data_inicio) ? '&filtro_data_inicio=' . urlencode($filtro_data_inicio) : ''; ?><?php echo !empty($filtro_data_fim) ? '&filtro_data_fim=' . urlencode($filtro_data_fim) : ''; ?>">
                                        <i class="fas fa-angle-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php
                            $inicio = max(1, $pagina - 2);
                            $fim = min($total_paginas, $pagina + 2);

                            for ($i = $inicio; $i <= $fim; $i++):
                            ?>
                                <li class="page-item <?php echo ($i == $pagina) ? 'active' : ''; ?>">
                                    <a class="page-link" href="?id=<?php echo $livro_id; ?>&pagina=<?php echo $i; ?><?php echo !empty($filtro_tipo) ? '&filtro_tipo=' . urlencode($filtro_tipo) : ''; ?><?php echo !empty($filtro_data_inicio) ? '&filtro_data_inicio=' . urlencode($filtro_data_inicio) : ''; ?><?php echo !empty($filtro_data_fim) ? '&filtro_data_fim=' . urlencode($filtro_data_fim) : ''; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($pagina < $total_paginas): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?id=<?php echo $livro_id; ?>&pagina=<?php echo $pagina + 1; ?><?php echo !empty($filtro_tipo) ? '&filtro_tipo=' . urlencode($filtro_tipo) : ''; ?><?php echo !empty($filtro_data_inicio) ? '&filtro_data_inicio=' . urlencode($filtro_data_inicio) : ''; ?><?php echo !empty($filtro_data_fim) ? '&filtro_data_fim=' . urlencode($filtro_data_fim) : ''; ?>">
                                        <i class="fas fa-angle-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
            <?php endif; ?>
        <?php endif; ?>

        <!-- Mensagem quando não há movimentações -->
        <?php if (empty($movimentacoes)): ?>
            <div class="card fade-in">
                <div class="card-body text-center py-4">
                    <i class="fas fa-receipt fa-2x text-muted mb-2"></i>
                    <h6 class="text-muted">Nenhuma movimentação encontrada</h6>
                    <p class="text-muted mb-0">
                        <?php if (!empty($filtro_tipo) || !empty($filtro_data_inicio) || !empty($filtro_data_fim)): ?>
                            Nenhuma movimentação encontrada com os filtros aplicados.
                        <?php else: ?>
                            Este livro ainda não possui movimentações registradas.
                        <?php endif; ?>
                    </p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Modal Nova Movimentação -->
    <div class="modal fade" id="modalNovaMovimentacao" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>Nova Movimentação
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="formNovaMovimentacao" method="POST" action="processar_movimentacao.php">
                    <div class="modal-body">
                        <input type="hidden" id="livro_id" name="livro_id" value="<?php echo $livro_id; ?>">
                        <input type="hidden" id="tipo_movimentacao" name="tipo">

                        <div class="alert alert-info" id="alertTipo">
                            <i class="fas fa-info-circle me-2"></i>
                            <span id="textoTipo"></span>
                        </div>

                        <div class="mb-3">
                            <label for="valor" class="form-label">Valor</label>
                            <div class="input-group">
                                <span class="input-group-text">R$</span>
                                <input type="text" class="form-control" id="valor" name="valor"
                                       required placeholder="0,00">
                                <input type="hidden" id="valor_hidden" name="valor_value">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="descricao" class="form-label">Descrição <small class="text-muted">(opcional)</small></label>
                            <textarea class="form-control" id="descricao" name="descricao" rows="3"
                                      placeholder="Descreva a movimentação (opcional)..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="categoria" class="form-label">
                                <?php if ($livro['tipo'] === 'master'): ?>
                                    Ponto de Atendimento
                                <?php else: ?>
                                    Categoria
                                <?php endif; ?>
                            </label>
                            <select class="form-select" id="categoria" name="categoria" required>
                                <option value="">
                                    <?php if ($livro['tipo'] === 'master'): ?>
                                        Selecione o ponto de atendimento...
                                    <?php else: ?>
                                        Selecione uma categoria...
                                    <?php endif; ?>
                                </option>
                                <!-- Opções serão preenchidas dinamicamente via JavaScript -->
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="origem" class="form-label">Origem da Movimentação</label>
                            <select class="form-select" id="origem" name="origem" required onchange="atualizarDataCompetencia(); toggleEquipamentoATM()">
                                <option value="caixa">Caixa</option>
                                <option value="atm">ATM/ATMR</option>
                            </select>
                            <div class="form-text">
                                <strong>Caixa:</strong> Movimentações do dia atual<br>
                                <strong>ATM/ATMR:</strong> Movimentações do último dia útil anterior
                            </div>
                        </div>

                        <!-- Campo de seleção de equipamento ATM/ATMR -->
                        <div class="mb-3" id="divEquipamentoATM" style="display: none;">
                            <label for="equipamento_atm_id" class="form-label">Equipamento ATM/ATMR</label>
                            <select class="form-select" id="equipamento_atm_id" name="equipamento_atm_id">
                                <option value="">Selecione o equipamento...</option>
                                <?php foreach ($equipamentos_atm as $equipamento): ?>
                                    <option value="<?php echo $equipamento['id']; ?>">
                                        <?php echo $equipamento['tipo']; ?> <?php echo htmlspecialchars($equipamento['numero']); ?>
                                        <?php if ($equipamento['localizacao']): ?>
                                            - <?php echo htmlspecialchars($equipamento['localizacao']); ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">
                                Selecione o equipamento específico para esta movimentação
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="data_competencia" class="form-label">Data de Competência</label>
                            <input type="date" class="form-control" id="data_competencia" name="data_competencia"
                                   value="<?php echo date('Y-m-d'); ?>" required readonly>
                            <div class="form-text" id="texto_data_competencia">
                                Data de competência da movimentação
                            </div>
                        </div>

                        <div class="alert alert-warning" id="alertSaldo" style="display: none;">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Atenção:</strong> Esta operação resultará em saldo negativo.
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary" id="btnConfirmar">
                            <i class="fas fa-save me-2"></i>Registrar Movimentação
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Editar Movimentação -->
    <div class="modal fade" id="modalEditarMovimentacao" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-edit me-2"></i>Editar Movimentação
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="formEditarMovimentacao" method="POST" action="processar_edicao_movimentacao.php">
                    <div class="modal-body">
                        <input type="hidden" id="edit_movimentacao_id" name="movimentacao_id">
                        <input type="hidden" id="edit_livro_id" name="livro_id" value="<?php echo $livro_id; ?>">

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Atenção:</strong> Alterações em movimentações podem afetar o saldo e histórico do livro caixa.
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_tipo" class="form-label">Tipo</label>
                                    <select class="form-select" id="edit_tipo" name="tipo" required>
                                        <option value="entrada">Entrada</option>
                                        <option value="saida">Saída</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_valor" class="form-label">Valor</label>
                                    <div class="input-group">
                                        <span class="input-group-text">R$</span>
                                        <input type="text" class="form-control" id="edit_valor" name="valor"
                                               required>
                                        <input type="hidden" id="edit_valor_hidden" name="valor_value">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="edit_descricao" class="form-label">Descrição <small class="text-muted">(opcional)</small></label>
                            <textarea class="form-control" id="edit_descricao" name="descricao" rows="3"
                                      placeholder="Descreva a movimentação (opcional)..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="edit_categoria" class="form-label">
                                <?php if ($livro['tipo'] === 'master'): ?>
                                    Ponto de Atendimento
                                <?php else: ?>
                                    Categoria
                                <?php endif; ?>
                            </label>
                            <select class="form-select" id="edit_categoria" name="categoria" required>
                                <!-- Opções serão preenchidas dinamicamente -->
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="edit_origem" class="form-label">Origem da Movimentação</label>
                            <select class="form-select" id="edit_origem" name="origem" required onchange="toggleEquipamentoATMEdit()">
                                <option value="caixa">Caixa</option>
                                <option value="atm">ATM/ATMR</option>
                            </select>
                        </div>

                        <!-- Campo de seleção de equipamento ATM/ATMR para edição -->
                        <div class="mb-3" id="divEquipamentoATMEdit" style="display: none;">
                            <label for="edit_equipamento_atm_id" class="form-label">Equipamento ATM/ATMR</label>
                            <select class="form-select" id="edit_equipamento_atm_id" name="equipamento_atm_id">
                                <option value="">Selecione o equipamento...</option>
                                <?php foreach ($equipamentos_atm as $equipamento): ?>
                                    <option value="<?php echo $equipamento['id']; ?>">
                                        <?php echo $equipamento['tipo']; ?> <?php echo htmlspecialchars($equipamento['numero']); ?>
                                        <?php if ($equipamento['localizacao']): ?>
                                            - <?php echo htmlspecialchars($equipamento['localizacao']); ?>
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="edit_data_competencia" class="form-label">Data de Competência</label>
                            <input type="date" class="form-control" id="edit_data_competencia" name="data_competencia" required>
                            <div class="form-text">
                                Data de competência da movimentação
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Informações do Registro</label>
                            <div class="bg-light p-3 rounded">
                                <small class="text-muted">
                                    <strong>Criado por:</strong> <span id="edit_criado_por"></span><br>
                                    <strong>Data de criação:</strong> <span id="edit_data_criacao"></span><br>
                                    <strong>Saldo anterior:</strong> R$ <span id="edit_saldo_anterior"></span><br>
                                    <strong>Saldo posterior original:</strong> R$ <span id="edit_saldo_posterior_original"></span>
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Salvar Alterações
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Histórico de Movimentação -->
    <div class="modal fade" id="modalHistoricoMovimentacao" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">
                        <i class="fas fa-history me-2"></i>Histórico da Movimentação
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="conteudoHistorico">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Carregando...</span>
                        </div>
                        <p class="mt-2 text-muted">Carregando histórico...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Fechamento de Dia -->
    <div class="modal fade" id="modalFecharDia" tabindex="-1" aria-labelledby="modalFecharDiaLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="modalFecharDiaLabel">Fechar Dia do Livro Caixa</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <p><strong>Saldo atual do livro:</strong> R$ <?php echo number_format($livro['saldo_atual'], 2, ',', '.'); ?></p>
            <div class="alert alert-info">
              <input type="checkbox" id="termoConsentimentoDia"> <label for="termoConsentimentoDia">Confirmo que o saldo está correto e estou ciente das movimentações do dia.</label>
            </div>
          </div>
          <div class="modal-footer">
            <form method="post" action="processar_controle_dia.php" id="formFecharDia">
              <input type="hidden" name="acao" value="fechar">
              <input type="hidden" name="livro_id" value="<?php echo $livro_id; ?>">
              <input type="hidden" name="redirect" value="visualizar_livro.php?id=<?php echo $livro_id; ?>">
              <button type="submit" class="btn btn-sicoob-blue" id="btnConfirmarFecharDia" disabled>Confirmar Fechamento do Dia</button>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmação de abertura de dia -->
    <div class="modal fade" id="modalAbrirDia" tabindex="-1" aria-labelledby="modalAbrirDiaLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="modalAbrirDiaLabel"><i class="fas fa-calendar-plus me-2 text-success"></i>Confirmar Abertura do Dia</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
          </div>
          <div class="modal-body">
            <p>Deseja realmente abrir o dia de hoje (<strong><?php echo formatarData($hoje); ?></strong>) para lançamentos?</p>
          </div>
          <div class="modal-footer">
            <form method="post" action="processar_controle_dia.php" class="d-inline">
              <input type="hidden" name="acao" value="abrir">
              <input type="hidden" name="livro_id" value="<?php echo $livro_id; ?>">
              <input type="hidden" name="redirect" value="visualizar_livro.php?id=<?php echo $livro_id; ?>">
              <button type="submit" class="btn btn-success"><i class="fas fa-check me-1"></i>Confirmar</button>
            </form>
            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancelar</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Histórico de Aberturas/Reaberturas/Fechamentos do Dia -->
    <div class="modal fade" id="modalHistoricoDia" tabindex="-1" aria-labelledby="modalHistoricoDiaLabel" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="modalHistoricoDiaLabel"><i class="fas fa-history me-2 text-info"></i>Histórico do Dia (<?php echo formatarData($hoje); ?>)</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
          </div>
          <div class="modal-body">
            <!-- DEBUG: -->
            <!-- HOJE: <?php echo $hoje; ?> -->
            <!-- HISTORICO DIA HOJE: <?php echo count($historico_dia_hoje); ?> eventos -->
            <?php if (empty($historico_dia_hoje)): ?>
              <div class="alert alert-info mb-0">Nenhum evento registrado para o dia.</div>
            <?php else: ?>
              <ul class="list-group">
                <?php foreach ($historico_dia_hoje as $log): ?>
                  <li class="list-group-item d-flex justify-content-between align-items-center">
                    <span>
                      <strong><?php echo ucfirst($log['acao']); ?></strong>
                      <small class="text-muted ms-2">por <?php echo htmlspecialchars($log['nome_completo']); ?></small>
                      <?php if ($log['observacoes']): ?>
                        <br><small class="text-muted">Obs: <?php echo htmlspecialchars($log['observacoes']); ?></small>
                      <?php endif; ?>
                    </span>
                    <span class="badge bg-light text-dark"><?php echo formatarDataHora($log['data_acao']); ?></span>
                  </li>
                <?php endforeach; ?>
              </ul>
            <?php endif; ?>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Fechar</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        const saldoAtual = <?php echo $livro['saldo_atual']; ?>;

        const tipoLivro = '<?php echo $livro['tipo']; ?>';
        const pontosAtendimento = <?php echo json_encode($pontos_atendimento); ?>;

        function novaMovimentacao(tipo) {
            document.getElementById('tipo_movimentacao').value = tipo;

            const alertTipo = document.getElementById('alertTipo');
            const textoTipo = document.getElementById('textoTipo');
            const btnConfirmar = document.getElementById('btnConfirmar');
            const selectCategoria = document.getElementById('categoria');

            if (tipo === 'entrada') {
                alertTipo.className = 'alert alert-success';
                textoTipo.innerHTML = '<strong>Entrada de Caixa:</strong> Esta operação irá aumentar o saldo do livro caixa.';
                btnConfirmar.className = 'btn btn-success';
                btnConfirmar.innerHTML = '<i class="fas fa-arrow-up me-2"></i>Registrar Entrada';
            } else {
                alertTipo.className = 'alert alert-danger';
                textoTipo.innerHTML = '<strong>Saída de Caixa:</strong> Esta operação irá diminuir o saldo do livro caixa.';
                btnConfirmar.className = 'btn btn-danger';
                btnConfirmar.innerHTML = '<i class="fas fa-arrow-down me-2"></i>Registrar Saída';
            }

            // Limpar formulário
            document.getElementById('formNovaMovimentacao').reset();
            document.getElementById('tipo_movimentacao').value = tipo;
            document.getElementById('livro_id').value = <?php echo $livro_id; ?>;
            document.getElementById('alertSaldo').style.display = 'none';

            // Preencher categorias/pontos de atendimento
            preencherCategorias();

            // Atualizar data de competência e campo de equipamento
            atualizarDataCompetencia();
            toggleEquipamentoATM();

            const modal = new bootstrap.Modal(document.getElementById('modalNovaMovimentacao'));
            modal.show();
        }

        function preencherCategorias() {
            const selectCategoria = document.getElementById('categoria');

            // Limpar opções existentes
            selectCategoria.innerHTML = '';

            if (tipoLivro === 'master') {
                // Para livros master: mostrar pontos de atendimento
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = 'Selecione o ponto de atendimento...';
                selectCategoria.appendChild(defaultOption);

                pontosAtendimento.forEach(pa => {
                    const option = document.createElement('option');
                    option.value = pa.nome + (pa.numero ? ' (PA: ' + pa.numero + ')' : '');
                    option.textContent = pa.nome + (pa.numero ? ' (PA: ' + pa.numero + ')' : '');
                    selectCategoria.appendChild(option);
                });
            } else {
                // Para livros normais: mostrar categorias tradicionais
                const tipo = document.getElementById('tipo_movimentacao').value;
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = 'Selecione uma categoria...';
                selectCategoria.appendChild(defaultOption);

                if (tipo === 'entrada') {
                    const categoriasEntrada = ['Recolhimento'];
                    categoriasEntrada.forEach(categoria => {
                        const option = document.createElement('option');
                        option.value = categoria;
                        option.textContent = categoria;
                        selectCategoria.appendChild(option);
                    });
                } else {
                    const categoriasSaida = ['Envio de Numerário'];
                    categoriasSaida.forEach(categoria => {
                        const option = document.createElement('option');
                        option.value = categoria;
                        option.textContent = categoria;
                        selectCategoria.appendChild(option);
                    });
                }
            }
        }

        // Função para formatar valor monetário
        function formatarMoeda(valor) {
            // Remove tudo que não é dígito
            valor = valor.replace(/\D/g, '');

            // Converte para centavos
            valor = (valor / 100).toFixed(2) + '';

            // Adiciona separadores de milhares e decimais
            valor = valor.replace('.', ',');
            valor = valor.replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.');

            return valor;
        }

        // Função para converter para valor numérico
        function converterParaNumero(valorFormatado) {
            return parseFloat(valorFormatado
                .replace(/\./g, '') // Remove pontos de milhares
                .replace(',', '.')); // Converte vírgula para ponto decimal
        }

        // Máscara monetária para campo valor (nova movimentação)
        const valorInput = document.getElementById('valor');
        const valorHidden = document.getElementById('valor_hidden');

        if (valorInput && valorHidden) {
            // Event listener para formatação em tempo real
            valorInput.addEventListener('input', function() {
                const valorFormatado = formatarMoeda(this.value);
                this.value = valorFormatado;

                // Atualiza campo hidden com valor numérico
                const valorNumerico = converterParaNumero(valorFormatado);
                valorHidden.value = valorNumerico.toFixed(2);

                // Verificar saldo em tempo real
                const tipo = document.getElementById('tipo_movimentacao').value;
                const alertSaldo = document.getElementById('alertSaldo');

                if (tipo === 'saida' && valorNumerico > saldoAtual) {
                    alertSaldo.style.display = 'block';
                } else {
                    alertSaldo.style.display = 'none';
                }
            });

            // Event listener para quando o campo perde o foco
            valorInput.addEventListener('blur', function() {
                if (this.value === '' || this.value === '0,00') {
                    this.value = '0,00';
                    valorHidden.value = '0.00';
                }
            });

            // Event listener para quando o campo ganha foco
            valorInput.addEventListener('focus', function() {
                if (this.value === '0,00') {
                    this.value = '';
                }
            });
        }

        // Mostrar/ocultar campo de equipamento ATM/ATMR
        function toggleEquipamentoATM() {
            const origem = document.getElementById('origem').value;
            const divEquipamento = document.getElementById('divEquipamentoATM');
            const selectEquipamento = document.getElementById('equipamento_atm_id');

            if (origem === 'atm') {
                divEquipamento.style.display = 'block';
                selectEquipamento.required = true;
            } else {
                divEquipamento.style.display = 'none';
                selectEquipamento.required = false;
                selectEquipamento.value = '';
            }
        }

        // Atualizar data de competência baseada na origem
        function atualizarDataCompetencia() {
            const origem = document.getElementById('origem').value;
            const dataCompetencia = document.getElementById('data_competencia');
            const textoData = document.getElementById('texto_data_competencia');

            // Edição sempre habilitada - usuário pode escolher qualquer data
            if (origem === 'caixa') {
                // Sugerir o dia atual para caixa
                dataCompetencia.value = '<?php echo date('Y-m-d'); ?>';
                textoData.textContent = 'Sugestão: movimentações de caixa normalmente são do dia atual';
            } else if (origem === 'atm') {
                // Para ATM/ATMR, sugerir baseado no tipo
                const tipo = document.getElementById('tipo_movimentacao').value;
                if (tipo === 'saida') {
                    dataCompetencia.value = '<?php echo date('Y-m-d'); ?>';
                    textoData.textContent = 'Sugestão: movimentações de ATM/ATMR (saída) normalmente são do dia atual';
                } else {
                    dataCompetencia.value = '<?php echo getUltimoDiaUtil(); ?>';
                    textoData.textContent = 'Sugestão: movimentações de ATM/ATMR (entrada) normalmente são do último dia útil anterior';
                }
            } else {
                textoData.textContent = 'Escolha a data de competência da movimentação';
            }
        }

        // Funções de controle de dias - REMOVIDAS
        // Sistema de abrir/fechar dia foi removido

        // Função toggleEdicaoData removida - edição sempre habilitada

        // Função para editar movimentação
        function editarMovimentacao(movimentacaoId) {
            // Buscar dados da movimentação via AJAX
            fetch('buscar_movimentacao.php?id=' + movimentacaoId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        preencherModalEdicao(data.movimentacao);
                        const modal = new bootstrap.Modal(document.getElementById('modalEditarMovimentacao'));
                        modal.show();
                    } else {
                        alert('Erro ao carregar dados da movimentação: ' + (data.message || 'Erro desconhecido'));
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    alert('Erro ao carregar dados da movimentação.');
                });
        }

        // Preencher modal de edição com dados da movimentação
        function preencherModalEdicao(mov) {
            document.getElementById('edit_movimentacao_id').value = mov.id;
            document.getElementById('edit_tipo').value = mov.tipo;

            // Formatar valor para exibição
            const valorNumerico = parseFloat(mov.valor);
            const valorFormatado = valorNumerico.toLocaleString('pt-BR', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
            document.getElementById('edit_valor').value = valorFormatado;
            document.getElementById('edit_valor_hidden').value = valorNumerico.toFixed(2);

            document.getElementById('edit_descricao').value = mov.descricao || '';
            document.getElementById('edit_categoria').value = mov.categoria || '';
            document.getElementById('edit_origem').value = mov.origem || 'caixa';
            document.getElementById('edit_equipamento_atm_id').value = mov.equipamento_atm_id || '';
            document.getElementById('edit_data_competencia').value = mov.data_competencia;

            // Informações do registro
            document.getElementById('edit_criado_por').textContent = mov.criado_por_nome;
            document.getElementById('edit_data_criacao').textContent = new Date(mov.data_movimentacao).toLocaleString('pt-BR');
            document.getElementById('edit_saldo_anterior').textContent = parseFloat(mov.saldo_anterior).toLocaleString('pt-BR', {minimumFractionDigits: 2});
            document.getElementById('edit_saldo_posterior_original').textContent = parseFloat(mov.saldo_posterior).toLocaleString('pt-BR', {minimumFractionDigits: 2});

            // Preencher categorias para edição
            preencherCategoriasEdicao(mov.tipo);

            // Mostrar/ocultar campo de equipamento ATM
            toggleEquipamentoATMEdit();
        }

        // Preencher categorias no modal de edição
        function preencherCategoriasEdicao(tipo) {
            const selectCategoria = document.getElementById('edit_categoria');
            const categoriaAtual = selectCategoria.value;

            // Limpar opções existentes
            selectCategoria.innerHTML = '';

            if (tipoLivro === 'master') {
                // Para livros master: mostrar pontos de atendimento
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = 'Selecione o ponto de atendimento...';
                selectCategoria.appendChild(defaultOption);

                pontosAtendimento.forEach(pa => {
                    const option = document.createElement('option');
                    option.value = pa.nome + (pa.numero ? ' (PA: ' + pa.numero + ')' : '');
                    option.textContent = pa.nome + (pa.numero ? ' (PA: ' + pa.numero + ')' : '');
                    selectCategoria.appendChild(option);
                });
            } else {
                // Para livros normais: mostrar categorias tradicionais
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = 'Selecione uma categoria...';
                selectCategoria.appendChild(defaultOption);

                if (tipo === 'entrada') {
                    const categoriasEntrada = ['Recolhimento', 'Suprimento Reserva'];
                    categoriasEntrada.forEach(categoria => {
                        const option = document.createElement('option');
                        option.value = categoria;
                        option.textContent = categoria;
                        selectCategoria.appendChild(option);
                    });
                } else {
                    const categoriasSaida = ['Envio de Numerário', 'Envio de Reserva', 'Suprimento'];
                    categoriasSaida.forEach(categoria => {
                        const option = document.createElement('option');
                        option.value = categoria;
                        option.textContent = categoria;
                        selectCategoria.appendChild(option);
                    });
                }
            }

            // Restaurar valor selecionado
            selectCategoria.value = categoriaAtual;
        }

        // Mostrar/ocultar campo de equipamento ATM/ATMR na edição
        function toggleEquipamentoATMEdit() {
            const origem = document.getElementById('edit_origem').value;
            const divEquipamento = document.getElementById('divEquipamentoATMEdit');
            const selectEquipamento = document.getElementById('edit_equipamento_atm_id');

            if (origem === 'atm') {
                divEquipamento.style.display = 'block';
                selectEquipamento.required = true;
            } else {
                divEquipamento.style.display = 'none';
                selectEquipamento.required = false;
                selectEquipamento.value = '';
            }
        }

        // Atualizar categorias quando tipo mudar na edição
        document.getElementById('edit_tipo').addEventListener('change', function() {
            preencherCategoriasEdicao(this.value);
        });

        // Máscara monetária para campo valor (edição)
        const editValorInput = document.getElementById('edit_valor');
        const editValorHidden = document.getElementById('edit_valor_hidden');

        if (editValorInput && editValorHidden) {
            // Event listener para formatação em tempo real
            editValorInput.addEventListener('input', function() {
                const valorFormatado = formatarMoeda(this.value);
                this.value = valorFormatado;

                // Atualiza campo hidden com valor numérico
                const valorNumerico = converterParaNumero(valorFormatado);
                editValorHidden.value = valorNumerico.toFixed(2);
            });

            // Event listener para quando o campo perde o foco
            editValorInput.addEventListener('blur', function() {
                if (this.value === '' || this.value === '0,00') {
                    this.value = '0,00';
                    editValorHidden.value = '0.00';
                }
            });

            // Event listener para quando o campo ganha foco
            editValorInput.addEventListener('focus', function() {
                if (this.value === '0,00') {
                    this.value = '';
                }
            });
        }

        // Função para ver histórico da movimentação
        function verHistorico(movimentacaoId) {
            const modal = new bootstrap.Modal(document.getElementById('modalHistoricoMovimentacao'));
            const conteudo = document.getElementById('conteudoHistorico');

            // Mostrar loading
            conteudo.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p class="mt-2 text-muted">Carregando histórico...</p>
                </div>
            `;

            modal.show();

            // Buscar histórico via AJAX
            fetch('buscar_historico_movimentacao.php?id=' + movimentacaoId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        conteudo.innerHTML = gerarConteudoHistorico(data.movimentacao, data.historico);
                    } else {
                        conteudo.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Erro ao carregar histórico: ${data.message || 'Erro desconhecido'}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    conteudo.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Erro ao carregar histórico. Tente novamente.
                        </div>
                    `;
                });
        }

        // Gerar conteúdo HTML do histórico
        function gerarConteudoHistorico(movimentacao, historico) {
            let html = `
                <!-- Dados Atuais da Movimentação -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-receipt me-2"></i>Dados Atuais da Movimentação
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-2"><strong>Tipo:</strong>
                                    <span class="badge ${movimentacao.tipo == 'entrada' ? 'bg-success' : 'bg-danger'}">
                                        ${movimentacao.tipo.charAt(0).toUpperCase() + movimentacao.tipo.slice(1)}
                                    </span>
                                </p>
                                <p class="mb-2"><strong>Valor:</strong> R$ ${parseFloat(movimentacao.valor).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</p>
                                <p class="mb-2"><strong>Categoria:</strong> ${movimentacao.categoria || 'Não informada'}</p>
                                <p class="mb-2"><strong>Origem:</strong> ${movimentacao.origem ? movimentacao.origem.charAt(0).toUpperCase() + movimentacao.origem.slice(1) : 'Caixa'}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-2"><strong>Data de Competência:</strong> ${new Date(movimentacao.data_competencia).toLocaleDateString('pt-BR')}</p>
                                <p class="mb-2"><strong>Criado por:</strong> ${movimentacao.criado_por_nome}</p>
                                <p class="mb-2"><strong>Data de Criação:</strong> ${new Date(movimentacao.data_movimentacao).toLocaleString('pt-BR')}</p>
                                ${movimentacao.editado ? `
                                    <p class="mb-2"><strong>Editado por:</strong> ${movimentacao.editado_por_nome}</p>
                                    <p class="mb-2"><strong>Data da Edição:</strong> ${new Date(movimentacao.editado_em).toLocaleString('pt-BR')}</p>
                                ` : ''}
                            </div>
                        </div>
                        ${movimentacao.descricao ? `
                            <div class="row">
                                <div class="col-12">
                                    <p class="mb-0"><strong>Descrição:</strong> ${movimentacao.descricao}</p>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;

            if (historico && historico.length > 0) {
                html += `
                    <!-- Histórico de Edições -->
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">
                                <i class="fas fa-edit me-2"></i>Histórico de Edições (${historico.length})
                            </h6>
                        </div>
                        <div class="card-body p-0" style="max-height: 400px; overflow-y: auto;">
                `;

                historico.forEach((edicao, index) => {
                    html += `
                        <div class="border-bottom p-3 ${index % 2 == 0 ? 'bg-light' : ''}">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <h6 class="mb-0">
                                    <i class="fas fa-clock me-2"></i>
                                    Edição #${historico.length - index}
                                </h6>
                                <small class="text-muted">
                                    ${new Date(edicao.editado_em).toLocaleString('pt-BR')} por ${edicao.editado_por_nome}
                                </small>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-danger small">Dados Originais</h6>
                                    <ul class="list-unstyled small mb-0">
                                        <li><strong>Tipo:</strong> ${edicao.tipo_original.charAt(0).toUpperCase() + edicao.tipo_original.slice(1)}</li>
                                        <li><strong>Valor:</strong> R$ ${parseFloat(edicao.valor_original).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</li>
                                        <li><strong>Categoria:</strong> ${edicao.categoria_original || 'Não informada'}</li>
                                        <li><strong>Origem:</strong> ${edicao.origem_original.charAt(0).toUpperCase() + edicao.origem_original.slice(1)}</li>
                                        <li><strong>Data Competência:</strong> ${new Date(edicao.data_competencia_original).toLocaleDateString('pt-BR')}</li>
                                        <li><strong>Saldo Posterior:</strong> R$ ${parseFloat(edicao.saldo_posterior_original).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-success small">Dados Novos</h6>
                                    <ul class="list-unstyled small mb-0">
                                        <li><strong>Tipo:</strong> ${edicao.tipo_novo.charAt(0).toUpperCase() + edicao.tipo_novo.slice(1)}</li>
                                        <li><strong>Valor:</strong> R$ ${parseFloat(edicao.valor_novo).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</li>
                                        <li><strong>Categoria:</strong> ${edicao.categoria_nova || 'Não informada'}</li>
                                        <li><strong>Origem:</strong> ${edicao.origem_nova.charAt(0).toUpperCase() + edicao.origem_nova.slice(1)}</li>
                                        <li><strong>Data Competência:</strong> ${new Date(edicao.data_competencia_nova).toLocaleDateString('pt-BR')}</li>
                                        <li><strong>Saldo Posterior:</strong> R$ ${parseFloat(edicao.saldo_posterior_novo).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            } else {
                html += `
                    <div class="card">
                        <div class="card-body text-center py-4">
                            <i class="fas fa-info-circle fa-2x text-muted mb-3"></i>
                            <h6 class="text-muted">Nenhuma Edição Registrada</h6>
                            <p class="text-muted mb-0">Esta movimentação ainda não foi editada.</p>
                        </div>
                    </div>
                `;
            }

            return html;
        }

        // Função para verificar divergência detalhada - DESABILITADA
        // function verificarDivergenciaDetalhada() {
        //     alert('Funcionalidade de análise detalhada de divergência será implementada em breve.\n\nPor enquanto, verifique:\n- Movimentações ATM do dia anterior\n- Lançamentos no livro master\n- Contate a UAD se necessário');
        // }

        // Função para fechar livro completo (98 folhas)
        function fecharLivroCompleto() {
            if (confirm('Confirma o fechamento deste livro caixa?\n\nEste livro atingiu o limite de 98 folhas e será fechado definitivamente.\nApós o fechamento, não será possível adicionar novas movimentações.\n\nDeseja continuar?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'fechar_livro_completo.php';

                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'livro_id';
                input.value = '<?php echo $livro_id; ?>';

                form.appendChild(input);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Função para imprimir livro completo
        function imprimirLivro() {
            const url = 'imprimir_livro_completo.php?id=<?php echo $livro_id; ?>';
            window.open(url, '_blank', 'width=800,height=600,scrollbars=yes,resizable=yes');
        }

        // Event listeners para submits dos formulários
        document.getElementById('formNovaMovimentacao').addEventListener('submit', function(e) {
            // Garantir que o valor correto seja enviado
            const valorInput = document.getElementById('valor');
            const valorHidden = document.getElementById('valor_hidden');

            if (valorInput && valorHidden) {
                // Alterar o name para enviar o valor correto
                valorInput.name = 'valor_display';
                valorHidden.name = 'valor';
            }
        });

        document.getElementById('formEditarMovimentacao').addEventListener('submit', function(e) {
            // Garantir que o valor correto seja enviado
            const editValorInput = document.getElementById('edit_valor');
            const editValorHidden = document.getElementById('edit_valor_hidden');

            if (editValorInput && editValorHidden) {
                // Alterar o name para enviar o valor correto
                editValorInput.name = 'valor_display';
                editValorHidden.name = 'valor';
            }
        });

        // Inicializar data de competência ao carregar
        document.addEventListener('DOMContentLoaded', function() {
            atualizarDataCompetencia();
        });

        function abrirModalFecharDia() {
          var modal = new bootstrap.Modal(document.getElementById('modalFecharDia'));
          modal.show();
        }

        document.addEventListener('DOMContentLoaded', function() {
          var termo = document.getElementById('termoConsentimentoDia');
          var btn = document.getElementById('btnConfirmarFecharDia');
          if (termo && btn) {
            termo.addEventListener('change', function() {
              btn.disabled = !termo.checked;
            });
          }
        });

        function abrirModalAbrirDia() {
            var modal = new bootstrap.Modal(document.getElementById('modalAbrirDia'));
            modal.show();
        }

        function abrirModalHistoricoDia() {
          var modal = new bootstrap.Modal(document.getElementById('modalHistoricoDia'));
          modal.show();
        }
    </script>
</body>
</html>
