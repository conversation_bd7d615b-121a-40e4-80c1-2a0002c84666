# LOG DE PROTEÇÃO DO SISTEMA ACD

**Data de Criação:** 2024-12-19  
**Versão:** 1.0  
**Sistema:** Análise de Crédito e Débito (ACD)  

---

## 📋 RESUMO EXECUTIVO

Este documento registra o status de proteção de todos os arquivos do sistema ACD, documentando quais páginas são públicas e quais requerem autenticação/autorização específica.

### 🎯 CONFIGURAÇÃO ATUAL:
- **Dashboard Público:** `dashboard.php` - Acesso livre
- **Dashboard ACD:** `index.php` - Protegido por permissão ACD
- **Sistema de Controle:** Baseado em botões da tabela `card_buttons`

---

## 🌐 ARQUIVOS PÚBLICOS

### Descrição
Páginas que podem ser acessadas por qualquer pessoa, sem necessidade de login ou permissões especiais.

### Lista de Arquivos:
```
✅ dashboard.php
   - Descrição: Dashboard público com métricas gerais
   - Acesso: Livre para todos
   - Conteúdo: Visualizações básicas do sistema

✅ unauthorized.php
   - Descrição: Página de acesso negado
   - Acesso: Livre para todos
   - Função: Informar sobre falta de permissões
```

**Total:** 2 arquivos públicos

---

## 🔒 ARQUIVOS PROTEGIDOS POR ACD

### Descrição
Páginas que requerem login + permissão específica ACD (baseada em botões do sistema).

### 👤 Arquivos com Proteção Básica ACD:
```
🔒 painelusu.php
   - Descrição: Painel personalizado do usuário
   - Proteção: Login + Permissão ACD
   - Função: Métricas do usuário e PAs de responsabilidade

🔒 formsacd.php
   - Descrição: Formulário de análise ACD
   - Proteção: Login + Permissão ACD
   - Função: Submissão de análises

🔒 buscar_cpf.php
   - Descrição: API de busca por CPF
   - Proteção: Login + Permissão ACD
   - Função: Consulta de dados via AJAX

🔒 buscar_documento.php
   - Descrição: API de busca por documento
   - Proteção: Login + Permissão ACD
   - Função: Consulta de documentos via AJAX
```

### 👑 Arquivos com Proteção ADMIN/GESTOR:
```
🔐 index.php
   - Descrição: Dashboard principal do ACD
   - Proteção: Login + Permissão ACD + Admin/Gestor
   - Função: Análise detalhada de crédito

🔐 rankings.php
   - Descrição: Rankings de Pontos de Atendimento
   - Proteção: Login + Permissão ACD + Admin/Gestor
   - Função: Classificações por performance

🔐 relatorios.php
   - Descrição: Relatórios detalhados
   - Proteção: Login + Permissão ACD + Admin/Gestor
   - Função: Geração de relatórios com filtros

🔐 gerenciar_vinculos.php
   - Descrição: Gerenciamento de vínculos usuário-PA
   - Proteção: Login + Permissão ACD + Admin/Gestor
   - Função: Administração de relacionamentos

🔐 importar_limite.php
   - Descrição: Importação de dados de limite
   - Proteção: Login + Permissão ACD + Admin/Gestor
   - Função: Upload e processamento de dados

🔐 importar_emprestimo.php
   - Descrição: Importação de dados de empréstimo
   - Proteção: Login + Permissão ACD + Admin/Gestor
   - Função: Upload e processamento de dados

🔐 importar_financiamento.php
   - Descrição: Importação de dados de financiamento
   - Proteção: Login + Permissão ACD + Admin/Gestor
   - Função: Upload e processamento de dados

🔐 importar_proposta.php
   - Descrição: Importação de propostas
   - Proteção: Login + Permissão ACD + Admin/Gestor
   - Função: Upload e processamento de dados

🔐 importar_rural.php
   - Descrição: Importação de crédito rural
   - Proteção: Login + Permissão ACD + Admin/Gestor
   - Função: Upload e processamento de dados
```

**Total:** 13+ arquivos protegidos por ACD (4 básicos + 9 admin/gestor)

---

## 🔐 ARQUIVOS PROTEGIDOS POR LOGIN

### Descrição
Páginas que requerem apenas login, sem permissões específicas adicionais.

### Lista de Arquivos:
```
(Nenhum arquivo identificado nesta categoria no momento)
```

**Total:** 0 arquivos

---

## ⚙️ SISTEMA DE CONTROLE DE ACESSO

### 🎯 Lógica de Funcionamento:

1. **Verificação de Login:**
   ```php
   if (!isset($_SESSION['user_id'])) {
       header('Location: ../login.php');
       exit;
   }
   ```

2. **Verificação de Permissão ACD:**
   ```php
   if (!checkACDPermission($_SESSION['user_id'])) {
       header('Location: unauthorized.php');
       exit;
   }
   ```

3. **Verificação Admin/Gestor (páginas específicas):**
   ```php
   if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
       header('Location: unauthorized.php');
       exit;
   }
   ```

### 🔑 Funções de Verificação:
```php
function checkACDPermission($usuario_id) {
    // PRIMEIRO: Admins e gestores têm acesso automático
    if (checkAdminOrManagerPermission($usuario_id)) {
        return true;
    }

    // SEGUNDO: Verifica se tem acesso a QUALQUER botão ACD
    // Acesso por usuário direto OU por setor
    return ($tem_acesso_usuario || $tem_acesso_setor);
}

function checkAdminOrManagerPermission($usuario_id) {
    // Verifica se o usuário é administrador ou gestor
    // APENAS por nivel_acesso_id: 1=Admin, 2=Gestor, 3=Usuário
    $stmt = $pdo->prepare("SELECT nivel_acesso_id FROM usuarios WHERE id = ?");
    $stmt->execute([$usuario_id]);
    $usuario = $stmt->fetch(PDO::FETCH_ASSOC);

    if (isset($usuario['nivel_acesso_id'])) {
        $nivel = intval($usuario['nivel_acesso_id']);
        return ($nivel === 1 || $nivel === 2);
    }
    return false;
}
```

### 🎨 Sidebar Dinâmico:
O sidebar agora oculta automaticamente os botões que o usuário não tem permissão:

```php
<?php if ($tem_acesso_acd && $eh_admin_gestor): ?>
    <!-- Botões apenas para Admin/Gestor -->
<?php elseif ($tem_acesso_acd): ?>
    <!-- Botões para usuários ACD -->
<?php else: ?>
    <!-- Mensagem para usuários sem acesso -->
<?php endif; ?>
```

### 🚀 Fluxo de Acesso Atualizado:

#### **👑 Administradores/Gestores**:
- ✅ **Acesso automático**: ACD concedido automaticamente
- ✅ **Pode acessar**: Todas as páginas do sistema
- ✅ **Sidebar completo**: Todos os botões visíveis
- ✅ **Sem restrições**: Acesso total às funcionalidades

#### **👥 Usuário Comum com ACD**:
- ✅ **Pode acessar**: dashboard.php (público)
- ✅ **Pode acessar**: painelusu.php, formsacd.php, APIs
- ❌ **NÃO pode acessar**: index.php, rankings.php, relatórios, importações
- 🎯 **Sidebar limitado**: Apenas botões básicos ACD

#### **❌ Usuário sem ACD**:
- ✅ **Pode acessar**: Apenas dashboard.php (público)
- ❌ **NÃO pode acessar**: Qualquer página ACD
- ℹ️ **Sidebar mínimo**: Apenas dashboard público + mensagem informativa

### 🎯 Critérios para Admin/Gestor:

#### **📊 Por Nível de Acesso (ÚNICO CRITÉRIO)**:
- ✅ `nivel_acesso_id = 1` (👑 Administrador)
- ✅ `nivel_acesso_id = 2` (👔 Gestor)
- ❌ `nivel_acesso_id = 3` (👤 Usuário comum)

**Nota:** Removidas verificações por tipo_usuario e setor devido a problemas de compatibilidade com o banco de dados.

### 📊 Tabelas Envolvidas:
- `card_buttons` - Definição dos botões ACD
- `card_button_usuarios` - Acesso direto por usuário
- `card_button_setores` - Acesso por setor
- `usuario_setor` - Relacionamento usuário-setor

---

## 🎯 BOTÕES ACD CONFIGURADOS

### Lista de Botões:
```
1. Painel do Usuário (acd/painelusu.php)
2. Rankings de PAs (acd/rankings.php)
3. Relatórios ACD (acd/relatorios.php)
4. Formulário ACD (acd/formsacd.php)
```

### 🔧 Regra de Acesso:
**Se o usuário tiver acesso a QUALQUER botão ACD, ele pode acessar TODA a pasta acd (exceto páginas públicas).**

---

## 📈 ESTATÍSTICAS DE SEGURANÇA

### 🛡️ Nível de Proteção:
- **Páginas Públicas:** ~10% (acesso livre)
- **Páginas Protegidas:** ~90% (requerem permissão)

### ✅ Status de Segurança:
- **🟢 EXCELENTE:** Sistema adequadamente protegido
- **🟢 CONTROLE GRANULAR:** Baseado em permissões de botão
- **🟢 FLEXIBILIDADE:** Administração centralizada

---

## 🚨 RECOMENDAÇÕES DE SEGURANÇA

### ✅ Implementado:
1. **Verificação dupla:** Login + Permissão específica
2. **Redirecionamento seguro:** Para páginas apropriadas
3. **APIs protegidas:** Verificação em endpoints AJAX
4. **Controle centralizado:** Via tabela card_buttons

### 🔄 Monitoramento:
1. **Revisar periodicamente** os acessos concedidos
2. **Auditar logs** de acesso às páginas protegidas
3. **Verificar** se novos arquivos estão adequadamente protegidos

---

## 📝 HISTÓRICO DE ALTERAÇÕES

### 2024-12-19 - v1.0:
- ✅ Implementação inicial do sistema de proteção ACD
- ✅ Configuração de `dashboard.php` como público
- ✅ Configuração de `index.php` como protegido
- ✅ Proteção de todas as páginas administrativas
- ✅ Implementação de APIs protegidas

---

## 🔗 ARQUIVOS RELACIONADOS

### 📁 Arquivos de Sistema:
- `check_acd_permission.php` - Função de verificação de acesso
- `unauthorized.php` - Página de acesso negado
- `components/sidebar.php` - Navegação do sistema

### 📁 Arquivos de Configuração:
- `../config/database.php` - Conexão com banco de dados
- `../config/funcoes.php` - Funções auxiliares

---

## 📞 CONTATO E SUPORTE

Para questões sobre permissões de acesso ou problemas de segurança, entre em contato com o administrador do sistema.

**Última Atualização:** 2024-12-19  
**Próxima Revisão:** 2025-01-19
