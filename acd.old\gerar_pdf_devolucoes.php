<?php
session_start();
require_once '../config/database.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    die('Acesso negado. Faça login para acessar este recurso.');
}

// Verificar se foi passado o ID do usuário
if (!isset($_GET['usuario_id'])) {
    die('ID do usuário não fornecido.');
}

$usuario_id = $_GET['usuario_id'];

// Parâmetros de filtro
$filtro_pa = isset($_GET['pa']) ? $_GET['pa'] : '';
$filtro_periodo = isset($_GET['periodo']) ? $_GET['periodo'] : 'mes_atual';
$data_inicio_custom = isset($_GET['data_inicio']) ? $_GET['data_inicio'] : '';
$data_fim_custom = isset($_GET['data_fim']) ? $_GET['data_fim'] : '';
$mostrar_filtros = !isset($_GET['print']) || $_GET['print'] !== '1';

try {
    // Buscar informações do usuário
    $stmt = $pdo->prepare("SELECT nome_completo, email FROM usuarios WHERE id = ?");
    $stmt->execute([$usuario_id]);
    $usuario_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$usuario_info) {
        die('Usuário não encontrado.');
    }
    
    // Buscar todos os vínculos do usuário (ativos e inativos)
    $stmt = $pdo->prepare("
        SELECT
            v.pa_id,
            v.data_inicio,
            v.data_fim,
            v.status,
            p.nome as pa_nome,
            p.numero as pa_numero
        FROM acd_usuario_pa v
        INNER JOIN pontos_atendimento p ON v.pa_id = p.id
        WHERE v.usuario_id = ?
        ORDER BY v.data_inicio ASC
    ");
    $stmt->execute([$usuario_id]);
    $todos_vinculos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($todos_vinculos)) {
        die('Usuário não possui PAs vinculados.');
    }

    // Separar vínculos ativos e inativos baseado na data de fim
    $vinculos_ativos = [];
    $vinculos_inativos = [];

    foreach ($todos_vinculos as $vinculo) {
        // Se não tem data de fim = ativo, se tem data de fim = inativo
        $is_ativo = (is_null($vinculo['data_fim']) || $vinculo['data_fim'] === null || $vinculo['data_fim'] === '');

        if ($is_ativo) {
            $vinculos_ativos[] = $vinculo;
        } else {
            $vinculos_inativos[] = $vinculo;
        }
    }

    // Extrair apenas os IDs dos PAs para compatibilidade
    $pas_responsavel = array_column($todos_vinculos, 'pa_id');
    
    // Construir condições de filtro de período
    $condicao_periodo = '';
    $periodo_descricao = '';

    switch ($filtro_periodo) {
        case 'semana_atual':
            $condicao_periodo = "AND YEARWEEK(data_criacao, 1) = YEARWEEK(CURDATE(), 1)";
            $periodo_descricao = "Semana Atual";
            break;
        case 'mes_atual':
            $condicao_periodo = "AND YEAR(data_criacao) = YEAR(CURDATE()) AND MONTH(data_criacao) = MONTH(CURDATE())";
            $periodo_descricao = "Mês Atual (" . date('m/Y') . ")";
            break;
        case 'ano_atual':
            $condicao_periodo = "AND YEAR(data_criacao) = YEAR(CURDATE())";
            $periodo_descricao = "Ano Atual (" . date('Y') . ")";
            break;
        case 'ultimos_30_dias':
            $condicao_periodo = "AND data_criacao >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
            $periodo_descricao = "Últimos 30 Dias";
            break;
        case 'ultimos_90_dias':
            $condicao_periodo = "AND data_criacao >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)";
            $periodo_descricao = "Últimos 90 Dias";
            break;
        case 'personalizado':
            if (!empty($data_inicio_custom) && !empty($data_fim_custom)) {
                $condicao_periodo = "AND DATE(data_criacao) BETWEEN ? AND ?";
                $periodo_descricao = "Período: " . date('d/m/Y', strtotime($data_inicio_custom)) . " a " . date('d/m/Y', strtotime($data_fim_custom));
            } else {
                $condicao_periodo = "AND YEAR(data_criacao) = YEAR(CURDATE()) AND MONTH(data_criacao) = MONTH(CURDATE())";
                $periodo_descricao = "Mês Atual (período personalizado inválido)";
            }
            break;
        default:
            $condicao_periodo = "AND YEAR(data_criacao) = YEAR(CURDATE()) AND MONTH(data_criacao) = MONTH(CURDATE())";
            $periodo_descricao = "Mês Atual (" . date('m/Y') . ")";
    }

    // Buscar propostas devolvidas dos PAs responsáveis (considerando período de vínculo)
    $propostas_devolvidas = [];
    $pas_disponiveis = []; // Para o filtro

    foreach ($todos_vinculos as $vinculo) {
        $pa_id = $vinculo['pa_id'];
        $pa_nome = $vinculo['pa_nome'];
        $data_inicio_vinculo = $vinculo['data_inicio'];
        $data_fim_vinculo = $vinculo['data_fim'] ?? date('Y-m-d');

        $pas_disponiveis[$pa_id] = $pa_nome;

        // Verificar se deve filtrar por PA específico
        if (!empty($filtro_pa) && $pa_nome !== $filtro_pa) {
            continue;
        }

            // Construir query com filtros incluindo período de vínculo
            $sql = "
                SELECT
                    id,
                    pa,
                    documento,
                    nome,
                    mesa,
                    data_criacao,
                    motivo_devolucao
                FROM acd_formularios
                WHERE pa = ?
                AND acao = 'devolver'
                AND DATE(data_criacao) >= ?
                AND DATE(data_criacao) <= ?
                $condicao_periodo
                ORDER BY data_criacao DESC
            ";

            $stmt = $pdo->prepare($sql);

            // Executar com parâmetros incluindo período de vínculo
            if ($filtro_periodo === 'personalizado' && !empty($data_inicio_custom) && !empty($data_fim_custom)) {
                // Para período personalizado, usar as datas mais restritivas
                $data_inicio_final = max($data_inicio_vinculo, $data_inicio_custom);
                $data_fim_final = min($data_fim_vinculo, $data_fim_custom);
                $stmt->execute([$pa_nome, $data_inicio_final, $data_fim_final, $data_inicio_custom, $data_fim_custom]);
            } else {
                $stmt->execute([$pa_nome, $data_inicio_vinculo, $data_fim_vinculo]);
            }

            $propostas_pa = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Adicionar informações do vínculo às propostas
            foreach ($propostas_pa as &$proposta) {
                $proposta['vinculo_inicio'] = $data_inicio_vinculo;
                $proposta['vinculo_fim'] = $vinculo['data_fim'];

                // Se não tem data de fim = ativo, se tem data de fim = inativo
                $proposta['vinculo_ativo'] = (is_null($vinculo['data_fim']) || $vinculo['data_fim'] === null || $vinculo['data_fim'] === '');
            }

            if (!empty($propostas_pa)) {
                $propostas_devolvidas[$pa_nome] = $propostas_pa;
            }
    }
    
    if (empty($propostas_devolvidas)) {
        $filtro_info = !empty($filtro_pa) ? " para o PA '$filtro_pa'" : " para todos os PAs";
        die("Nenhuma proposta devolvida encontrada no período '$periodo_descricao'$filtro_info.");
    }
    
} catch (Exception $e) {
    die('Erro ao buscar dados: ' . $e->getMessage());
}

// Configurar cabeçalhos para PDF
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Devoluções - <?php echo htmlspecialchars($usuario_info['nome_completo']); ?></title>
    <style>
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
            .page-break { page-break-before: always; }
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 20px;
            color: #333;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #003641;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #003641;
            margin: 0;
            font-size: 24px;
        }
        
        .header h2 {
            color: #00AE9D;
            margin: 5px 0;
            font-size: 18px;
        }
        
        .info-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .info-row {
            display: block;
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .info-row strong {
            display: inline-block;
            min-width: 200px;
            color: #003641;
        }
        
        .pa-section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .pa-header {
            background: #003641;
            color: white;
            padding: 10px 15px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .propostas-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }
        
        .propostas-table th {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
            font-weight: bold;
            font-size: 11px;
        }
        
        .propostas-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            font-size: 10px;
            vertical-align: top;
        }
        
        .propostas-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .valor {
            text-align: right;
            font-weight: bold;
        }
        
        .data {
            white-space: nowrap;
        }
        
        .observacoes {
            max-width: 200px;
            word-wrap: break-word;
        }
        
        .summary {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .summary h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .no-print {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            background: #003641;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 0 10px;
        }
        
        .btn:hover {
            background: #00AE9D;
        }

        .filters-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .filters-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #003641;
        }

        .form-group select,
        .form-group input {
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn-filter {
            background: #003641;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            height: fit-content;
        }

        .btn-filter:hover {
            background: #00AE9D;
        }

        .current-filters {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .current-filters strong {
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Relatório de Propostas Devolvidas</h1>
        <h2><?php echo htmlspecialchars($usuario_info['nome_completo']); ?></h2>
        <p><?php echo $periodo_descricao; ?> | Gerado em: <?php echo date('d/m/Y H:i'); ?></p>
        <?php if (!empty($filtro_pa)): ?>
            <p style="color: #00AE9D; font-weight: bold;">Filtrado por PA: <?php echo htmlspecialchars($filtro_pa); ?></p>
        <?php endif; ?>
    </div>

    <div class="info-box">
        <div class="info-row">
            <strong>Usuário:</strong>
            <span><?php echo htmlspecialchars($usuario_info['nome_completo']); ?></span>
        </div>
        <div class="info-row">
            <strong>E-mail:</strong>
            <span><?php echo htmlspecialchars($usuario_info['email']); ?></span>
        </div>
        <div class="info-row">
            <strong>ID do Usuário:</strong>
            <span><?php echo htmlspecialchars($usuario_id); ?></span>
        </div>
        <div class="info-row">
            <strong>Total de Vínculos:</strong>
            <span><?php echo count($todos_vinculos); ?> vínculo(s)</span>
        </div>
        <div class="info-row">
            <strong>PAs Ativos:</strong>
            <span><?php echo count($vinculos_ativos); ?> PA(s)</span>
        </div>
        <div class="info-row">
            <strong>PAs Inativos:</strong>
            <span><?php echo count($vinculos_inativos); ?> PA(s)</span>
        </div>
        <div class="info-row">
            <strong>PAs com Devoluções:</strong>
            <span><?php echo count($propostas_devolvidas); ?> PA(s)</span>
        </div>

        <?php if (!empty($vinculos_ativos)): ?>
            <div class="info-row">
                <strong>Responsabilidades Ativas:</strong>
                <span>
                    <?php
                    $responsabilidades_ativas = [];
                    foreach ($vinculos_ativos as $vinculo) {
                        $responsabilidades_ativas[] = $vinculo['pa_nome'] . ' (desde ' . date('d/m/Y', strtotime($vinculo['data_inicio'])) . ')';
                    }
                    echo implode(', ', $responsabilidades_ativas);
                    ?>
                </span>
            </div>
        <?php endif; ?>

        <?php if (!empty($vinculos_inativos)): ?>
            <div class="info-row">
                <strong>Histórico de Responsabilidades:</strong>
                <span>
                    <?php
                    $responsabilidades_historicas = [];
                    foreach ($vinculos_inativos as $vinculo) {
                        $periodo = date('d/m/Y', strtotime($vinculo['data_inicio']));
                        if ($vinculo['data_fim']) {
                            $periodo .= ' até ' . date('d/m/Y', strtotime($vinculo['data_fim']));
                        }
                        $responsabilidades_historicas[] = $vinculo['pa_nome'] . ' (' . $periodo . ')';
                    }
                    echo implode(', ', $responsabilidades_historicas);
                    ?>
                </span>
            </div>
        <?php endif; ?>

    </div>

    <?php if ($mostrar_filtros): ?>
        <!-- Seção de Filtros -->
        <div class="filters-section no-print">
            <h3 style="margin-top: 0; color: #003641;">🔍 Filtros</h3>

            <?php if (!empty($filtro_pa) || $filtro_periodo !== 'mes_atual'): ?>
                <div class="current-filters">
                    <strong>Filtros Ativos:</strong>
                    <?php if (!empty($filtro_pa)): ?>
                        PA: <?php echo htmlspecialchars($filtro_pa); ?>
                    <?php endif; ?>
                    <?php if (!empty($filtro_pa) && $filtro_periodo !== 'mes_atual'): ?> | <?php endif; ?>
                    <?php if ($filtro_periodo !== 'mes_atual'): ?>
                        Período: <?php echo $periodo_descricao; ?>
                    <?php endif; ?>
                    <a href="?usuario_id=<?php echo urlencode($usuario_id); ?>" style="margin-left: 10px; color: #dc3545;">✖ Limpar Filtros</a>
                </div>
            <?php endif; ?>

            <form method="GET" class="filters-form">
                <input type="hidden" name="usuario_id" value="<?php echo htmlspecialchars($usuario_id); ?>">

                <div class="form-group">
                    <label for="pa">PA (Posto de Atendimento)</label>
                    <select name="pa" id="pa">
                        <option value="">Todos os PAs</option>
                        <?php foreach ($pas_disponiveis as $pa_id => $pa_nome): ?>
                            <option value="<?php echo htmlspecialchars($pa_nome); ?>"
                                    <?php echo ($filtro_pa === $pa_nome) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($pa_nome); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label for="periodo">Período</label>
                    <select name="periodo" id="periodo" onchange="toggleCustomDates()">
                        <option value="semana_atual" <?php echo ($filtro_periodo === 'semana_atual') ? 'selected' : ''; ?>>Semana Atual</option>
                        <option value="mes_atual" <?php echo ($filtro_periodo === 'mes_atual') ? 'selected' : ''; ?>>Mês Atual</option>
                        <option value="ano_atual" <?php echo ($filtro_periodo === 'ano_atual') ? 'selected' : ''; ?>>Ano Atual</option>
                        <option value="ultimos_30_dias" <?php echo ($filtro_periodo === 'ultimos_30_dias') ? 'selected' : ''; ?>>Últimos 30 Dias</option>
                        <option value="ultimos_90_dias" <?php echo ($filtro_periodo === 'ultimos_90_dias') ? 'selected' : ''; ?>>Últimos 90 Dias</option>
                        <option value="personalizado" <?php echo ($filtro_periodo === 'personalizado') ? 'selected' : ''; ?>>Período Personalizado</option>
                    </select>
                </div>

                <div class="form-group" id="data_inicio_group" style="<?php echo ($filtro_periodo !== 'personalizado') ? 'display: none;' : ''; ?>">
                    <label for="data_inicio">Data Início</label>
                    <input type="date" name="data_inicio" id="data_inicio" value="<?php echo htmlspecialchars($data_inicio_custom); ?>">
                </div>

                <div class="form-group" id="data_fim_group" style="<?php echo ($filtro_periodo !== 'personalizado') ? 'display: none;' : ''; ?>">
                    <label for="data_fim">Data Fim</label>
                    <input type="date" name="data_fim" id="data_fim" value="<?php echo htmlspecialchars($data_fim_custom); ?>">
                </div>

                <div class="form-group">
                    <button type="submit" class="btn-filter">🔍 Filtrar</button>
                </div>
            </form>
        </div>
    <?php endif; ?>

    <div class="no-print">
        <button onclick="window.print()" class="btn">🖨️ Imprimir</button>
        <a href="index.php" class="btn">← Voltar ao Dashboard</a>
        <?php if ($mostrar_filtros): ?>
            <a href="?usuario_id=<?php echo urlencode($usuario_id); ?>&print=1<?php echo !empty($filtro_pa) ? '&pa=' . urlencode($filtro_pa) : ''; ?><?php echo $filtro_periodo !== 'mes_atual' ? '&periodo=' . urlencode($filtro_periodo) : ''; ?><?php echo !empty($data_inicio_custom) ? '&data_inicio=' . urlencode($data_inicio_custom) : ''; ?><?php echo !empty($data_fim_custom) ? '&data_fim=' . urlencode($data_fim_custom) : ''; ?>" class="btn">📄 Versão para Impressão</a>
        <?php endif; ?>
    </div>

    <?php
    $total_geral = 0;

    foreach ($propostas_devolvidas as $pa_nome => $propostas):
        $total_pa = count($propostas);
        $total_geral += $total_pa;
    ?>
        <div class="pa-section">
            <div class="pa-header">
                📍 <?php echo htmlspecialchars($pa_nome); ?> - <?php echo $total_pa; ?> proposta(s) devolvida(s)
                <?php
                // Buscar informações do vínculo para este PA
                $vinculo_info = null;
                foreach ($todos_vinculos as $v) {
                    if ($v['pa_nome'] === $pa_nome) {
                        $vinculo_info = $v;
                        break;
                    }
                }

                if ($vinculo_info) {
                    // Se não tem data de fim = ativo, se tem data de fim = inativo
                    $is_ativo = (is_null($vinculo_info['data_fim']) || $vinculo_info['data_fim'] === null || $vinculo_info['data_fim'] === '');

                    echo ' | ';
                    if ($is_ativo) {
                        echo '🟢 Ativo desde ' . date('d/m/Y', strtotime($vinculo_info['data_inicio']));
                    } else {
                        echo '🔴 Inativo (' . date('d/m/Y', strtotime($vinculo_info['data_inicio']));
                        if ($vinculo_info['data_fim']) {
                            echo ' até ' . date('d/m/Y', strtotime($vinculo_info['data_fim']));
                        }
                        echo ')';
                    }
                }
                ?>
            </div>
            
            <table class="propostas-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Data</th>
                        <th>Documento</th>
                        <th>Nome do Cliente</th>
                        <th>Mesa</th>
                        <th>Motivo da Devolução</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($propostas as $proposta): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($proposta['id']); ?></td>
                            <td class="data"><?php echo date('d/m/Y H:i', strtotime($proposta['data_criacao'])); ?></td>
                            <td><?php echo htmlspecialchars($proposta['documento']); ?></td>
                            <td><?php echo htmlspecialchars($proposta['nome']); ?></td>
                            <td><?php echo ucfirst(htmlspecialchars($proposta['mesa'])); ?></td>
                            <td class="observacoes"><?php echo htmlspecialchars($proposta['motivo_devolucao']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php endforeach; ?>

    <div class="summary">
        <h3>📊 Resumo Geral</h3>
        <div class="info-row">
            <strong>Total de Propostas Devolvidas:</strong>
            <span><?php echo number_format($total_geral, 0, ',', '.'); ?></span>
        </div>
        <div class="info-row">
            <strong>Período:</strong>
            <span><?php echo $periodo_descricao; ?></span>
        </div>
        <?php if (!empty($filtro_pa)): ?>
            <div class="info-row">
                <strong>PA Filtrado:</strong>
                <span><?php echo htmlspecialchars($filtro_pa); ?></span>
            </div>
        <?php endif; ?>
        <div class="info-row">
            <strong>Data de Geração:</strong>
            <span><?php echo date('d/m/Y H:i:s'); ?></span>
        </div>
    </div>

    <script>
        // Auto-print quando solicitado via parâmetro
        if (new URLSearchParams(window.location.search).get('print') === '1') {
            window.onload = function() {
                setTimeout(function() {
                    window.print();
                }, 500);
            };
        }

        // Controlar exibição dos campos de data personalizada
        function toggleCustomDates() {
            const periodo = document.getElementById('periodo').value;
            const dataInicioGroup = document.getElementById('data_inicio_group');
            const dataFimGroup = document.getElementById('data_fim_group');

            if (periodo === 'personalizado') {
                dataInicioGroup.style.display = 'flex';
                dataFimGroup.style.display = 'flex';
            } else {
                dataInicioGroup.style.display = 'none';
                dataFimGroup.style.display = 'none';
            }
        }

        // Inicializar estado dos campos de data
        document.addEventListener('DOMContentLoaded', function() {
            toggleCustomDates();
        });
    </script>
</body>
</html>
