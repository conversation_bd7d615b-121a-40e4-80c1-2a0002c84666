<?php
require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';
session_start();

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se o usuário tem permissão para acessar a pasta ACD
if (!checkACDPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Verificar se o usuário é administrador ou gestor
if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

$mensagem = '';
$tipo_mensagem = '';

// Processar upload do arquivo
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['arquivo'])) {
    $arquivo = $_FILES['arquivo'];
    $tipo = $_POST['tipo'];
    
    // Validar tipo de arquivo
    $tipos_permitidos = ['csv', 'xlsx', 'xls'];
    $extensao = strtolower(pathinfo($arquivo['name'], PATHINFO_EXTENSION));
    
    if (!in_array($extensao, $tipos_permitidos)) {
        $mensagem = "Tipo de arquivo não permitido. Use CSV ou Excel.";
        $tipo_mensagem = "danger";
    } elseif ($arquivo['error'] !== UPLOAD_ERR_OK) {
        $mensagem = "Erro no upload do arquivo.";
        $tipo_mensagem = "danger";
    } else {
        try {
            // Aqui você implementará a lógica de processamento do arquivo
            // Por enquanto, apenas registra o upload
            $mensagem = "Arquivo recebido com sucesso! Processamento em desenvolvimento.";
            $tipo_mensagem = "success";
            
            // Log da ação
            $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes) VALUES (?, ?, ?)");
            $stmt->execute([
                $_SESSION['user_id'],
                'IMPORTACAO_LIMITE',
                "Upload de arquivo: {$arquivo['name']}"
            ]);
        } catch (PDOException $e) {
            $mensagem = "Erro ao processar arquivo: " . $e->getMessage();
            $tipo_mensagem = "danger";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="../assets/images/Sicoob.ico">
    <title>Importar Limites - Análise de Crédito</title>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <style>
        :root {
            /* Cores do Sicoob */
            --sicoob-turquesa: #00AE9D;
            --sicoob-verde-escuro: #003641;
            --sicoob-branco: #FFFFFF;
            --sicoob-verde-claro: #C9D200;
            --sicoob-verde-medio: #7DB61C;
            --sicoob-roxo: #49479D;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .wrapper {
            display: flex;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .content-container {
            background: var(--sicoob-branco);
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 8px rgba(0, 54, 65, 0.1);
            border-top: 4px solid var(--sicoob-turquesa);
        }

        .content-title {
            color: var(--sicoob-verde-escuro);
            margin-bottom: 2rem;
            font-weight: 600;
            position: relative;
            padding-bottom: 1rem;
        }

        .content-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--sicoob-turquesa);
        }

        .upload-area {
            border: 2px dashed var(--sicoob-turquesa);
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            background: rgba(0, 174, 157, 0.05);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            background: rgba(0, 174, 157, 0.1);
        }

        .upload-area i {
            font-size: 3rem;
            color: var(--sicoob-turquesa);
            margin-bottom: 1rem;
        }

        .btn-primary {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
        }

        .btn-primary:hover {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
        }

        .alert {
            border-radius: 4px;
            border: none;
        }

        .alert-success {
            background-color: rgba(125, 182, 28, 0.1);
            color: var(--sicoob-verde-medio);
        }

        .alert-danger {
            background-color: rgba(73, 71, 157, 0.1);
            color: var(--sicoob-roxo);
        }

        .file-info {
            margin-top: 1rem;
            font-size: 0.9rem;
            color: var(--sicoob-verde-escuro);
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'components/sidebar.php'; ?>

        <div class="main-content">
            <div class="content-container">
                <h2 class="content-title">Importar Limites</h2>

                <?php if ($mensagem): ?>
                    <div class="alert alert-<?php echo $tipo_mensagem; ?> alert-dismissible fade show" role="alert">
                        <i class="fas <?php echo $tipo_mensagem === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> me-2"></i>
                        <?php echo $mensagem; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    <div class="upload-area" id="dropZone">
                        <i class="fas fa-file-upload"></i>
                        <h4>Arraste seu arquivo aqui ou clique para selecionar</h4>
                        <p class="text-muted">Formatos aceitos: CSV, XLSX, XLS</p>
                        <input type="file" name="arquivo" id="arquivo" class="d-none" accept=".csv,.xlsx,.xls">
                        <div class="file-info" id="fileInfo"></div>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>Importar Arquivo
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Click no upload area
            $('#dropZone').click(function() {
                $('#arquivo').click();
            });

            // Drag and drop
            $('#dropZone').on('dragover', function(e) {
                e.preventDefault();
                $(this).addClass('bg-light');
            }).on('dragleave', function(e) {
                e.preventDefault();
                $(this).removeClass('bg-light');
            }).on('drop', function(e) {
                e.preventDefault();
                $(this).removeClass('bg-light');
                
                const files = e.originalEvent.dataTransfer.files;
                if (files.length) {
                    $('#arquivo').prop('files', files);
                    updateFileInfo(files[0]);
                }
            });

            // Arquivo selecionado
            $('#arquivo').change(function() {
                if (this.files.length) {
                    updateFileInfo(this.files[0]);
                }
            });

            // Atualizar informações do arquivo
            function updateFileInfo(file) {
                const fileInfo = $('#fileInfo');
                const size = (file.size / 1024).toFixed(2);
                fileInfo.html(`
                    <strong>Arquivo selecionado:</strong><br>
                    Nome: ${file.name}<br>
                    Tamanho: ${size} KB
                `);
            }
        });
    </script>
</body>
</html> 