<?php
// Definir cabeçalho para garantir que a resposta seja JSON
header('Content-Type: application/json');

// Iniciar captura de saída para evitar que HTML acidental seja enviado
ob_start();

try {
    // Usar caminhos absolutos para os includes
    require_once __DIR__ . '/../../auth_check.php';
    
    // Verificar se o database.php está no diretório /d/ ou no diretório /d/cobranca/
    if (file_exists(__DIR__ . '/../../config/database.php')) {
        require_once __DIR__ . '/../../config/database.php';
    } else {
        require_once __DIR__ . '/../config/database.php';
    }

    // Log para depuração
    error_log("=== INÍCIO DO PROCESSO DE REVERSÃO DE PAGAMENTO DE HONORÁRIO ===");
    error_log("Método: " . $_SERVER['REQUEST_METHOD']);
    error_log("Dados recebidos: " . print_r($_POST, true));

    // Verificar se a requisição é POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido.');
    }

    // Validar parâmetros obrigatórios
    if (!isset($_POST['honorario_id']) || !is_numeric($_POST['honorario_id'])) {
        throw new Exception('ID do honorário inválido.');
    }

    $honorario_id = (int)$_POST['honorario_id'];
    error_log("Honorário ID para reversão: " . $honorario_id);

    // Iniciar transação
    $pdo->beginTransaction();

    // Verificar se o honorário existe e está pago
    $stmt = $pdo->prepare("SELECT * FROM cbp_honorarios WHERE id = ? AND status = 'PAGO'");
    $stmt->execute([$honorario_id]);
    $honorario = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$honorario) {
        throw new Exception('Honorário não encontrado ou não está pago.');
    }
    
    error_log("Dados do honorário encontrado para reversão:");
    error_log(print_r($honorario, true));
    
    // Reverter o status do honorário
    $stmt = $pdo->prepare("
        UPDATE cbp_honorarios 
        SET status = 'PENDENTE',
            data_pagamento = NULL,
            observacoes = NULL,
            updated_at = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$honorario_id]);
    
    if ($stmt->rowCount() === 0) {
        throw new Exception('Falha ao reverter o status do honorário.');
    }

    error_log("Honorário revertido com sucesso");

    // Buscar informações detalhadas do honorário para o log
    $stmt = $pdo->prepare("
        SELECT 
            h.*,
            a.nome AS nome_associado,
            adv.nome AS nome_advogado,
            p.numero_processo
        FROM cbp_honorarios h
        LEFT JOIN cbp_associados a ON h.associado_id = a.id
        LEFT JOIN cbp_advogados adv ON h.advogado_id = adv.id
        LEFT JOIN cbp_processos_judiciais p ON h.processo_id = p.id
        WHERE h.id = ?
    ");
    $stmt->execute([$honorario_id]);
    $honorario_detalhes = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Preparar detalhes para o log
    $detalhes_log = "Reversão de pagamento de honorário ID: {$honorario_id}";
    
    if (!empty($honorario_detalhes['nome_associado'])) {
        $detalhes_log .= ", Associado: {$honorario_detalhes['nome_associado']}";
    }
    
    if (!empty($honorario_detalhes['numero_processo'])) {
        $detalhes_log .= ", Processo: {$honorario_detalhes['numero_processo']}";
    }
    
    if (!empty($honorario_detalhes['nome_advogado'])) {
        $detalhes_log .= ", Advogado: {$honorario_detalhes['nome_advogado']}";
    }
    
    $detalhes_log .= ", Valor: R$ " . number_format($honorario_detalhes['valor_honorario'], 2, ',', '.');
    
    // Inserir no log
    $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$_SESSION['user_id'], 'Reversão de Pagamento de Honorário', $detalhes_log]);
    
    error_log("Registro de log criado para a reversão do pagamento do honorário");

    // Commit
    $pdo->commit();
    error_log("=== FIM DA REVERSÃO DE PAGAMENTO DE HONORÁRIO - SUCESSO ===");

    // Limpar buffer de saída antes de enviar o JSON
    ob_end_clean();
    
    echo json_encode(['success' => true, 'message' => 'Pagamento revertido com sucesso.']);

} catch (PDOException $e) {
    // Rollback em caso de erro com o banco de dados
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    
    error_log("=== ERRO NA REVERSÃO DE PAGAMENTO DE HONORÁRIO (PDO) ===");
    error_log("Mensagem: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    error_log("=== FIM DO ERRO ===");

    // Limpar buffer de saída antes de enviar o JSON
    ob_end_clean();
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro de banco de dados: ' . $e->getMessage()
    ]);

} catch (Exception $e) {
    // Rollback em caso de qualquer outro erro
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    
    error_log("=== ERRO NA REVERSÃO DE PAGAMENTO DE HONORÁRIO ===");
    error_log("Mensagem: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    error_log("=== FIM DO ERRO ===");

    // Limpar buffer de saída antes de enviar o JSON
    ob_end_clean();
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 