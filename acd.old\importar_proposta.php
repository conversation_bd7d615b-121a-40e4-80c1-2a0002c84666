<?php
require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';
session_start();

// Aumentar o tempo limite de execução para 15 minutos
set_time_limit(900);

// Definir o tamanho do lote
define('BATCH_SIZE', 100);

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se o usuário tem permissão para acessar a pasta ACD
if (!checkACDPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Verificar se o usuário é administrador ou gestor
if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Configurações da API
$api_config = [
    'api_user' => 'UFL7GXZ14LU9NOR',
    'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
    'api_module' => 'Usuarios',
    'base_url' => 'https://intranet.sicoobcredilivre.com.br/api',
    'timeout' => 30
];

$mensagem = '';
$tipo_mensagem = '';

// Função para normalizar o nome da mesa
function normalizarMesa($mesa) {
    // Converter para UTF-8 se necessário
    if (!mb_check_encoding($mesa, 'UTF-8')) {
        $mesa = mb_convert_encoding($mesa, 'UTF-8', 'auto');
    }
    
    // Converter para minúsculo
    $mesa = mb_strtolower(trim($mesa), 'UTF-8');
    
    // Primeiro, tentar converter caracteres acentuados usando transliterator
    if (class_exists('Transliterator')) {
        $transliterator = Transliterator::create('Any-Latin; Latin-ASCII');
        $mesa = $transliterator->transliterate($mesa);
    } else {
        // Fallback para conversão manual se Transliterator não estiver disponível
        $acentos = [
            'á' => 'a', 'à' => 'a', 'â' => 'a', 'ã' => 'a', 'ä' => 'a',
            'é' => 'e', 'è' => 'e', 'ê' => 'e', 'ë' => 'e',
            'í' => 'i', 'ì' => 'i', 'î' => 'i', 'ï' => 'i',
            'ó' => 'o', 'ò' => 'o', 'ô' => 'o', 'õ' => 'o', 'ö' => 'o',
            'ú' => 'u', 'ù' => 'u', 'û' => 'u', 'ü' => 'u',
            'ç' => 'c',
            'Á' => 'a', 'À' => 'a', 'Â' => 'a', 'Ã' => 'a', 'Ä' => 'a',
            'É' => 'e', 'È' => 'e', 'Ê' => 'e', 'Ë' => 'e',
            'Í' => 'i', 'Ì' => 'i', 'Î' => 'i', 'Ï' => 'i',
            'Ó' => 'o', 'Ò' => 'o', 'Ô' => 'o', 'Õ' => 'o', 'Ö' => 'o',
            'Ú' => 'u', 'Ù' => 'u', 'Û' => 'u', 'Ü' => 'u',
            'Ç' => 'c'
        ];
        $mesa = str_replace(array_keys($acentos), array_values($acentos), $mesa);
    }
    
    // Remover caracteres especiais e espaços
    $mesa = preg_replace('/[^a-z0-9]/', '', $mesa);
    
    // Mapa de substituição para mesas
    $mapa_mesas = [
        'emprestimo' => 'emprestimo',
        'emprstimo' => 'emprestimo',
        'emprstmo' => 'emprestimo',
        'emprstm' => 'emprestimo',
        'emprst' => 'emprestimo',
        'emprs' => 'emprestimo',
        'empr' => 'emprestimo',
        'emp' => 'emprestimo',
        'limite' => 'limite',
        'limites' => 'limite',
        'rural' => 'rural',
        'cartao' => 'cartao',
        'carto' => 'cartao'
    ];
    
    // Log para debug
    error_log("Mesa original: " . $mesa);
    error_log("Mesa após normalização: " . ($mapa_mesas[$mesa] ?? $mesa));
    
    return $mapa_mesas[$mesa] ?? $mesa;
}

// Função para formatar CPF/CNPJ (apenas números)
function formatarDocumento($documento) {
    // Log do documento original
    error_log("Documento original: '" . bin2hex($documento) . "'");
    
    // Remove tudo que não for número
    $documento_limpo = preg_replace('/[^0-9]/', '', $documento);
    
    // Log do documento após limpeza
    error_log("Documento após limpeza: '" . $documento_limpo . "'");
    error_log("Tamanho do documento: " . strlen($documento_limpo));
    
    // Validar tamanho do documento
    if (strlen($documento_limpo) !== 11 && strlen($documento_limpo) !== 14) {
        error_log("Documento com tamanho inválido: " . strlen($documento_limpo) . " dígitos");
    }
    
    return $documento_limpo;
}

// Função para buscar usuário na API pelo loginAD
function buscarUsuarioAPI($loginAD) {
    global $api_config;
    
    try {
        $loginAD = trim($loginAD);
        if (empty($loginAD)) {
            throw new Exception("Login AD é obrigatório");
        }

        // Log para debug
        error_log("Buscando usuário na API com loginAD: " . $loginAD);

        $apiFields = [
            'api_user' => $api_config['api_user'],
            'api_token' => $api_config['api_token'],
            'api_module' => 'Usuarios',
            'api_action' => 'listarUsuarios',
            'loginAD' => $loginAD
        ];

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $api_config['base_url'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => http_build_query($apiFields),
            CURLOPT_TIMEOUT => $api_config['timeout'],
            CURLOPT_CONNECTTIMEOUT => 10
        ));
        
        $response = curl_exec($curl);
        
        if (curl_errno($curl)) {
            throw new Exception('Erro na API: ' . curl_error($curl));
        }
        
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        if ($httpCode !== 200) {
            throw new Exception('Erro na API: HTTP Code ' . $httpCode);
        }
        
        curl_close($curl);
        
        $data = json_decode($response, true);

        // Log da resposta da API
        error_log("Resposta da API: " . print_r($data, true));

        if (!is_array($data) || empty($data)) {
            throw new Exception("Usuário não encontrado na API para o login: $loginAD");
        }

        // Procurar o usuário específico pelo loginAD
        foreach ($data as $usuario) {
            if (isset($usuario['loginAD']) && strcasecmp($usuario['loginAD'], $loginAD) === 0) {
                error_log("Usuário encontrado na API: " . print_r($usuario, true));
                return [
                    'id' => $usuario['id'] ?? null,
                    'nome' => $usuario['nome'] ?? null
                ];
            }
        }

        throw new Exception("Usuário não encontrado na API para o login: $loginAD");
    } catch (Exception $e) {
        error_log("Erro ao buscar usuário na API: " . $e->getMessage());
        throw new Exception($e->getMessage());
            }
        }

// Função para buscar usuário no banco pelo email
function buscarUsuarioBanco($email) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT id FROM usuarios WHERE email = ?");
        $stmt->execute([$email]);
        $usuario = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$usuario) {
            throw new Exception("Usuário não encontrado para o email: $email");
        }

        return $usuario['id'];
    } catch (Exception $e) {
        throw new Exception($e->getMessage());
    }
}

// Função para buscar PA pelo número
function buscarPA($numero) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT nome FROM pontos_atendimento WHERE numero = ?");
        $stmt->execute([$numero]);
        $pa = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$pa) {
            throw new Exception("PA não encontrado para o número: $numero");
        }
        
        return $pa['nome'];
    } catch (Exception $e) {
        throw new Exception($e->getMessage());
    }
}

// Função para validar documento
function validarDocumento($documento) {
    // Remove tudo que não for número
    $documento_limpo = preg_replace('/[^0-9]/', '', $documento);
    $tamanho = strlen($documento_limpo);
    
    // Log para debug
    error_log("Validando documento: '$documento' (limpo: '$documento_limpo', tamanho: $tamanho)");
    
    // Se começar com 'V', provavelmente é uma linha de cabeçalho
    if (strtoupper(trim($documento)) === 'V') {
        error_log("Documento começa com 'V' - provavelmente é uma linha de cabeçalho");
        return ['valido' => false, 'motivo' => 'cabecalho'];
    }
    
    // Se estiver vazio ou contiver apenas caracteres não numéricos
    if (empty($documento_limpo)) {
        error_log("Documento vazio ou sem números");
        return ['valido' => false, 'motivo' => 'vazio'];
    }
    
    // Se tiver menos de 6 dígitos, rejeita
    if ($tamanho < 6) {
        error_log("Documento muito curto: $tamanho dígitos");
        return ['valido' => false, 'motivo' => 'curto', 'tamanho' => $tamanho, 'documento' => $documento_limpo];
    }
    
    // Se tiver entre 6 e 10 dígitos, tenta completar como CPF
    if ($tamanho >= 6 && $tamanho <= 10) {
        $documento_completo = str_pad($documento_limpo, 11, '0', STR_PAD_LEFT);
        if (validarCPF($documento_completo)) {
            error_log("CPF completado com zeros: $documento_limpo -> $documento_completo");
            return ['valido' => true, 'tipo' => 'cpf', 'documento' => $documento_completo, 'completado' => true];
        }
    }
    
    // Se tiver entre 10 e 13 dígitos, tenta completar como CNPJ
    if ($tamanho >= 10 && $tamanho <= 13) {
        $documento_completo = str_pad($documento_limpo, 14, '0', STR_PAD_LEFT);
        if (validarCNPJ($documento_completo)) {
            error_log("CNPJ completado com zeros: $documento_limpo -> $documento_completo");
            return ['valido' => true, 'tipo' => 'cnpj', 'documento' => $documento_completo, 'completado' => true];
        }
    }
    
    // Validação normal para documentos com tamanho correto
    if ($tamanho === 11) {
        if (!validarCPF($documento_limpo)) {
            error_log("CPF inválido: $documento_limpo");
            return ['valido' => false, 'motivo' => 'cpf_invalido', 'documento' => $documento_limpo];
        }
        return ['valido' => true, 'tipo' => 'cpf', 'documento' => $documento_limpo];
    }
    
    if ($tamanho === 14) {
        if (!validarCNPJ($documento_limpo)) {
            error_log("CNPJ inválido: $documento_limpo");
            return ['valido' => false, 'motivo' => 'cnpj_invalido', 'documento' => $documento_limpo];
        }
        return ['valido' => true, 'tipo' => 'cnpj', 'documento' => $documento_limpo];
    }
    
    // Se chegou aqui, tenta completar como CPF ou CNPJ independente do tamanho
    if ($tamanho < 11) {
        $documento_completo = str_pad($documento_limpo, 11, '0', STR_PAD_LEFT);
        error_log("Tentando completar como CPF: $documento_limpo -> $documento_completo");
        return ['valido' => true, 'tipo' => 'cpf', 'documento' => $documento_completo, 'completado' => true];
    } else {
        $documento_completo = str_pad($documento_limpo, 14, '0', STR_PAD_LEFT);
        error_log("Tentando completar como CNPJ: $documento_limpo -> $documento_completo");
        return ['valido' => true, 'tipo' => 'cnpj', 'documento' => $documento_completo, 'completado' => true];
    }
}

// Processar upload do arquivo
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['arquivo'])) {
    $arquivo = $_FILES['arquivo'];
    
    // Validar tipo de arquivo
    $tipos_permitidos = ['csv'];
    $extensao = strtolower(pathinfo($arquivo['name'], PATHINFO_EXTENSION));
    
    if (!in_array($extensao, $tipos_permitidos)) {
        $mensagem = "Tipo de arquivo não permitido. Use apenas CSV.";
        $tipo_mensagem = "danger";
    } elseif ($arquivo['error'] !== UPLOAD_ERR_OK) {
        $mensagem = "Erro no upload do arquivo. Código do erro: " . $arquivo['error'];
        $tipo_mensagem = "danger";
    } else {
        try {
            // Função para detectar o delimitador
            function detectDelimiter($file) {
                $delimiters = [',', ';', '\t', '|'];
                $firstLine = fgets($file);
                rewind($file);
                
                $counts = [];
                foreach ($delimiters as $delimiter) {
                    $counts[$delimiter] = substr_count($firstLine, $delimiter);
                }
                
                $maxCount = max($counts);
                return $maxCount > 0 ? array_search($maxCount, $counts) : ',';
            }

            // Abrir o arquivo CSV
            $handle = fopen($arquivo['tmp_name'], "r");
            if ($handle !== FALSE) {
                // Detectar o delimitador
                $delimiter = detectDelimiter($handle);
                
                // Ler o cabeçalho com detecção de codificação
                $cabecalho_raw = fgetcsv($handle, 0, $delimiter);
                if (!$cabecalho_raw) {
                    throw new Exception("Não foi possível ler o cabeçalho do arquivo CSV");
                }

                // Converter o cabeçalho para UTF-8 se necessário
                $cabecalho = array_map(function($coluna) {
                    // Tentar detectar a codificação
                    $encoding = mb_detect_encoding($coluna, ['UTF-8', 'ISO-8859-1', 'Windows-1252'], true);
                    if ($encoding && $encoding !== 'UTF-8') {
                        return mb_convert_encoding($coluna, 'UTF-8', $encoding);
                    }
                    return $coluna;
                }, $cabecalho_raw);

                // Log do cabeçalho para debug
                error_log("Cabeçalho original: " . print_r($cabecalho_raw, true));
                error_log("Cabeçalho convertido: " . print_r($cabecalho, true));

                // Normalizar nomes das colunas (remover acentos, espaços extras, converter para minúsculo)
                function normalizarColuna($nome) {
                    // Converter para UTF-8 se ainda não estiver
                    if (!mb_check_encoding($nome, 'UTF-8')) {
                        $nome = mb_convert_encoding($nome, 'UTF-8', 'auto');
                    }
                    
                    // Converter para minúsculo mantendo acentos
                    $nome = mb_strtolower(trim($nome), 'UTF-8');
                    
                    // Substituir caracteres acentuados
                    $mapa = [
                        'á' => 'a', 'à' => 'a', 'ã' => 'a', 'â' => 'a', 'ä' => 'a',
                        'é' => 'e', 'è' => 'e', 'ê' => 'e', 'ë' => 'e',
                        'í' => 'i', 'ì' => 'i', 'î' => 'i', 'ï' => 'i',
                        'ó' => 'o', 'ò' => 'o', 'õ' => 'o', 'ô' => 'o', 'ö' => 'o',
                        'ú' => 'u', 'ù' => 'u', 'û' => 'u', 'ü' => 'u',
                        'ç' => 'c',
                        'Á' => 'A', 'À' => 'A', 'Ã' => 'A', 'Â' => 'A', 'Ä' => 'A',
                        'É' => 'E', 'È' => 'E', 'Ê' => 'E', 'Ë' => 'E',
                        'Í' => 'I', 'Ì' => 'I', 'Î' => 'I', 'Ï' => 'I',
                        'Ó' => 'O', 'Ò' => 'O', 'Õ' => 'O', 'Ô' => 'O', 'Ö' => 'O',
                        'Ú' => 'U', 'Ù' => 'U', 'Û' => 'U', 'Ü' => 'U',
                        'Ç' => 'C'
                    ];
                    
                    $nome = strtr($nome, $mapa);
                    
                    // Remover caracteres especiais mantendo letras, números e espaços
                    $nome = preg_replace('/[^a-z0-9\s]/i', '', $nome);
                    
                    // Remover espaços extras
                    $nome = preg_replace('/\s+/', '', $nome);
                    
                    return $nome;
                }

                // Mapear índices das colunas com nomes normalizados
                $colunas = array_map('normalizarColuna', $cabecalho);
                error_log("Colunas normalizadas: " . print_r($colunas, true));

                // Mapear índices das colunas com nomes normalizados
                $indices = [
                    'documento' => array_search('cpfcnpj', $colunas),
                    'nome' => array_search('nomecompleto', $colunas),
                    'mesa' => array_search('mesa', $colunas),
                    'usuario' => array_search('usuario', $colunas),
                    'pa' => array_search('pa', $colunas),
                    'acao' => array_search('acao', $colunas),
                    'motivo' => array_search('motivo', $colunas),
                    'email' => array_search('email', $colunas),
                    'data' => array_search('data', $colunas)
                ];

                // Log dos índices encontrados
                error_log("Índices mapeados: " . print_r($indices, true));

                // Verificar se todas as colunas necessárias foram encontradas
                $colunas_faltantes = [];
                foreach ($indices as $nome => $indice) {
                    if ($indice === false) {
                        $colunas_faltantes[] = $nome;
                    }
                }

                if (!empty($colunas_faltantes)) {
                    $mensagem_erro = "Colunas obrigatórias não encontradas: " . implode(', ', $colunas_faltantes) . "\n";
                    $mensagem_erro .= "Cabeçalho original: " . implode(', ', $cabecalho_raw) . "\n";
                    $mensagem_erro .= "Cabeçalho convertido: " . implode(', ', $cabecalho) . "\n";
                    $mensagem_erro .= "Colunas normalizadas: " . implode(', ', $colunas);
                    throw new Exception($mensagem_erro);
                }

                $linhas_processadas = 0;
                $erros = [];
                $linha_atual = 0;
                $batch = [];
                $total_linhas = 0;
                
                // Contar total de linhas primeiro
                $temp_handle = fopen($arquivo['tmp_name'], "r");
                while (($data = fgetcsv($temp_handle, 0, $delimiter)) !== FALSE) {
                    $total_linhas++;
                }
                fclose($temp_handle);
                
                // Processar cada linha
                while (($data = fgetcsv($handle, 0, $delimiter)) !== FALSE) {
                    $linha_atual++;
                    
                    // Verificar se a linha está vazia
                    if (empty(array_filter($data, 'trim'))) {
                        continue;
                    }

                    try {
                        // Processamento especial para o campo documento
                        $documento_original = trim($data[$indices['documento']]);
                        error_log("Linha $linha_atual - Documento original: '" . $documento_original . "'");
                        
                        // Validar documento
                        $validacao = validarDocumento($documento_original);
                        
                        if (!$validacao['valido']) {
                            switch ($validacao['motivo']) {
                                case 'cabecalho':
                                    error_log("Linha $linha_atual - Ignorando linha de cabeçalho");
                                    continue 2; // Pula para a próxima linha
                                case 'vazio':
                                    $erros[] = "Linha $linha_atual: Documento vazio";
                                    break;
                                case 'curto':
                                    $erros[] = "Linha $linha_atual: Documento muito curto ({$validacao['tamanho']} dígitos) - Valor: $documento_original";
                                    break;
                                case 'incompleto':
                                    $erros[] = "Linha $linha_atual: Documento incompleto ({$validacao['tamanho']} dígitos) - Valor: $documento_original";
                                    break;
                                case 'longo':
                                    $erros[] = "Linha $linha_atual: Documento muito longo ({$validacao['tamanho']} dígitos) - Valor: $documento_original";
                                    break;
                                case 'cpf_invalido':
                                    $erros[] = "Linha $linha_atual: CPF inválido - Valor: $documento_original";
                                    break;
                                case 'cnpj_invalido':
                                    $erros[] = "Linha $linha_atual: CNPJ inválido - Valor: $documento_original";
                                    break;
                            }
                            continue;
                        }
                        
                        $documento = $validacao['documento'];
                        
                        // Mapear os outros campos
                        $nome = trim($data[$indices['nome']]);
                        $mesa_original = trim($data[$indices['mesa']]);
                        $mesa = normalizarMesa($mesa_original);
                        $usuario = trim($data[$indices['usuario']]);
                        
                        // Processamento especial para o campo PA
                        $pa_original = $data[$indices['pa']];
                        $pa_numero = trim($pa_original);
                        
                        // Log detalhado do campo PA
                        error_log("Linha $linha_atual - PA Original: '" . bin2hex($pa_original) . "'");
                        error_log("Linha $linha_atual - PA Após trim: '" . bin2hex($pa_numero) . "'");
                        error_log("Linha $linha_atual - PA Length: " . strlen($pa_numero));
                        
                        // Verificar se o PA é zero ou vazio
                        if ($pa_numero === '0' || $pa_numero === '') {
                            error_log("Linha $linha_atual - PA é zero ou vazio");
                            $pa_numero = '0'; // Normalizar para '0' se for vazio
                        }
                        
                        $acao = strtolower(trim($data[$indices['acao']]));
                        $motivo = !empty($data[$indices['motivo']]) ? trim($data[$indices['motivo']]) : null;
                        $email = trim($data[$indices['email']]);
                        $data_criacao = DateTime::createFromFormat('d/m/Y', trim($data[$indices['data']]));

                        // Validar campos obrigatórios
                        $campos_ausentes = [];
                        if (empty($documento)) {
                            $campos_ausentes[] = 'documento';
                            error_log("Linha $linha_atual - Documento vazio detectado. Valor original: '" . $documento_original . "'");
                            if (!empty($documento_original)) {
                                error_log("Linha $linha_atual - Documento original não vazio, mas sem números válidos");
                            }
                        }
                        if (empty($nome)) $campos_ausentes[] = 'nome';
                        if (empty($mesa)) $campos_ausentes[] = 'mesa';
                        if (empty($usuario)) $campos_ausentes[] = 'usuario';
                        if ($pa_numero === '') {
                            $campos_ausentes[] = 'pa';
                            error_log("Linha $linha_atual - PA vazio detectado. Valor original: '" . $pa_original . "'");
                        }
                        if (empty($acao)) $campos_ausentes[] = 'acao';
                        if (empty($email)) $campos_ausentes[] = 'email';
                        if (!$data_criacao) $campos_ausentes[] = 'data';

                        if (!empty($campos_ausentes)) {
                            $erros[] = "Linha $linha_atual: Campos obrigatórios ausentes: " . implode(', ', $campos_ausentes) . 
                                     (in_array('documento', $campos_ausentes) ? " (Documento original: '" . $documento_original . "')" : "") .
                                     (in_array('pa', $campos_ausentes) ? " (PA original: '" . $pa_original . "')" : "");
                            continue;
                        }

                        // Validar formato do documento
                        if (strlen($documento) !== 11 && strlen($documento) !== 14) {
                            $erros[] = "Linha $linha_atual: Documento inválido (deve ter 11 ou 14 dígitos): " . $documento_original;
                            continue;
                        }

                        // Validar mesa
                        $mesas_validas = ['limite', 'emprestimo', 'rural', 'cartao'];
                        if (!in_array($mesa, $mesas_validas)) {
                            $erros[] = "Linha $linha_atual: Mesa inválida: " . $data[$indices['mesa']] . " (normalizada: $mesa)";
                            continue;
                        }

                        // Validar ação
                        $acoes_validas = ['submeter', 'devolver'];
                        if (!in_array($acao, $acoes_validas)) {
                            $erros[] = "Linha $linha_atual: Ação inválida: $acao";
                            continue;
                        }

                        // Buscar usuário no banco
                        $usuario_id = buscarUsuarioBanco($email);
                        error_log("Linha $linha_atual - Usuário banco: ID=$usuario_id, Email=$email");

                        // Buscar usuário na API
                        $usuario_api = buscarUsuarioAPI($usuario);
                        error_log("Linha $linha_atual - Usuário API: " . print_r($usuario_api, true));
                        
                        if (!$usuario_api || !isset($usuario_api['id'])) {
                            $erros[] = "Linha $linha_atual: Usuário não encontrado na API: $usuario";
                            continue;
                        }

                        // Buscar PA
                        try {
                            $pa_nome = buscarPA($pa_numero);
                            error_log("Linha $linha_atual - PA encontrado: Número=$pa_numero, Nome=$pa_nome");
                        } catch (Exception $e) {
                            error_log("Linha $linha_atual - Erro ao buscar PA: " . $e->getMessage());
                            $erros[] = "Linha $linha_atual: " . $e->getMessage();
                            continue;
                        }

                        // Preparar data
                        $data_criacao_sql = $data_criacao->format('Y-m-d H:i:s');

                        // Adicionar ao lote
                        $registro = [
                            'usuario_id' => $usuario_id,
                            'documento' => $documento,
                            'nome' => $nome,
                            'mesa' => $mesa,
                            'usuario_pa' => $usuario_api['id'],
                            'pa' => $pa_nome,
                            'acao' => $acao,
                            'motivo' => $motivo,
                            'data_criacao' => $data_criacao_sql
                        ];

                        // Log do registro antes de adicionar ao lote
                        error_log("Linha $linha_atual - Registro preparado: " . print_r($registro, true));
                        $batch[] = $registro;

                        // Se atingiu o tamanho do lote, processa
                        if (count($batch) >= BATCH_SIZE) {
                            processarLote($batch, $pdo);
                            $linhas_processadas += count($batch);
                            $batch = [];
                            
                            // Mostrar progresso
                            $progresso = round(($linha_atual / $total_linhas) * 100, 2);
                            error_log("Progresso: $progresso% ($linha_atual/$total_linhas)");
                        }

                    } catch (Exception $e) {
                        $erros[] = "Linha $linha_atual: " . $e->getMessage();
                        continue;
                    }
                }

                // Processar o último lote se houver
                if (!empty($batch)) {
                    processarLote($batch, $pdo);
                    $linhas_processadas += count($batch);
                    }

                fclose($handle);

                if ($linhas_processadas > 0) {
                    $mensagem = "Processamento concluído! $linhas_processadas registros importados.";
                if (count($erros) > 0) {
                        $mensagem .= "<br>Alguns erros foram encontrados:<br>" . implode("<br>", $erros);
                    $tipo_mensagem = "warning";
                    } else {
                        $tipo_mensagem = "success";
                    }
                } else {
                    $mensagem = "Nenhum registro foi importado.";
                    if (count($erros) > 0) {
                        $mensagem .= "<br>Erros encontrados:<br>" . implode("<br>", $erros);
            }
                    $tipo_mensagem = "danger";
                }

            } else {
                throw new Exception("Não foi possível abrir o arquivo para leitura");
            }
        } catch (Exception $e) {
            $mensagem = "Erro ao processar arquivo: " . $e->getMessage();
            $tipo_mensagem = "danger";
        }
    }
}

// Função para processar um lote de registros
function processarLote($batch, $pdo) {
    try {
        $pdo->beginTransaction();
        
        $stmt = $pdo->prepare("
            INSERT INTO acd_formularios (
                usuario_id, documento, nome, mesa, usuario_pa, 
                pa, acao, motivo_devolucao, data_criacao
            ) VALUES (
                :usuario_id, :documento, :nome, :mesa, :usuario_pa, 
                :pa, :acao, :motivo, :data_criacao
            )
        ");

        foreach ($batch as $registro) {
            $stmt->execute($registro);
        }
        
        $pdo->commit();
    } catch (Exception $e) {
        $pdo->rollBack();
        throw new Exception("Erro ao processar lote: " . $e->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="../assets/images/Sicoob.ico">
    <title>Importar Propostas - Análise de Crédito</title>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <style>
        :root {
            /* Cores do Sicoob */
            --sicoob-turquesa: #00AE9D;
            --sicoob-verde-escuro: #003641;
            --sicoob-branco: #FFFFFF;
            --sicoob-verde-claro: #C9D200;
            --sicoob-verde-medio: #7DB61C;
            --sicoob-roxo: #49479D;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .wrapper {
            display: flex;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .content-container {
            background: var(--sicoob-branco);
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 8px rgba(0, 54, 65, 0.1);
            border-top: 4px solid var(--sicoob-turquesa);
        }

        .content-title {
            color: var(--sicoob-verde-escuro);
            margin-bottom: 2rem;
            font-weight: 600;
            position: relative;
            padding-bottom: 1rem;
        }

        .content-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--sicoob-turquesa);
        }

        .upload-area {
            border: 2px dashed var(--sicoob-turquesa);
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            background: rgba(0, 174, 157, 0.05);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            background: rgba(0, 174, 157, 0.1);
        }

        .upload-area i {
            font-size: 3rem;
            color: var(--sicoob-turquesa);
            margin-bottom: 1rem;
        }

        .btn-primary {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
        }

        .btn-primary:hover {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
        }

        .alert {
            border-radius: 4px;
            border: none;
        }

        .alert-success {
            background-color: rgba(125, 182, 28, 0.1);
            color: var(--sicoob-verde-medio);
        }

        .alert-danger {
            background-color: rgba(73, 71, 157, 0.1);
            color: var(--sicoob-roxo);
        }

        .alert-warning {
            background-color: rgba(201, 210, 0, 0.1);
            color: var(--sicoob-verde-claro);
        }

        .file-info {
            margin-top: 1rem;
            font-size: 0.9rem;
            color: var(--sicoob-verde-escuro);
            text-align: left;
            padding: 1rem;
            background: rgba(0, 54, 65, 0.05);
            border-radius: 4px;
        }

        .file-info strong {
            color: var(--sicoob-verde-escuro);
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'components/sidebar.php'; ?>

        <div class="main-content">
            <div class="content-container">
                <h2 class="content-title">Importar Propostas</h2>

                <?php if ($mensagem): ?>
                    <div class="alert alert-<?php echo $tipo_mensagem; ?> alert-dismissible fade show" role="alert">
                        <i class="fas <?php 
                            echo $tipo_mensagem === 'success' ? 'fa-check-circle' : 
                                ($tipo_mensagem === 'warning' ? 'fa-exclamation-triangle' : 'fa-exclamation-circle'); 
                        ?> me-2"></i>
                        <?php echo $mensagem; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    <div class="upload-area" id="dropZone">
                        <i class="fas fa-file-upload"></i>
                        <h4>Arraste seu arquivo CSV aqui ou clique para selecionar</h4>
                        <p class="text-muted">Formato aceito: CSV</p>
                        <input type="file" name="arquivo" id="arquivo" class="d-none" accept=".csv">
                        <div class="file-info" id="fileInfo"></div>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>Importar Arquivo
                        </button>
                    </div>
                </form>

                <div class="mt-4">
                    <h5 class="mb-3">Formato do Arquivo CSV</h5>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Campo</th>
                                    <th>Descrição</th>
                                    <th>Exemplo</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>CPF/CNPJ</td>
                                    <td>Documento do cliente</td>
                                    <td>028.725.266-33</td>
                                </tr>
                                <tr>
                                    <td>NOME COMPLETO</td>
                                    <td>Nome do cliente</td>
                                    <td>JANE LEMOS MOREIRA</td>
                                </tr>
                                <tr>
                                    <td>MESA</td>
                                    <td>Tipo de operação</td>
                                    <td>Empréstimo</td>
                                </tr>
                                <tr>
                                    <td>USUÁRIO</td>
                                    <td>Login AD do usuário</td>
                                    <td>TATIANAF3049_03</td>
                                </tr>
                                <tr>
                                    <td>PA</td>
                                    <td>Número do PA</td>
                                    <td>3</td>
                                </tr>
                                <tr>
                                    <td>AÇÃO</td>
                                    <td>Ação a ser realizada</td>
                                    <td>Submeter</td>
                                </tr>
                                <tr>
                                    <td>MOTIVO</td>
                                    <td>Motivo da devolução (opcional)</td>
                                    <td>-</td>
                                </tr>
                                <tr>
                                    <td>EMAIL</td>
                                    <td>Email do usuário</td>
                                    <td><EMAIL></td>
                                </tr>
                                <tr>
                                    <td>DATA</td>
                                    <td>Data da proposta</td>
                                    <td>02/01/2025</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#dropZone').on('click', function(e) {
                if (e.target === this) {
                    $('#arquivo').trigger('click');
                }
            });

            $('#arquivo').on('change', function() {
                if (this.files.length) {
                    updateFileInfo(this.files[0]);
                }
            });

            $('#dropZone')
                .on('dragover', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    $(this).addClass('bg-light');
                })
                .on('dragleave', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    $(this).removeClass('bg-light');
                })
                .on('drop', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    $(this).removeClass('bg-light');
                    
                    const files = e.originalEvent.dataTransfer.files;
                    if (files.length) {
                        $('#arquivo').prop('files', files);
                        updateFileInfo(files[0]);
                    }
                });

            function updateFileInfo(file) {
                const fileInfo = $('#fileInfo');
                const size = (file.size / 1024).toFixed(2);
                fileInfo.html(`
                    <strong>Arquivo selecionado:</strong><br>
                    Nome: ${file.name}<br>
                    Tamanho: ${size} KB
                `);
            }
        });
    </script>
</body>
</html> 