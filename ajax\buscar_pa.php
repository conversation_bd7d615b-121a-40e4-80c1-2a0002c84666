<?php
require_once '../config/database.php';
require_once '../auth_check.php';

header('Content-Type: application/json');

if (!isset($_GET['pa'])) {
    echo json_encode(['success' => false, 'message' => 'PA não fornecido']);
    exit;
}

$pa = $_GET['pa'];

try {
    $stmt = $pdo->prepare("SELECT nome FROM pontos_atendimento WHERE numero = ?");
    $stmt->execute([$pa]);
    $pa_data = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($pa_data) {
        echo json_encode([
            'success' => true,
            'nome' => $pa_data['nome']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'PA não encontrado'
        ]);
    }
} catch (PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao buscar PA: ' . $e->getMessage()
    ]);
} 