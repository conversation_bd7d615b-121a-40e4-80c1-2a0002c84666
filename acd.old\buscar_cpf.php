<?php
session_start();
require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

header('Content-Type: application/json');

// Verificar se o usuário está logado e tem acesso ACD
if (!isset($_SESSION['user_id']) || !checkACDPermission($_SESSION['user_id'])) {
    echo json_encode(['error' => 'Acesso negado']);
    exit;
}

if (!isset($_POST['cpf'])) {
    echo json_encode([]);
    exit;
}

$cpf = limparCPF($_POST['cpf']);

try {
    $sql = "SELECT DISTINCT cpf, nome 
            FROM parecer_base 
            WHERE cpf LIKE ? 
            LIMIT 5";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$cpf . '%']);
    $resultados = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode($resultados);
} catch (PDOException $e) {
    echo json_encode([]);
} 