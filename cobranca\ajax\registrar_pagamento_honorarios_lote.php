<?php
// Definir cabeçalho para garantir que a resposta seja JSON
header('Content-Type: application/json');

// Iniciar captura de saída para evitar que HTML acidental seja enviado
ob_start();

try {
    // Usar caminhos absolutos para os includes
    require_once __DIR__ . '/../../auth_check.php';
    
    // Verificar se o database.php está no diretório /d/ ou no diretório /d/cobranca/
    if (file_exists(__DIR__ . '/../../config/database.php')) {
        require_once __DIR__ . '/../../config/database.php';
    } else {
        require_once __DIR__ . '/../config/database.php';
    }
    
    require_once __DIR__ . '/../includes/class/Honorarios.php';
    
    // Verificar se a requisição é POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido.');
    }
    
    // Validar parâmetros obrigatórios
    if (!isset($_POST['honorario_ids']) || !is_array($_POST['honorario_ids']) || empty($_POST['honorario_ids'])) {
        throw new Exception('Nenhum honorário selecionado.');
    }
    
    if (!isset($_POST['data_pagamento']) || empty($_POST['data_pagamento'])) {
        throw new Exception('Data de pagamento não fornecida.');
    }
    
    // Obter parâmetros
    $honorario_ids = array_map('intval', $_POST['honorario_ids']);
    $data_pagamento = $_POST['data_pagamento'];
    $observacoes = isset($_POST['observacoes']) ? trim($_POST['observacoes']) : '';
    
    error_log("=== INÍCIO DO REGISTRO DE PAGAMENTO DE HONORÁRIOS EM LOTE ===");
    error_log("Número de honorários: " . count($honorario_ids));
    error_log("IDs: " . implode(', ', $honorario_ids));
    error_log("Data pagamento: " . $data_pagamento);
    error_log("Observações: " . $observacoes);
    
    // Iniciar transação
    $pdo->beginTransaction();
    
    // Agrupar honorários por advogado
    $honorariosPorAdvogado = [];
    $advogadoNomes = [];
    $totalGeral = 0;
    
    // Buscar informações dos honorários
    $placeholders = implode(',', array_fill(0, count($honorario_ids), '?'));
    $stmt = $pdo->prepare("
        SELECT 
            h.*,
            a.nome AS nome_associado,
            adv.nome AS nome_advogado,
            adv.id AS advogado_id,
            p.numero_processo
        FROM cbp_honorarios h
        LEFT JOIN cbp_associados a ON h.associado_id = a.id
        LEFT JOIN cbp_advogados adv ON h.advogado_id = adv.id
        LEFT JOIN cbp_processos_judiciais p ON h.processo_id = p.id
        WHERE h.id IN ($placeholders)
        AND h.status = 'PENDENTE'
    ");
    
    $stmt->execute($honorario_ids);
    $honorarios = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($honorarios)) {
        throw new Exception('Nenhum honorário pendente foi encontrado.');
    }
    
    // Agrupar por advogado
    foreach ($honorarios as $honorario) {
        $advogado_id = $honorario['advogado_id'] ?? 0;
        $advogado_nome = !empty($honorario['nome_advogado']) ? $honorario['nome_advogado'] : 'Não atribuído';
        
        if (!isset($honorariosPorAdvogado[$advogado_id])) {
            $honorariosPorAdvogado[$advogado_id] = [];
            $advogadoNomes[$advogado_id] = $advogado_nome;
        }
        
        $honorariosPorAdvogado[$advogado_id][] = $honorario;
        
        // Atualizar status do honorário
        $update = $pdo->prepare("
            UPDATE cbp_honorarios
            SET status = 'PAGO',
                data_pagamento = ?,
                observacoes = ?,
                updated_at = NOW()
            WHERE id = ?
        ");
        
        $update->execute([$data_pagamento, $observacoes, $honorario['id']]);
        $totalGeral += floatval($honorario['valor_honorario']);
    }
    
    error_log("Total de honorários processados: " . count($honorarios));
    error_log("Total por advogado: " . count($honorariosPorAdvogado));
    
    // Gerar PDFs por advogado
    $pdfUrls = [];
    $pdfGeradoComSucesso = false;
    
    // Coletar todos os IDs de honorários em um único array
    $todos_honorario_ids = [];
    foreach ($honorariosPorAdvogado as $advogado_id => $honorariosAdvogado) {
        $honorario_ids_advogado = array_column($honorariosAdvogado, 'id');
        $todos_honorario_ids = array_merge($todos_honorario_ids, $honorario_ids_advogado);
    }
    
    // Gerar uma única URL para o relatório com todos os honorários
    $todos_honorario_ids_str = implode(',', $todos_honorario_ids);
    $relatorioUrl = "/d/cobranca/gerar_relatorio_html.php?honorario_ids={$todos_honorario_ids_str}&data_pagamento={$data_pagamento}";
    
    error_log("URL do relatório consolidado gerada: " . $relatorioUrl);
    
    $pdfUrls[] = $relatorioUrl;
    $pdfGeradoComSucesso = true;
    
    // Registrar a operação no log do sistema
    $quantidade_honorarios = count($honorarios);
    $valor_total = number_format($totalGeral, 2, ',', '.');
    
    // Preparar detalhes do log
    $detalhes_log = "Pagamento em lote de {$quantidade_honorarios} honorários, Valor total: R$ {$valor_total}, Data do pagamento: " . date('d/m/Y', strtotime($data_pagamento));
    
    // Adicionar resumo por advogado
    foreach ($honorariosPorAdvogado as $advogado_id => $honorariosAdvogado) {
        $nome_advogado = $advogadoNomes[$advogado_id];
        $total_advogado = 0;
        foreach ($honorariosAdvogado as $honorario) {
            $total_advogado += floatval($honorario['valor_honorario']);
        }
        $total_advogado_fmt = number_format($total_advogado, 2, ',', '.');
        $qtd_honorarios = count($honorariosAdvogado);
        
        $detalhes_log .= ", Advogado {$nome_advogado}: {$qtd_honorarios} honorários (R$ {$total_advogado_fmt})";
    }
    
    // Inserir no log
    $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$_SESSION['user_id'], 'Pagamento de Honorários em Lote', $detalhes_log]);
    
    error_log("Registro de log criado para o pagamento de honorários em lote");

    // Commit
    $pdo->commit();
    
    // Mensagem de sucesso apropriada
    $mensagem = 'Pagamentos registrados com sucesso.';
    if (count($pdfUrls) == 0 && count($honorariosPorAdvogado) > 0) {
        $mensagem .= ' Não foi possível gerar os relatórios. Verifique os logs do sistema.';
    }
    
    error_log("=== FIM DO REGISTRO DE PAGAMENTO DE HONORÁRIOS EM LOTE - SUCESSO ===");
    
    // Limpar buffer de saída
    ob_end_clean();
    
    // Retornar sucesso com URLs dos relatórios
    echo json_encode([
        'success' => true,
        'message' => $mensagem,
        'total_processado' => count($honorarios),
        'valor_total' => $totalGeral,
        'pdfs' => $pdfUrls
    ]);
    
} catch (PDOException $e) {
    // Rollback em caso de erro com o banco de dados
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    
    error_log("=== ERRO NO REGISTRO DE PAGAMENTO DE HONORÁRIOS EM LOTE (PDO) ===");
    error_log("Mensagem: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    error_log("=== FIM DO ERRO ===");
    
    // Limpar buffer de saída
    ob_end_clean();
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro de banco de dados: ' . $e->getMessage()
    ]);
    
} catch (Exception $e) {
    // Rollback em caso de qualquer outro erro
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    
    error_log("=== ERRO NO REGISTRO DE PAGAMENTO DE HONORÁRIOS EM LOTE ===");
    error_log("Mensagem: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    error_log("=== FIM DO ERRO ===");
    
    // Limpar buffer de saída
    ob_end_clean();
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Função para sanitizar nome de arquivo
 */
function sanitizeFilename($filename) {
    // Remover acentos
    $filename = preg_replace('/[áàãâä]/ui', 'a', $filename);
    $filename = preg_replace('/[éèêë]/ui', 'e', $filename);
    $filename = preg_replace('/[íìîï]/ui', 'i', $filename);
    $filename = preg_replace('/[óòõôö]/ui', 'o', $filename);
    $filename = preg_replace('/[úùûü]/ui', 'u', $filename);
    $filename = preg_replace('/[ç]/ui', 'c', $filename);
    
    // Substituir espaços e caracteres especiais
    $filename = preg_replace('/[^a-z0-9_\-]/i', '_', $filename);
    
    // Remover underscores duplicados
    $filename = preg_replace('/_+/', '_', $filename);
    
    return $filename;
}

/**
 * Função para gerar o PDF do relatório
 */
function gerarPdf($pdfPath, $honorarios, $advogado_nome, $totalAdvogado, $data_pagamento) {
    try {
        error_log("Iniciando geração do PDF para " . $advogado_nome);
        error_log("Caminho do PDF: " . $pdfPath);
        
        // Verificar se o diretório de destino existe e tem permissões
        $pdfDir = dirname($pdfPath);
        if (!is_dir($pdfDir)) {
            error_log("Diretório do PDF não existe: " . $pdfDir);
            return false;
        }
        
        if (!is_writable($pdfDir)) {
            error_log("Diretório do PDF não tem permissão de escrita: " . $pdfDir);
            return false;
        }
        
        // Verificar FPDF
        $fpdfPath = __DIR__ . '/../lib/fpdf/fpdf.php';
        error_log("Procurando FPDF em: " . $fpdfPath);
        
        if (!file_exists($fpdfPath)) {
            error_log("Biblioteca FPDF não encontrada em: " . $fpdfPath);
            return false;
        }
        
        require_once $fpdfPath;
        error_log("FPDF carregada com sucesso");
        
        // Criar classe derivada da FPDF para permitir salvar em uma string
        class PDF extends FPDF {
            protected $buffer = '';
            
            function Output($dest = '', $name = '', $isUTF8 = false) {
                // Salvar no buffer interno se dest for vazio
                if (empty($dest)) {
                    return parent::Output('S', $name, $isUTF8);
                }
                return parent::Output($dest, $name, $isUTF8);
            }
        }
        
        $pdf = new PDF();
        $pdf->AddPage();
        
        // Título
        $pdf->SetFont('Arial', 'B', 16);
        $pdf->Cell(0, 10, 'Relatorio de Pagamentos de Honorarios', 0, 1, 'C');
        $pdf->SetFont('Arial', 'B', 12);
        $pdf->Cell(0, 10, 'Advogado: ' . $advogado_nome, 0, 1, 'C');
        $pdf->Cell(0, 10, 'Data do Pagamento: ' . date('d/m/Y', strtotime($data_pagamento)), 0, 1, 'C');
        $pdf->Ln(10);
        
        // Cabeçalho da tabela
        $pdf->SetFont('Arial', 'B', 10);
        $pdf->Cell(15, 7, 'ID', 1, 0, 'C');
        $pdf->Cell(60, 7, 'Associado', 1, 0, 'C');
        $pdf->Cell(50, 7, 'Processo', 1, 0, 'C');
        $pdf->Cell(30, 7, 'Tipo', 1, 0, 'C');
        $pdf->Cell(35, 7, 'Valor Honorario', 1, 1, 'C');
        
        // Dados da tabela
        $pdf->SetFont('Arial', '', 10);
        foreach ($honorarios as $honorario) {
            $pdf->Cell(15, 7, $honorario['id'], 1, 0, 'C');
            $pdf->Cell(60, 7, $honorario['nome_associado'] ?? 'Não identificado', 1, 0, 'L');
            $pdf->Cell(50, 7, $honorario['numero_processo'] ?? 'Não informado', 1, 0, 'L');
            
            // Determinar tipo
            $tipo = $honorario['tipo'];
            if ($tipo === 'PARCELA' && !empty($honorario['numero_parcela']) && !empty($honorario['total_parcelas'])) {
                $tipo = "Parcela {$honorario['numero_parcela']}/{$honorario['total_parcelas']}";
            } elseif ($tipo === 'ENTRADA') {
                $tipo = 'Entrada';
            } elseif ($tipo === 'ALVARA') {
                $tipo = 'Alvará';
            }
            
            $pdf->Cell(30, 7, $tipo, 1, 0, 'C');
            $pdf->Cell(35, 7, 'R$ ' . number_format($honorario['valor_honorario'], 2, ',', '.'), 1, 1, 'R');
        }
        
        // Total
        $pdf->SetFont('Arial', 'B', 10);
        $pdf->Cell(155, 7, 'Total', 1, 0, 'R');
        $pdf->Cell(35, 7, 'R$ ' . number_format($totalAdvogado, 2, ',', '.'), 1, 1, 'R');
        
        // Informações adicionais
        $pdf->Ln(10);
        $pdf->SetFont('Arial', '', 10);
        $pdf->Cell(0, 7, 'Data de emissao: ' . date('d/m/Y H:i:s'), 0, 1, 'L');
        $pdf->Cell(0, 7, 'Pagina 1/' . $pdf->PageNo(), 0, 1, 'L');
        
        // Usar diretamente o Output com destino F (File)
        error_log("Salvando PDF diretamente no arquivo...");
        
        // Obter o caminho absoluto completo
        $realPath = realpath(dirname($pdfPath)) . DIRECTORY_SEPARATOR . basename($pdfPath);
        if (!$realPath) {
            $realPath = $pdfPath;
        }
        error_log("Caminho absoluto: " . $realPath);
        
        // Tentar três diferentes métodos para salvar o PDF
        // Método 1: Output direto
        try {
            error_log("Método 1: Output direto para arquivo");
            $pdf->Output('F', $realPath);
            
            if (file_exists($realPath) && filesize($realPath) > 0) {
                error_log("PDF gerado com sucesso usando Output direto. Tamanho: " . filesize($realPath));
                return true;
            }
        } catch (Exception $e) {
            error_log("Erro no método 1: " . $e->getMessage());
        }
        
        // Método 2: Output para string e depois para arquivo usando file_put_contents
        try {
            error_log("Método 2: Output para string e depois file_put_contents");
            $pdfContent = $pdf->Output('S');
            $result = file_put_contents($realPath, $pdfContent);
            
            if ($result !== false && file_exists($realPath) && filesize($realPath) > 0) {
                error_log("PDF gerado com sucesso usando file_put_contents. Bytes escritos: " . $result);
                return true;
            }
        } catch (Exception $e) {
            error_log("Erro no método 2: " . $e->getMessage());
        }
        
        // Método 3: Output para browser e captura de buffer
        try {
            error_log("Método 3: Output para browser com captura de buffer");
            ob_start();
            $pdf->Output();
            $pdfContent = ob_get_contents();
            ob_end_clean();
            
            $result = file_put_contents($realPath, $pdfContent);
            
            if ($result !== false && file_exists($realPath) && filesize($realPath) > 0) {
                error_log("PDF gerado com sucesso usando captura de buffer. Bytes escritos: " . $result);
                return true;
            }
        } catch (Exception $e) {
            error_log("Erro no método 3: " . $e->getMessage());
        }
        
        // Se chegou aqui, todos os métodos falharam
        error_log("Todos os métodos falharam ao gerar o PDF");
        return false;
    }
    catch (Exception $e) {
        error_log("Erro ao gerar PDF: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
        return false;
    }
} 