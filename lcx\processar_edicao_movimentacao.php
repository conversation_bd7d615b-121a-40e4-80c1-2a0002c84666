<?php
session_start();
require_once '../config/database.php';
require_once 'funcoes_dias_uteis.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user_id = $_SESSION['user_id'];

// Verificar se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: index.php?erro=metodo_invalido');
    exit;
}

// Verificar permissões LCX do usuário
try {
    $stmt = $pdo->prepare("SELECT nivel_permissao FROM lcx_permissoes WHERE usuario_id = ? AND ativo = 1");
    $stmt->execute([$user_id]);
    $permissao_lcx = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$permissao_lcx) {
        header('Location: index.php?erro=sem_permissao_lcx');
        exit;
    }
    
    $nivel_permissao = $permissao_lcx['nivel_permissao'];
    
    // Verificar se tem permissão para fazer movimentações
    if (!in_array($nivel_permissao, ['tesoureiro', 'gestor', 'gestor_master', 'admin'])) {
        header('Location: index.php?erro=sem_permissao_movimentacao');
        exit;
    }
} catch (Exception $e) {
    error_log("Erro ao verificar permissões: " . $e->getMessage());
    header('Location: index.php?erro=erro_sistema');
    exit;
}

// Validar dados obrigatórios
$movimentacao_id = intval($_POST['movimentacao_id'] ?? 0);
$livro_id = intval($_POST['livro_id'] ?? 0);
$tipo = $_POST['tipo'] ?? '';
$valor = floatval($_POST['valor'] ?? 0);
$descricao = trim($_POST['descricao'] ?? '');
$categoria = trim($_POST['categoria'] ?? '');
$origem = $_POST['origem'] ?? 'caixa';
$equipamento_atm_id = !empty($_POST['equipamento_atm_id']) ? intval($_POST['equipamento_atm_id']) : null;
$data_competencia = $_POST['data_competencia'] ?? '';

if (!$movimentacao_id || !$livro_id || !$tipo || !$valor || !$categoria || !$origem || !$data_competencia) {
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=dados_obrigatorios');
    exit;
}

if (!in_array($tipo, ['entrada', 'saida'])) {
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=tipo_invalido');
    exit;
}

if (!in_array($origem, ['caixa', 'atm', 'transportadora', 'saldo_tesouraria'])) {
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=origem_invalida');
    exit;
}

if ($valor <= 0) {
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=valor_invalido');
    exit;
}

// Validar equipamento ATM quando origem for ATM
if ($origem === 'atm' && !$equipamento_atm_id) {
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=equipamento_atm_obrigatorio');
    exit;
}

// Validar que saldo_tesouraria só seja usado em livros master
if ($origem === 'saldo_tesouraria') {
    // Buscar tipo do livro para validação
    $stmt = $pdo->prepare("SELECT tipo FROM lcx_livros_caixa WHERE id = ?");
    $stmt->execute([$livro_id]);
    $livro_temp = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$livro_temp || $livro_temp['tipo'] !== 'master') {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=saldo_tesouraria_apenas_master');
        exit;
    }
}

// Validar data de competência
if (!DateTime::createFromFormat('Y-m-d', $data_competencia)) {
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=data_competencia_invalida');
    exit;
}

// Buscar dados da movimentação original e do livro
try {
    $sql = "
        SELECT m.*, lc.permite_edicao_data, lc.ponto_atendimento_id, lc.saldo_atual,
               lc.tipo as livro_tipo
        FROM lcx_movimentacoes m
        JOIN lcx_livros_caixa lc ON m.livro_caixa_id = lc.id
        WHERE m.id = ? AND lc.id = ?
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$movimentacao_id, $livro_id]);
    $movimentacao_original = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$movimentacao_original) {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=movimentacao_nao_encontrada');
        exit;
    }
    
    // Verificar se o usuário é tesoureiro do PA deste livro (SEM EXCEÇÕES - nem admin)
    $stmt = $pdo->prepare("
        SELECT id FROM lcx_tesoureiros_pa
        WHERE usuario_id = ? AND ponto_atendimento_id = ? AND ativo = 1
    ");
    $stmt->execute([$user_id, $movimentacao_original['ponto_atendimento_id']]);

    if ($stmt->rowCount() === 0) {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=sem_permissao_pa');
        exit;
    }
    
} catch (Exception $e) {
    error_log("Erro ao buscar movimentação: " . $e->getMessage());
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=erro_sistema');
    exit;
}

// Verificar se o usuário tem acesso a este livro
$tem_acesso = false;
if (in_array($nivel_permissao, ['admin', 'gestor_master'])) {
    $tem_acesso = true;
} else {
    // Verificar se é tesoureiro do PA deste livro
    try {
        $stmt = $pdo->prepare("
            SELECT id FROM lcx_tesoureiros_pa 
            WHERE usuario_id = ? AND ponto_atendimento_id = ? AND ativo = 1
        ");
        $stmt->execute([$user_id, $movimentacao_original['ponto_atendimento_id']]);
        $tem_acesso = $stmt->rowCount() > 0;
    } catch (Exception $e) {
        error_log("Erro ao verificar acesso ao livro: " . $e->getMessage());
    }
}

if (!$tem_acesso) {
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=sem_acesso_livro');
    exit;
}

try {
    // Iniciar transação
    $pdo->beginTransaction();

    // Salvar histórico da edição ANTES de alterar
    $stmt_historico = $pdo->prepare("
        INSERT INTO lcx_historico_edicoes (
            movimentacao_id, livro_caixa_id,
            tipo_original, valor_original, descricao_original, categoria_original,
            origem_original, equipamento_atm_id_original, data_competencia_original,
            saldo_anterior_original, saldo_posterior_original,
            tipo_novo, valor_novo, descricao_nova, categoria_nova,
            origem_nova, equipamento_atm_id_novo, data_competencia_nova,
            saldo_anterior_novo, saldo_posterior_novo,
            editado_por
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    // Calcular novo saldo posterior
    $novo_saldo_posterior = floatval($movimentacao_original['saldo_anterior']);
    if ($tipo === 'entrada') {
        $novo_saldo_posterior += $valor;
    } else {
        $novo_saldo_posterior -= $valor;
    }

    $stmt_historico->execute([
        $movimentacao_id,
        $livro_id,
        // Dados originais
        $movimentacao_original['tipo'],
        $movimentacao_original['valor'],
        $movimentacao_original['descricao'],
        $movimentacao_original['categoria'],
        $movimentacao_original['origem'],
        $movimentacao_original['equipamento_atm_id'],
        $movimentacao_original['data_competencia'],
        $movimentacao_original['saldo_anterior'],
        $movimentacao_original['saldo_posterior'],
        // Dados novos
        $tipo,
        $valor,
        $descricao,
        $categoria ?: null,
        $origem,
        $equipamento_atm_id,
        $data_competencia,
        $movimentacao_original['saldo_anterior'], // Saldo anterior não muda
        $novo_saldo_posterior,
        $user_id
    ]);

    // Atualizar a movimentação
    $stmt = $pdo->prepare("
        UPDATE lcx_movimentacoes
        SET tipo = ?, valor = ?, descricao = ?, categoria = ?, origem = ?,
            data_competencia = ?, equipamento_atm_id = ?,
            editado = TRUE, editado_por = ?, editado_em = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ");

    $stmt->execute([
        $tipo,
        $valor,
        $descricao,
        $categoria ?: null,
        $origem,
        $data_competencia,
        $equipamento_atm_id,
        $user_id,
        $movimentacao_id
    ]);
    
    // Recalcular saldos se o valor ou tipo mudou
    $valor_original = floatval($movimentacao_original['valor']);
    $tipo_original = $movimentacao_original['tipo'];

    if ($valor != $valor_original || $tipo != $tipo_original) {
        // RECÁLCULO COMPLETO: Buscar saldo inicial do livro
        $stmt_livro = $pdo->prepare("SELECT saldo_inicial FROM lcx_livros_caixa WHERE id = ?");
        $stmt_livro->execute([$livro_id]);
        $saldo_inicial = floatval($stmt_livro->fetch(PDO::FETCH_ASSOC)['saldo_inicial']);

        // Buscar todas as movimentações em ordem cronológica
        $stmt_movs = $pdo->prepare("
            SELECT id, tipo, valor
            FROM lcx_movimentacoes
            WHERE livro_caixa_id = ?
            ORDER BY data_competencia ASC, data_movimentacao ASC, id ASC
        ");
        $stmt_movs->execute([$livro_id]);
        $todas_movimentacoes = $stmt_movs->fetchAll(PDO::FETCH_ASSOC);

        // Recalcular todos os saldos sequencialmente
        $saldo_atual = $saldo_inicial;

        foreach ($todas_movimentacoes as $mov) {
            $saldo_anterior = $saldo_atual;
            $valor_mov = floatval($mov['valor']);

            // Se for a movimentação que estamos editando, usar os novos valores
            if ($mov['id'] == $movimentacao_id) {
                $valor_mov = $valor;
                $tipo_mov = $tipo;
            } else {
                $tipo_mov = $mov['tipo'];
            }

            // Calcular novo saldo
            if ($tipo_mov === 'entrada') {
                $saldo_atual += $valor_mov;
            } else {
                $saldo_atual -= $valor_mov;
            }

            // Atualizar saldos da movimentação
            $stmt_update = $pdo->prepare("
                UPDATE lcx_movimentacoes
                SET saldo_anterior = ?, saldo_posterior = ?
                WHERE id = ?
            ");
            $stmt_update->execute([$saldo_anterior, $saldo_atual, $mov['id']]);
        }

        // Atualizar saldo final do livro
        $stmt = $pdo->prepare("UPDATE lcx_livros_caixa SET saldo_atual = ? WHERE id = ?");
        $stmt->execute([$saldo_atual, $livro_id]);
    }
    
    // Confirmar transação
    $pdo->commit();

    // Preparar dados para log
    $dados_anteriores = [
        'tipo' => $movimentacao_original['tipo'],
        'valor' => $movimentacao_original['valor'],
        'descricao' => $movimentacao_original['descricao'],
        'categoria' => $movimentacao_original['categoria'],
        'origem' => $movimentacao_original['origem'],
        'data_competencia' => $movimentacao_original['data_competencia'],
        'equipamento_atm_id' => $movimentacao_original['equipamento_atm_id']
    ];

    $dados_novos = [
        'tipo' => $tipo,
        'valor' => $valor,
        'descricao' => $descricao,
        'categoria' => $categoria,
        'origem' => $origem,
        'data_competencia' => $data_competencia,
        'equipamento_atm_id' => $equipamento_atm_id
    ];

    // Verificar se é edição retroativa
    $hoje = date('Y-m-d');
    $data_mov = $data_competencia;
    $eh_retroativa = ($data_mov < $hoje);

    // Determinar tipo de edição retroativa
    $tipo_edicao = '';
    if ($eh_retroativa) {
        if ($movimentacao_original['permite_edicao_data']) {
            if (!empty($movimentacao_original['data_edicao_especifica'])) {
                $tipo_edicao = 'específica';
            } else {
                $tipo_edicao = 'geral';
            }
        }
    }

    // Registrar log apropriado
    if ($eh_retroativa && $tipo_edicao) {
        log_edicao_retroativa($pdo, $livro_id, $movimentacao_id, $data_competencia, $dados_anteriores, $dados_novos, $tipo_edicao);
    } else {
        log_editar_movimentacao($pdo, $livro_id, $movimentacao_id, $dados_anteriores, $dados_novos);
    }

    // Redirecionar com sucesso
    header('Location: visualizar_livro.php?id=' . $livro_id . '&sucesso=movimentacao_editada');
    exit;
    
} catch (Exception $e) {
    // Reverter transação em caso de erro
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    // Log detalhado do erro
    $erro_detalhado = "Erro ao editar movimentação: " . $e->getMessage() .
                     " | Movimentação ID: {$movimentacao_id}" .
                     " | Livro ID: {$livro_id}" .
                     " | Usuário: {$user_id}" .
                     " | Arquivo: " . $e->getFile() .
                     " | Linha: " . $e->getLine() .
                     " | POST: " . print_r($_POST, true);
    error_log($erro_detalhado);

    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=erro_edicao&debug=' . urlencode($e->getMessage()));
    exit;
}
?>
