<?php
require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';
require_once 'vinculos_functions.php';
session_start();

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se o usuário tem permissão para acessar a pasta ACD
if (!checkACDPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Verificar se o usuário é administrador ou gestor
if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Buscar filtros
$filtro_usuario = $_GET['usuario_id'] ?? '';
$filtro_pa = $_GET['pa_id'] ?? '';
$filtro_data_inicio = $_GET['data_inicio'] ?? '';
$filtro_data_fim = $_GET['data_fim'] ?? '';
$filtro_status = $_GET['status'] ?? '';

// Buscar usuários para o filtro
$stmt = $pdo->query("SELECT id, nome_completo FROM usuarios ORDER BY nome_completo");
$usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Buscar pontos de atendimento para o filtro
$stmt = $pdo->query("SELECT id, numero, nome FROM pontos_atendimento ORDER BY numero");
$pontos_atendimento = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Construir consulta com filtros
$where_conditions = [];
$params = [];

if ($filtro_usuario) {
    $where_conditions[] = "v.usuario_id = ?";
    $params[] = $filtro_usuario;
}

if ($filtro_pa) {
    $where_conditions[] = "v.pa_id = ?";
    $params[] = $filtro_pa;
}

if ($filtro_data_inicio) {
    $where_conditions[] = "v.data_inicio >= ?";
    $params[] = $filtro_data_inicio;
}

if ($filtro_data_fim) {
    $where_conditions[] = "(v.data_fim IS NULL OR v.data_fim <= ?)";
    $params[] = $filtro_data_fim;
}

if ($filtro_status) {
    $where_conditions[] = "v.status = ?";
    $params[] = $filtro_status;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Buscar histórico completo
$sql = "
    SELECT 
        v.*,
        u.nome_completo as usuario_nome,
        u.email as usuario_email,
        p.nome as pa_nome,
        p.numero as pa_numero,
        uc.nome_completo as criado_por_nome,
        ud.nome_completo as desativado_por_nome
    FROM acd_usuario_pa v
    INNER JOIN usuarios u ON v.usuario_id = u.id
    INNER JOIN pontos_atendimento p ON v.pa_id = p.id
    LEFT JOIN usuarios uc ON v.criado_por = uc.id
    LEFT JOIN usuarios ud ON v.desativado_por = ud.id
    $where_clause
    ORDER BY v.data_inicio DESC, v.criado_em DESC
    LIMIT 500
";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$historico = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="../assets/images/Sicoob.ico">
    <title>Histórico de Vínculos - Análise de Crédito</title>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <style>
        :root {
            --sicoob-turquesa: #00AE9D;
            --sicoob-verde-escuro: #003641;
            --sicoob-branco: #FFFFFF;
            --sicoob-verde-claro: #C9D200;
            --sicoob-verde-medio: #7DB61C;
            --sicoob-roxo: #49479D;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .wrapper {
            display: flex;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 2rem;
            transition: all 0.3s ease;
        }

        .content-container {
            background: var(--sicoob-branco);
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 8px rgba(0, 54, 65, 0.1);
            border-top: 4px solid var(--sicoob-turquesa);
        }

        .content-title {
            color: var(--sicoob-verde-escuro);
            margin-bottom: 2rem;
            font-weight: 600;
            position: relative;
            padding-bottom: 1rem;
        }

        .content-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--sicoob-turquesa);
        }

        .btn-primary {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
        }

        .btn-primary:hover {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
        }

        .table thead th {
            background-color: var(--sicoob-verde-escuro);
            color: var(--sicoob-branco);
            border: none;
        }

        .table tbody tr:hover {
            background-color: rgba(0, 174, 157, 0.05);
        }

        .badge-ativo {
            background-color: var(--sicoob-verde-medio);
        }

        .badge-inativo {
            background-color: #6c757d;
        }

        .filter-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'components/sidebar.php'; ?>

        <div class="main-content">
            <div class="content-container">
                <h2 class="content-title">
                    <i class="fas fa-history me-2"></i>Histórico de Vínculos
                </h2>

                <!-- Filtros -->
                <div class="filter-card">
                    <h5 class="mb-3">
                        <i class="fas fa-filter me-2"></i>Filtros
                    </h5>
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="usuario_id" class="form-label">Usuário</label>
                            <select class="form-select" name="usuario_id">
                                <option value="">Todos os usuários</option>
                                <?php foreach ($usuarios as $usuario): ?>
                                    <option value="<?php echo $usuario['id']; ?>" 
                                            <?php echo $filtro_usuario == $usuario['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($usuario['nome_completo']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="pa_id" class="form-label">Ponto de Atendimento</label>
                            <select class="form-select" name="pa_id">
                                <option value="">Todos os PAs</option>
                                <?php foreach ($pontos_atendimento as $pa): ?>
                                    <option value="<?php echo $pa['id']; ?>" 
                                            <?php echo $filtro_pa == $pa['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($pa['numero'] . ' - ' . $pa['nome']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="data_inicio" class="form-label">Data Início</label>
                            <input type="date" class="form-control" name="data_inicio" 
                                   value="<?php echo htmlspecialchars($filtro_data_inicio); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="data_fim" class="form-label">Data Fim</label>
                            <input type="date" class="form-control" name="data_fim" 
                                   value="<?php echo htmlspecialchars($filtro_data_fim); ?>">
                        </div>
                        <div class="col-md-2">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" name="status">
                                <option value="">Todos</option>
                                <option value="ativo" <?php echo $filtro_status === 'ativo' ? 'selected' : ''; ?>>Ativo</option>
                                <option value="inativo" <?php echo $filtro_status === 'inativo' ? 'selected' : ''; ?>>Inativo</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="fas fa-search me-2"></i>Filtrar
                            </button>
                            <a href="historico_vinculos.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Limpar
                            </a>
                        </div>
                    </form>
                </div>

                <!-- Resultados -->
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title mb-3">
                            <i class="fas fa-list me-2"></i>Histórico Completo
                            <small class="text-muted">(<?php echo count($historico); ?> registros)</small>
                        </h5>
                        
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Usuário</th>
                                        <th>PA</th>
                                        <th>Período</th>
                                        <th>Status</th>
                                        <th>Criado Por</th>
                                        <th>Observações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($historico)): ?>
                                        <tr>
                                            <td colspan="6" class="text-center text-muted py-4">
                                                <i class="fas fa-info-circle me-2"></i>
                                                Nenhum registro encontrado com os filtros aplicados
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($historico as $item): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($item['usuario_nome']); ?></strong><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($item['usuario_email']); ?></small>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($item['pa_numero']); ?></strong><br>
                                                    <small><?php echo htmlspecialchars($item['pa_nome']); ?></small>
                                                </td>
                                                <td>
                                                    <strong>Início:</strong> <?php echo date('d/m/Y', strtotime($item['data_inicio'])); ?><br>
                                                    <?php if ($item['data_fim']): ?>
                                                        <strong>Fim:</strong> <?php echo date('d/m/Y', strtotime($item['data_fim'])); ?>
                                                    <?php else: ?>
                                                        <small class="text-success">Em andamento</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge <?php echo $item['status'] === 'ativo' ? 'badge-ativo' : 'badge-inativo'; ?>">
                                                        <?php echo ucfirst($item['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($item['criado_por_nome'] ?? 'N/A'); ?><br>
                                                    <small class="text-muted">
                                                        <?php echo date('d/m/Y H:i', strtotime($item['criado_em'])); ?>
                                                    </small>
                                                    <?php if ($item['desativado_por_nome']): ?>
                                                        <br><small class="text-danger">
                                                            Desativado por: <?php echo htmlspecialchars($item['desativado_por_nome']); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($item['observacoes']): ?>
                                                        <small><?php echo nl2br(htmlspecialchars($item['observacoes'])); ?></small>
                                                    <?php else: ?>
                                                        <small class="text-muted">Sem observações</small>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <a href="gerenciar_vinculos.php" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left me-2"></i>Voltar para Gerenciar Vínculos
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
