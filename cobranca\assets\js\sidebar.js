document.addEventListener('DOMContentLoaded', function() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const content = document.querySelector('.content');

    // Toggle sidebar no mobile
    sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('active');
        content.classList.toggle('sidebar-active');
    });

    // Fechar sidebar ao clicar fora em dispositivos móveis
    document.addEventListener('click', function(event) {
        const isMobile = window.innerWidth <= 768;
        const clickedOutsideSidebar = !sidebar.contains(event.target);
        const clickedOutsideToggle = !sidebarToggle.contains(event.target);

        if (isMobile && clickedOutsideSidebar && clickedOutsideToggle && sidebar.classList.contains('active')) {
            sidebar.classList.remove('active');
            content.classList.remove('sidebar-active');
        }
    });

    // Ajustar sidebar ao redimensionar a janela
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            sidebar.classList.remove('active');
            content.classList.remove('sidebar-active');
        }
    });
}); 