<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

// Adicionar log para depuração
$log_file = fopen(__DIR__ . '/debug_log.txt', 'a');
fwrite($log_file, "\n\n" . date('Y-m-d H:i:s') . " - Nova requisição\n");
fwrite($log_file, "POST data: " . print_r($_POST, true) . "\n");

// Verifica os campos obrigatórios
if (empty($_POST['id']) || empty($_POST['data_envio']) || empty($_POST['advogado_id'])) {
    echo json_encode(['success' => false, 'message' => 'Campos obrigatórios não preenchidos']);
    exit;
}

try {
    // Inicia a transação
    $pdo->beginTransaction();

    // Obtém os valores do formulário
    $id = $_POST['id'];
    $retomado = isset($_POST['retomado']) && $_POST['retomado'] == '1';

    // Verificar se o processo tem acordos
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_acordos WHERE processo_id = ?");
    $stmt->execute([$id]);
    $tem_acordos = $stmt->fetchColumn() > 0;

    // Obter status atual do processo
    $stmt = $pdo->prepare("SELECT status_id FROM cbp_processos_judiciais WHERE id = ?");
    $stmt->execute([$id]);
    $status_atual = $stmt->fetchColumn();

    // Definir o novo status
    if ($retomado) {
        $status_id = 4; // RETOMADO
    } else if ($tem_acordos) {
        $status_id = 2; // ACORDO JUDICIAL
    } else {
        $status_id = $status_atual; // Mantém o status atual se não tiver acordos
    }

    // Log para debug
    fwrite($log_file, "ID do processo: " . $id . "\n");
    fwrite($log_file, "Status retomado: " . ($retomado ? 'Sim' : 'Não') . "\n");
    fwrite($log_file, "Tem acordos: " . ($tem_acordos ? 'Sim' : 'Não') . "\n");
    fwrite($log_file, "Status atual: " . $status_atual . "\n");
    fwrite($log_file, "Novo status ID: " . $status_id . "\n");

    // Verifica se o processo existe
    $stmt = $pdo->prepare("SELECT id FROM cbp_processos_judiciais WHERE id = ?");
    $stmt->execute([$id]);
    if (!$stmt->fetch()) {
        throw new Exception('Processo não encontrado');
    }

    // Formatar data de envio (sem função externa)
    $data_envio = !empty($_POST['data_envio']) ? date('Y-m-d', strtotime(str_replace('/', '-', $_POST['data_envio']))) : null;

    // Atualiza o processo
    $stmt = $pdo->prepare("
        UPDATE cbp_processos_judiciais 
        SET status_id = :status_id,
            data_envio = :data_envio,
            advogado_id = :advogado_id,
            updated_at = NOW()
        WHERE id = :id
    ");

    $params = [
        'status_id' => $status_id,
        'data_envio' => $data_envio,
        'advogado_id' => $_POST['advogado_id'],
        'id' => $id
    ];

    // Log dos parâmetros
    fwrite($log_file, "Parâmetros da atualização: " . print_r($params, true) . "\n");

    $stmt->execute($params);

    // Se o processo foi marcado como RETOMADO, inativa todos os acordos ativos
    if ($retomado) {
        fwrite($log_file, "Inativando acordos do processo " . $id . "\n");
        
        // Primeiro, obter o ID do status INATIVO da tabela cbp_status_acordo
        $stmt = $pdo->prepare("SELECT id FROM cbp_status_acordo WHERE nome = 'INATIVO'");
        $stmt->execute();
        $status_inativo = $stmt->fetchColumn();
        
        if (!$status_inativo) {
            // Use 4 como padrão se não encontrar (de acordo com o insert na tabela)
            $status_inativo = 4;
            fwrite($log_file, "Status INATIVO não encontrado na tabela, usando ID 4 como padrão\n");
        } else {
            fwrite($log_file, "Status INATIVO encontrado com ID: " . $status_inativo . "\n");
        }
        
        $stmt = $pdo->prepare("
            UPDATE cbp_acordos 
            SET ativo = 0,
                status_id = :status_id,
                updated_at = NOW() 
            WHERE processo_id = :processo_id 
            AND ativo = 1
        ");
        $stmt->execute([
            'status_id' => $status_inativo,
            'processo_id' => $id
        ]);
        
        fwrite($log_file, "Acordos inativados com sucesso\n");
    } else {
        // Se o processo deixou de ser RETOMADO, reativar o último acordo
        fwrite($log_file, "Processo deixou de ser RETOMADO, buscando último acordo para reativar\n");
        
        // Obter o ID do status VIGENTE
        $stmt = $pdo->prepare("SELECT id FROM cbp_status_acordo WHERE nome = 'VIGENTE'");
        $stmt->execute();
        $status_vigente = $stmt->fetchColumn();
        
        if (!$status_vigente) {
            // Use 1 como padrão se não encontrar
            $status_vigente = 1;
            fwrite($log_file, "Status VIGENTE não encontrado na tabela, usando ID 1 como padrão\n");
        } else {
            fwrite($log_file, "Status VIGENTE encontrado com ID: " . $status_vigente . "\n");
        }
        
        // Buscar o último acordo do processo
        $stmt = $pdo->prepare("
            SELECT id 
            FROM cbp_acordos 
            WHERE processo_id = :processo_id 
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute(['processo_id' => $id]);
        $ultimo_acordo_id = $stmt->fetchColumn();
        
        if ($ultimo_acordo_id) {
            fwrite($log_file, "Último acordo encontrado (ID: " . $ultimo_acordo_id . "), reativando...\n");
            
            // Reativar o último acordo
            $stmt = $pdo->prepare("
                UPDATE cbp_acordos 
                SET ativo = 1,
                    status_id = :status_id,
                    updated_at = NOW() 
                WHERE id = :acordo_id
            ");
            $stmt->execute([
                'status_id' => $status_vigente,
                'acordo_id' => $ultimo_acordo_id
            ]);
            
            fwrite($log_file, "Acordo reativado com sucesso\n");
        } else {
            fwrite($log_file, "Nenhum acordo encontrado para reativar\n");
        }
    }

    // Registra a mudança de status no histórico
    $stmt = $pdo->prepare("
        INSERT INTO cbp_historico_status 
        (processo_id, status_id, data_alteracao, observacoes) 
        VALUES (:processo_id, :status_id, NOW(), :observacoes)
    ");

    $params_historico = [
        'processo_id' => $id,
        'status_id' => $status_id,
        'observacoes' => $retomado ? 'Processo marcado como RETOMADO' : 'Processo atualizado para ACORDO JUDICIAL'
    ];

    // Log dos parâmetros do histórico
    fwrite($log_file, "Parâmetros do histórico: " . print_r($params_historico, true) . "\n");

    $stmt->execute($params_historico);

    // Registrar no log
    $detalhes = "Atualização de processo - ID: " . $id . 
                " - Status: " . ($retomado ? 'RETOMADO' : ($tem_acordos ? 'ACORDO JUDICIAL' : $status_atual)) . 
                " - Data de Envio: " . ($data_envio ? date('d/m/Y', strtotime($data_envio)) : 'Não informado') . 
                " - Advogado: " . $_POST['advogado_id'] . 
                " - Observações: " . ($retomado ? 'Processo marcado como RETOMADO' : ($tem_acordos ? 'Processo atualizado para ACORDO JUDICIAL' : '')) . 
                " - Data de Alteração: " . date('d/m/Y H:i:s');
    $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$_SESSION['user_id'], 'Atualização de Processo', $detalhes]);

    // Commit da transação
    $pdo->commit();
    fwrite($log_file, "Transação commitada com sucesso\n");

    echo json_encode(['success' => true, 'message' => 'Processo atualizado com sucesso']);

} catch (Exception $e) {
    // Rollback em caso de erro
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
        fwrite($log_file, "Transação revertida devido a erro\n");
    }
    
    // Log do erro
    fwrite($log_file, "Erro: " . $e->getMessage() . "\n");
    fwrite($log_file, "Stack trace: " . $e->getTraceAsString() . "\n");
    
    echo json_encode([
        'success' => false, 
        'message' => 'Erro ao atualizar o processo: ' . $e->getMessage(),
        'error_details' => $e->getTraceAsString()
    ]);
}

// Fecha o arquivo de log
fclose($log_file); 