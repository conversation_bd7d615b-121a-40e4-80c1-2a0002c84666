<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

// Buscar acordos
$sql = "
    SELECT 
        a.*,
        p.associado_nome as nome_cliente,
        p.associado_documento as cpf_cnpj,
        p.numero_processo,
        a.numero_repactuacao,
        sa.nome as status_nome,
        sa.cor as status_cor,
        ass.nome as assuntor_nome,
        COUNT(pa.id) as total_parcelas,
        SUM(CASE WHEN pa.status = 'PAGO' THEN 1 ELSE 0 END) as parcelas_pagas,
        COALESCE(SUM(CASE WHEN pa.status = 'PAGO' THEN pa.valor_pago ELSE 0 END), 0) as total_pago,
        CASE
            WHEN a.ativo = 0 THEN 'INATIVO'
            WHEN NOT EXISTS (
                SELECT 1
                FROM cbp_parcelas_acordo pa2
                WHERE pa2.acordo_id = a.id
                AND pa2.status != 'PAGO'
            ) AND NOT EXISTS (
                SELECT 1
                FROM cbp_alvaras_acordo aa
                WHERE aa.acordo_id = a.id
                AND aa.status = 'PENDENTE'
            ) THEN 'QUITADO'
            WHEN EXISTS (
                SELECT 1
                FROM cbp_parcelas_acordo pa2
                WHERE pa2.acordo_id = a.id
                AND pa2.status = 'CANCELADO'
            ) THEN 'CANCELADO'
            WHEN EXISTS (
                SELECT 1
                FROM cbp_parcelas_acordo pa2
                WHERE pa2.acordo_id = a.id
                AND pa2.status = 'PENDENTE'
                AND pa2.data_vencimento < CURRENT_DATE
            ) THEN 'INADIMPLENTE'
            ELSE 'VIGENTE'
        END as status_acordo
    FROM cbp_acordos a
    INNER JOIN cbp_processos_judiciais p ON a.processo_id = p.id
    LEFT JOIN cbp_status_acordo sa ON a.status_id = sa.id
    LEFT JOIN cbp_parcelas_acordo pa ON a.id = pa.acordo_id
    LEFT JOIN cbp_associados ass ON a.assuntor_id = ass.id
    WHERE 1=1
    GROUP BY a.id, p.associado_nome, p.associado_documento, p.numero_processo, sa.nome, sa.cor, ass.nome
    ORDER BY a.created_at DESC
";

$stmt = $pdo->prepare($sql);
$stmt->execute();
$acordos = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Array de cores para status
$status_colors = [
    'VIGENTE' => '#00AE9D',
    'QUITADO' => '#003641',
    'CANCELADO' => '#dc3545',
    'INADIMPLENTE' => '#49479D',
    'INATIVO' => '#6c757d'
];

// Gerar HTML da tabela
ob_start();
?>
<table class="table table-hover">
    <thead>
        <tr>
            <th>Processo/Cliente</th>
            <th>Data Acordo</th>
            <th>Valor Total</th>
            <th>Parcelas</th>
            <th>Valor Pago</th>
            <th>Status</th>
            <th>Ações</th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($acordos as $acordo): ?>
            <tr>
                <td>
                    <?= htmlspecialchars($acordo['numero_processo']) ?><br>
                    <small class="text-muted"><?= htmlspecialchars($acordo['nome_cliente']) ?></small>
                    <?php if (!empty($acordo['assuntor_nome'])): ?>
                    <br>
                    <small class="text-muted" style="font-size: 0.5rem;">
                        <i class="fas fa-exchange-alt"></i> 
                        <?= htmlspecialchars($acordo['assuntor_nome']) ?>
                    </small>
                    <?php endif; ?>
                </td>
                <td class="text-center">
                    <?= date('d/m/Y', strtotime($acordo['data_acordo'])) ?>
                </td>
                <td class="text-center">
                    R$ <?= number_format($acordo['valor_total'], 2, ',', '.') ?>
                </td>
                <td class="text-center">
                    <?= $acordo['parcelas_pagas'] ?>/<?= $acordo['total_parcelas'] ?>
                </td>
                <td class="text-center">
                    R$ <?= number_format($acordo['total_pago'], 2, ',', '.') ?>
                </td>
                <td>
                    <span class="status-badge" style="background-color: <?= $status_colors[$acordo['status_nome']] ?>">
                        <?= $acordo['status_nome'] ?>
                    </span>
                </td>
                <td>
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-turquesa" onclick="visualizarAcordo(<?= $acordo['id'] ?>)" title="Visualizar">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-verde-claro" onclick="editarAcordo(<?= $acordo['id'] ?>)" title="Editar">
                            <i class="fas fa-edit"></i>
                        </button>
                        <?php if ($acordo['status_nome'] != 'QUITADO' && $acordo['status_nome'] != 'CANCELADO' && $acordo['ativo'] == 1): ?>
                            <button type="button" class="btn btn-sm btn-verde-medio" onclick="registrarPagamento(<?= $acordo['id'] ?>)" title="Registrar Pagamento">
                                <i class="fas fa-dollar-sign"></i>
                            </button>
                        <?php elseif ($acordo['status_nome'] != 'QUITADO' && $acordo['status_nome'] != 'CANCELADO' && $acordo['ativo'] == 0): ?>
                            <button type="button" class="btn btn-sm btn-verde-medio" disabled title="Acordo inativo - não é possível registrar pagamentos">
                                <i class="fas fa-dollar-sign"></i>
                            </button>
                        <?php endif; ?>
                    </div>
                </td>
            </tr>
        <?php endforeach; ?>
    </tbody>
</table>
<?php
$html = ob_get_clean();
echo $html;
?> 