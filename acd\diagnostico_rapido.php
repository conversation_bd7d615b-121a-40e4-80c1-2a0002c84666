<?php
/**
 * Diagnóstico rápido do problema de constraint na tabela acd_usuario_pa
 */

require_once '../config/database.php';

echo "<h2>🔍 Diagnóstico Rápido - acd_usuario_pa</h2>";

try {
    // 1. Verificar se a tabela existe
    echo "<h3>1. Verificação da Tabela</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'acd_usuario_pa'");
    if ($stmt->rowCount() == 0) {
        echo "<p>❌ <strong>PROBLEMA:</strong> Tabela acd_usuario_pa não existe!</p>";
        echo "<p>💡 <strong>SOLUÇÃO:</strong> Execute o script de migração para criar a tabela.</p>";
        echo "<p><a href='migrar_tabela_usuario_pa.php'>🔄 Criar Tabela</a></p>";
        exit;
    }
    echo "<p>✅ Tabela acd_usuario_pa existe.</p>";
    
    // 2. Verificar estrutura
    echo "<h3>2. Estrutura da Tabela</h3>";
    $stmt = $pdo->query("DESCRIBE acd_usuario_pa");
    $colunas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $campos = array_column($colunas, 'Field');
    $tem_usuario_vinculo = in_array('usuario_vinculo', $campos);
    $tem_criado_por = in_array('criado_por', $campos);
    
    echo "<p><strong>Campos encontrados:</strong> " . implode(', ', $campos) . "</p>";
    
    if ($tem_usuario_vinculo && !$tem_criado_por) {
        echo "<p>⚠️ <strong>ESTRUTURA ANTIGA</strong> detectada (campo 'usuario_vinculo').</p>";
    } elseif ($tem_criado_por && !$tem_usuario_vinculo) {
        echo "<p>✅ <strong>ESTRUTURA NOVA</strong> detectada (campo 'criado_por').</p>";
    } else {
        echo "<p>❓ <strong>ESTRUTURA MISTA</strong> ou não reconhecida.</p>";
    }
    
    // 3. Verificar constraints problemáticas
    echo "<h3>3. Constraints Foreign Key</h3>";
    $stmt = $pdo->query("
        SELECT 
            CONSTRAINT_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'acd_usuario_pa' 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    $constraints = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $constraint_problematica = false;
    foreach ($constraints as $constraint) {
        echo "<p>🔗 {$constraint['CONSTRAINT_NAME']}: {$constraint['COLUMN_NAME']} → {$constraint['REFERENCED_TABLE_NAME']}.{$constraint['REFERENCED_COLUMN_NAME']}</p>";
        if ($constraint['CONSTRAINT_NAME'] == 'acd_usuario_pa_ibfk_3' && $constraint['COLUMN_NAME'] == 'usuario_vinculo') {
            $constraint_problematica = true;
        }
    }
    
    if ($constraint_problematica) {
        echo "<p>❌ <strong>CONSTRAINT PROBLEMÁTICA ENCONTRADA:</strong> acd_usuario_pa_ibfk_3</p>";
    }
    
    // 4. Verificar dados órfãos (se estrutura antiga)
    if ($tem_usuario_vinculo) {
        echo "<h3>4. Verificação de Dados Órfãos</h3>";
        
        $stmt = $pdo->query("
            SELECT COUNT(*) as total
            FROM acd_usuario_pa 
            WHERE usuario_vinculo IS NOT NULL 
            AND usuario_vinculo NOT IN (SELECT id FROM usuarios)
        ");
        $orfaos = $stmt->fetchColumn();
        
        if ($orfaos > 0) {
            echo "<p>❌ <strong>DADOS ÓRFÃOS ENCONTRADOS:</strong> $orfaos registros com usuario_vinculo inválido.</p>";
            
            // Mostrar exemplos
            $stmt = $pdo->query("
                SELECT id, usuario_id, pa_id, usuario_vinculo
                FROM acd_usuario_pa 
                WHERE usuario_vinculo IS NOT NULL 
                AND usuario_vinculo NOT IN (SELECT id FROM usuarios)
                LIMIT 5
            ");
            $exemplos = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<p><strong>Exemplos:</strong></p>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Usuario ID</th><th>PA ID</th><th>Usuario Vinculo (Inválido)</th></tr>";
            foreach ($exemplos as $exemplo) {
                echo "<tr>";
                echo "<td>{$exemplo['id']}</td>";
                echo "<td>{$exemplo['usuario_id']}</td>";
                echo "<td>{$exemplo['pa_id']}</td>";
                echo "<td style='color: red;'>{$exemplo['usuario_vinculo']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>✅ Nenhum dado órfão encontrado.</p>";
        }
    }
    
    // 5. Verificar usuários disponíveis
    echo "<h3>5. Usuários Disponíveis</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) FROM usuarios");
    $total_usuarios = $stmt->fetchColumn();
    echo "<p>Total de usuários: $total_usuarios</p>";
    
    if ($total_usuarios == 0) {
        echo "<p>❌ <strong>PROBLEMA:</strong> Nenhum usuário encontrado na tabela usuarios!</p>";
    }
    
    // 6. Verificar PAs disponíveis
    echo "<h3>6. Pontos de Atendimento Disponíveis</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) FROM pontos_atendimento");
    $total_pas = $stmt->fetchColumn();
    echo "<p>Total de PAs: $total_pas</p>";
    
    if ($total_pas == 0) {
        echo "<p>❌ <strong>PROBLEMA:</strong> Nenhum PA encontrado na tabela pontos_atendimento!</p>";
    }
    
    // 7. Resumo e recomendações
    echo "<h3>7. 📋 Resumo e Recomendações</h3>";
    
    if ($tem_usuario_vinculo && !$tem_criado_por) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>⚠️ Estrutura Antiga Detectada</h4>";
        echo "<p><strong>Problema:</strong> Sua tabela usa a estrutura antiga que pode causar erros de constraint.</p>";
        echo "<p><strong>Soluções disponíveis:</strong></p>";
        echo "<ol>";
        echo "<li><strong>Correção Rápida:</strong> <a href='corrigir_constraint_usuario_pa.php'>Corrigir apenas o erro de constraint</a></li>";
        echo "<li><strong>Migração Completa (Recomendado):</strong> <a href='migrar_tabela_usuario_pa.php'>Migrar para estrutura com histórico</a></li>";
        echo "</ol>";
        echo "</div>";
        
    } elseif ($tem_criado_por && !$tem_usuario_vinculo) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>✅ Estrutura Atualizada</h4>";
        echo "<p>Sua tabela está usando a estrutura nova com suporte a histórico.</p>";
        echo "<p>Se ainda está tendo problemas, pode ser um problema de dados específicos.</p>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>❓ Estrutura Não Reconhecida</h4>";
        echo "<p>A estrutura da tabela não corresponde nem ao padrão antigo nem ao novo.</p>";
        echo "<p>Recomenda-se executar a migração completa.</p>";
        echo "</div>";
    }
    
    // Verificar se há problemas críticos
    $problemas_criticos = [];
    if ($total_usuarios == 0) $problemas_criticos[] = "Nenhum usuário na tabela usuarios";
    if ($total_pas == 0) $problemas_criticos[] = "Nenhum PA na tabela pontos_atendimento";
    if ($tem_usuario_vinculo && $orfaos > 0) $problemas_criticos[] = "Dados órfãos na tabela acd_usuario_pa";
    
    if (!empty($problemas_criticos)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>🚨 Problemas Críticos Detectados</h4>";
        echo "<ul>";
        foreach ($problemas_criticos as $problema) {
            echo "<li>$problema</li>";
        }
        echo "</ul>";
        echo "<p><strong>Estes problemas devem ser resolvidos antes de usar o sistema de vínculos.</strong></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Erro durante o diagnóstico:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "<br><a href='gerenciar_vinculos.php'>← Voltar para Gerenciar Vínculos</a>";
?>
