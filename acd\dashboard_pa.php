<?php
session_start();
require_once '../config/database.php';
require_once 'check_acd_permission.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se o usuário tem permissão para acessar a pasta ACD
if (!checkACDPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Obter o PA da URL
$pa_nome = $_GET['pa'] ?? '';

if (empty($pa_nome)) {
    header('Location: rankings.php');
    exit;
}

// Processar filtros de período
$filtro_periodo = $_GET['periodo'] ?? 'mes';
$filtro_data_inicio = $_GET['data_inicio'] ?? '';
$filtro_data_fim = $_GET['data_fim'] ?? '';

// Função para gerar condições de data baseadas no filtro
function gerarCondicaoData($filtro_periodo, $data_inicio = '', $data_fim = '') {
    switch ($filtro_periodo) {
        case 'mes':
            return "AND YEAR(data_criacao) = YEAR(CURDATE()) AND MONTH(data_criacao) = MONTH(CURDATE())";
        case 'ano':
            return "AND YEAR(data_criacao) = YEAR(CURDATE())";
        case 'personalizado':
            if (!empty($data_inicio) && !empty($data_fim)) {
                return "AND DATE(data_criacao) BETWEEN '$data_inicio' AND '$data_fim'";
            }
            return "AND YEAR(data_criacao) = YEAR(CURDATE()) AND MONTH(data_criacao) = MONTH(CURDATE())";
        default:
            return "AND YEAR(data_criacao) = YEAR(CURDATE()) AND MONTH(data_criacao) = MONTH(CURDATE())";
    }
}

// Gerar descrição do período para exibição
function gerarDescricaoPeriodo($filtro_periodo, $data_inicio = '', $data_fim = '') {
    switch ($filtro_periodo) {
        case 'mes':
            return 'Este Mês (' . date('m/Y') . ')';
        case 'ano':
            return 'Este Ano (' . date('Y') . ')';
        case 'personalizado':
            if (!empty($data_inicio) && !empty($data_fim)) {
                return 'De ' . date('d/m/Y', strtotime($data_inicio)) . ' até ' . date('d/m/Y', strtotime($data_fim));
            }
            return 'Este Mês (' . date('m/Y') . ')';
        default:
            return 'Este Mês (' . date('m/Y') . ')';
    }
}

$condicao_data = gerarCondicaoData($filtro_periodo, $filtro_data_inicio, $filtro_data_fim);
$descricao_periodo = gerarDescricaoPeriodo($filtro_periodo, $filtro_data_inicio, $filtro_data_fim);

try {
    // Verificar se o PA existe
    $stmt = $pdo->prepare("SELECT id, nome, numero FROM pontos_atendimento WHERE nome = ?");
    $stmt->execute([$pa_nome]);
    $pa_info = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$pa_info) {
        header('Location: rankings.php');
        exit;
    }

    // Buscar responsável atual do PA
    $stmt = $pdo->prepare("
        SELECT 
            v.usuario_id,
            v.data_inicio,
            v.data_fim,
            u.nome_completo as responsavel_nome
        FROM acd_usuario_pa v
        INNER JOIN usuarios u ON v.usuario_id = u.id
        WHERE v.pa_id = ?
        AND (v.data_fim IS NULL OR v.data_fim > CURDATE())
        AND v.status IN ('ativo', 1)
        ORDER BY v.data_inicio DESC
        LIMIT 1
    ");
    $stmt->execute([$pa_info['id']]);
    $responsavel = $stmt->fetch(PDO::FETCH_ASSOC);

    // Métricas do período selecionado
    $sql_metricas = "
        SELECT
            COUNT(*) as total_propostas,
            SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) as aprovadas,
            SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
            CASE
                WHEN COUNT(*) > 0 THEN ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1)
                ELSE 0
            END as taxa_devolucao
        FROM acd_formularios
        WHERE pa = ?
        $condicao_data
    ";
    $stmt = $pdo->prepare($sql_metricas);
    $stmt->execute([$pa_nome]);
    $metricas_periodo = $stmt->fetch(PDO::FETCH_ASSOC);

    // Função para buscar usuários da API
    function buscarUsuariosAPI() {
        $apiFields = [
            'api_user' => 'UFL7GXZ14LU9NOR',
            'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
            'api_module' => 'Usuarios',
            'api_action' => 'listarUsuarios'
        ];

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => http_build_query($apiFields),
        ));

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            curl_close($curl);
            return [];
        }

        curl_close($curl);

        $usuarios = json_decode($response, true);

        if (!is_array($usuarios)) {
            return [];
        }

        $usuarios_mapeados = [];
        foreach ($usuarios as $user) {
            $usuarios_mapeados[$user['id']] = [
                'id' => $user['id'],
                'nome' => $user['nome'] ?? '',
                'pa' => $user['nomeAgencia'] ?? '',
                'ativo' => (isset($user['status']) && $user['status'] == 1 &&
                           isset($user['bloqueado']) && $user['bloqueado'] == 0)
            ];
        }

        return $usuarios_mapeados;
    }

    // Buscar usuários da API
    $usuarios_api = buscarUsuariosAPI();

    // Estatísticas por usuário PA (período selecionado) - APENAS usuários que enviaram propostas
    $sql_usuarios = "
        SELECT
            usuario_pa,
            COUNT(*) as total_propostas,
            SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) as aprovadas,
            SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
            CASE
                WHEN COUNT(*) > 0 THEN ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1)
                ELSE 0
            END as taxa_devolucao
        FROM acd_formularios
        WHERE pa = ?
        $condicao_data
        AND usuario_pa IS NOT NULL
        AND usuario_pa != ''
        AND usuario_pa != '0'
        GROUP BY usuario_pa
        HAVING COUNT(*) > 0
        ORDER BY total_propostas DESC
        LIMIT 10
    ";
    $stmt = $pdo->prepare($sql_usuarios);
    $stmt->execute([$pa_nome]);
    $estatisticas_usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Evolução dos últimos 7 dias
    $stmt = $pdo->prepare("
        SELECT
            DATE(data_criacao) as data,
            COUNT(*) as total,
            SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas
        FROM acd_formularios
        WHERE pa = ?
        AND data_criacao >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        GROUP BY DATE(data_criacao)
        ORDER BY data DESC
    ");
    $stmt->execute([$pa_nome]);
    $evolucao_7_dias = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Distribuição por usuário PA (período selecionado)
    $sql_usuarios_distribuicao = "
        SELECT
            usuario_pa,
            COUNT(*) as total,
            SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
            CASE
                WHEN COUNT(*) > 0 THEN ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1)
                ELSE 0
            END as taxa_devolucao
        FROM acd_formularios
        WHERE pa = ?
        $condicao_data
        AND usuario_pa IS NOT NULL
        AND usuario_pa != ''
        AND usuario_pa != '0'
        GROUP BY usuario_pa
        ORDER BY total DESC
        LIMIT 15
    ";
    $stmt = $pdo->prepare($sql_usuarios_distribuicao);
    $stmt->execute([$pa_nome]);
    $distribuicao_usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Buscar detalhes das mesas para cada usuário
    foreach ($distribuicao_usuarios as $index => &$usuario) {
        $sql_mesas = "
            SELECT
                mesa,
                COUNT(*) as quantidade
            FROM acd_formularios
            WHERE pa = ?
            $condicao_data
            AND usuario_pa = ?
            GROUP BY mesa
            ORDER BY quantidade DESC
        ";
        $stmt_mesas = $pdo->prepare($sql_mesas);
        $stmt_mesas->execute([$pa_nome, $usuario['usuario_pa']]);
        $mesas_resultado = $stmt_mesas->fetchAll(PDO::FETCH_ASSOC);
        $distribuicao_usuarios[$index]['mesas_detalhes'] = $mesas_resultado;
    }
    unset($usuario); // Limpar referência

    // Distribuição por mesa para gráfico de pizza
    $sql_mesa_pizza = "
        SELECT
            mesa,
            COUNT(*) as total,
            SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
            CASE
                WHEN COUNT(*) > 0 THEN ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1)
                ELSE 0
            END as taxa_devolucao
        FROM acd_formularios
        WHERE pa = ?
        $condicao_data
        GROUP BY mesa
        ORDER BY total DESC
    ";
    $stmt = $pdo->prepare($sql_mesa_pizza);
    $stmt->execute([$pa_nome]);
    $distribuicao_mesa_pizza = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    error_log("Erro ao buscar dados do PA: " . $e->getMessage());
    $pa_info = ['nome' => $pa_nome, 'numero' => 'N/A'];
    $responsavel = null;
    $metricas_periodo = ['total_propostas' => 0, 'aprovadas' => 0, 'devolvidas' => 0, 'taxa_devolucao' => 0];
    $usuarios_api = [];
    $estatisticas_usuarios = [];
    $distribuicao_usuarios = [];
    $distribuicao_mesa_pizza = [];
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard PA: <?php echo htmlspecialchars($pa_info['nome']); ?> - ACD</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #003641;
            --secondary-color: #00AE9D;
            --text-light: #666666;
            --text-dark: #333333;
            --bg-light: #f5f5f5;
            --bg-white: #ffffff;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: var(--bg-light);
            margin: 0;
            padding: 0;
        }

        .wrapper {
            display: flex;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
            padding: 1.5rem;
            margin-left: 250px;
        }

        .page-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .page-title {
            font-size: 2rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }

        .back-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            transform: translateY(-1px);
        }

        .header-controls {
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            margin-top: 1rem;
        }

        .period-filter {
            display: flex;
            align-items: center;
            gap: 1rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .period-filter label {
            color: white;
            font-weight: 600;
            font-size: 0.9rem;
            margin: 0;
        }

        .period-filter select,
        .period-filter input {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            padding: 0.4rem 0.6rem;
            font-size: 0.85rem;
            color: var(--text-dark);
            min-width: 120px;
        }

        .period-filter select:focus,
        .period-filter input:focus {
            outline: none;
            background: white;
            border-color: rgba(255, 255, 255, 0.8);
        }

        .date-inputs {
            display: none;
            gap: 0.5rem;
            align-items: center;
        }

        .date-inputs.show {
            display: flex;
        }

        .date-inputs input {
            min-width: 140px;
        }

        .btn-apply-filter {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.4rem 0.8rem;
            border-radius: 4px;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-apply-filter:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .metric-card {
            background: var(--bg-white);
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .metric-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
        }

        .metric-icon {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            flex-shrink: 0;
        }

        .metric-icon.semana { background: #C9D200; } /* Verde claro */
        .metric-icon.mes { background: #00AE9D; } /* Turquesa */
        .metric-icon.ano { background: #7DB61C; } /* Verde médio */
        .metric-icon.personalizado { background: #49479D; } /* Roxo */

        .metric-content {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .metric-info {
            display: flex;
            flex-direction: column;
        }

        .metric-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0 0 0.25rem 0;
        }

        .metric-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0;
        }

        .metric-details {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            font-size: 0.8rem;
            text-align: right;
        }

        .metric-detail {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            gap: 0.25rem;
        }

        .status-submeter { color: #7DB61C; } /* Verde médio - positivo */
        .status-devolver { color: #003641; } /* Verde escuro - atenção */

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .content-card {
            background: var(--bg-white);
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            overflow: hidden;
            min-height: 500px;
        }

        .content-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1rem 1.5rem;
            font-size: 1.1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .content-body {
            padding: 1.5rem;
        }

        .propostas-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .proposta-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid #f0f0f0;
            transition: background 0.2s ease;
        }

        .proposta-item:hover {
            background: rgba(0, 174, 157, 0.05);
        }

        .proposta-item:last-child {
            border-bottom: none;
        }

        .proposta-info {
            flex: 1;
        }

        .proposta-nome {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.25rem;
        }

        .proposta-detalhes {
            font-size: 0.85rem;
            color: var(--text-light);
        }

        .proposta-status {
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .mesa-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid #f0f0f0;
        }

        .mesa-item:last-child {
            border-bottom: none;
        }

        .mesa-nome {
            font-weight: 600;
            color: var(--text-dark);
            text-transform: capitalize;
        }

        .mesa-metrics {
            display: flex;
            gap: 1rem;
            font-size: 0.85rem;
        }

        .mesa-detalhes {
            font-size: 0.75rem;
            color: var(--text-light);
            margin-top: 0.25rem;
            line-height: 1.2;
        }

        .mesa-item-detail {
            display: inline-block;
            margin-right: 0.75rem;
            margin-bottom: 0.2rem;
        }

        .mesa-item-detail .mesa-tipo {
            font-weight: 600;
            color: var(--secondary-color);
            text-transform: capitalize;
        }

        .mesa-item-detail .mesa-count {
            color: var(--text-dark);
            font-weight: 500;
        }

        .usuarios-pa-container {
            height: auto;
            overflow: visible;
        }

        .mesa-item {
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #f0f0f0;
        }

        .mesa-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .responsavel-info {
            background: rgba(0, 174, 157, 0.1);
            border: 1px solid rgba(0, 174, 157, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .responsavel-info h4 {
            color: var(--secondary-color);
            margin: 0 0 0.5rem 0;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .responsavel-info p {
            margin: 0;
            color: var(--text-dark);
        }

        .no-data {
            text-align: center;
            color: var(--text-light);
            padding: 2rem;
            font-style: italic;
        }

        .chart-container {
            position: relative;
            height: 500px;
            width: 100%;
            padding: 1rem 0;
        }

        .chart-legend {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1rem;
            font-size: 0.85rem;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 2px;
        }

        .legend-propostas { background: #00AE9D; } /* Turquesa */
        .legend-devolucao { background: #003641; } /* Verde escuro */

        .usuario-inativo {
            color: #dc3545 !important;
            font-weight: bold;
        }

        /* Estilos para impressão */
        @media print {
            body {
                print-color-adjust: exact;
                -webkit-print-color-adjust: exact;
                margin: 0;
                padding: 0;
            }

            /* Ocultar elementos desnecessários */
            .wrapper {
                display: block;
            }

            /* Ocultar sidebar/menu */
            .sidebar,
            nav,
            .navbar,
            .menu,
            aside {
                display: none !important;
            }

            /* Ajustar main-content para ocupar toda a largura */
            .main-content {
                margin-left: 0 !important;
                padding: 1rem !important;
                width: 100% !important;
                max-width: none !important;
            }

            /* Ocultar controles de filtro */
            .period-filter,
            .header-controls,
            .back-button {
                display: none !important;
            }

            /* Ajustar cabeçalho para impressão */
            .page-header {
                break-inside: avoid;
                page-break-inside: avoid;
                background: var(--primary-color) !important;
                color: white !important;
                margin-bottom: 1rem;
                padding: 1rem !important;
            }

            /* Cards de métricas */
            .metric-card {
                break-inside: avoid;
                page-break-inside: avoid;
                margin-bottom: 1rem;
            }

            /* Cards de conteúdo */
            .content-card {
                break-inside: avoid;
                page-break-inside: avoid;
                margin-bottom: 1rem;
                box-shadow: none;
                border: 1px solid #ddd;
            }

            /* Gráficos - altura otimizada para impressão */
            .chart-container {
                height: 500px !important;
                break-inside: avoid;
                page-break-inside: avoid;
                margin-bottom: 2rem;
            }

            /* Container de usuários PA - altura otimizada */
            .usuarios-pa-container {
                min-height: 400px;
                max-height: none !important;
                overflow: visible !important;
            }

            /* Grid responsivo para impressão - cada card em página separada */
            .content-grid {
                display: block !important;
                grid-template-columns: none !important;
                gap: 0 !important;
            }

            .metrics-grid {
                grid-template-columns: 1fr !important;
                margin-bottom: 1rem;
            }

            /* Forçar quebra de página para cada card */
            .content-card {
                page-break-before: always !important;
                page-break-after: always !important;
                break-before: page !important;
                break-after: page !important;
                margin-bottom: 0 !important;
                width: 100% !important;
                max-width: none !important;
            }

            /* Primeira card não precisa de quebra antes */
            .content-card:first-child {
                page-break-before: auto !important;
                break-before: auto !important;
            }

            /* Cabeçalho de cada página */
            .content-header {
                font-size: 1.2rem !important;
                padding: 1.5rem !important;
                margin-bottom: 1rem !important;
                background: var(--primary-color) !important;
                color: white !important;
            }

            /* Ajustar conteúdo dos cards para impressão */
            .content-body {
                padding: 2rem !important;
                min-height: 400px;
            }

            /* Garantir que legendas sejam impressas */
            .chart-legend {
                margin-top: 2rem !important;
                font-size: 1rem !important;
            }

            /* Ajustar fontes para impressão */
            .page-title {
                font-size: 1.5rem !important;
            }

            .page-subtitle {
                font-size: 1rem !important;
            }

            /* Ocultar elementos interativos */
            button:not(.no-print),
            .btn:not(.no-print),
            .tooltip,
            .dropdown {
                display: none !important;
            }

            /* Garantir que cores sejam impressas */
            * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
            }
        }

        /* Responsividade */
        @media (max-width: 1200px) {
            .main-content {
                margin-left: 0;
                padding: 1rem;
            }
        }

        @media (max-width: 1024px) {
            .content-grid {
                grid-template-columns: 1fr 1fr;
            }
        }

        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }

            .page-header {
                padding: 1.5rem;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .chart-container {
                height: 350px;
            }

            .header-controls {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .period-filter {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'components/sidebar.php'; ?>
        
        <div class="main-content">
            <!-- Cabeçalho da Página -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="fas fa-building"></i>
                    Dashboard PA: <?php echo htmlspecialchars($pa_info['nome']); ?>
                </h1>
                <p class="page-subtitle">
                    Número: <?php echo htmlspecialchars($pa_info['numero']); ?> •
                    <?php echo $descricao_periodo; ?>
                </p>

                <div class="header-controls">
                    <a href="rankings.php" class="back-button">
                        <i class="fas fa-arrow-left"></i>
                        Voltar aos Rankings
                    </a>

                    <form method="GET" action="" class="period-filter">
                        <input type="hidden" name="pa" value="<?php echo htmlspecialchars($pa_nome); ?>">

                        <label for="periodo">Período:</label>
                        <select name="periodo" id="periodo" onchange="toggleDateInputs()">
                            <option value="mes" <?php echo $filtro_periodo === 'mes' ? 'selected' : ''; ?>>Este Mês</option>
                            <option value="ano" <?php echo $filtro_periodo === 'ano' ? 'selected' : ''; ?>>Este Ano</option>
                            <option value="personalizado" <?php echo $filtro_periodo === 'personalizado' ? 'selected' : ''; ?>>Personalizado</option>
                        </select>

                        <div class="date-inputs <?php echo $filtro_periodo === 'personalizado' ? 'show' : ''; ?>" id="dateInputs">
                            <input type="date" name="data_inicio" value="<?php echo htmlspecialchars($filtro_data_inicio); ?>" placeholder="Data início">
                            <input type="date" name="data_fim" value="<?php echo htmlspecialchars($filtro_data_fim); ?>" placeholder="Data fim">
                        </div>

                        <button type="submit" class="btn-apply-filter">
                            <i class="fas fa-filter"></i> Aplicar
                        </button>

                        <button type="button" class="btn-apply-filter" onclick="window.print()" style="margin-left: 0.5rem;">
                            <i class="fas fa-print"></i> Imprimir
                        </button>
                    </form>
                </div>
            </div>

            <!-- Informações do Responsável -->
            <?php if ($responsavel): ?>
                <div class="responsavel-info">
                    <h4>
                        <i class="fas fa-user-tie"></i>
                        Responsável Atual
                    </h4>
                    <p>
                        <strong><?php echo htmlspecialchars($responsavel['responsavel_nome']); ?></strong> • 
                        Responsável desde <?php echo date('d/m/Y', strtotime($responsavel['data_inicio'])); ?>
                    </p>
                </div>
            <?php endif; ?>

            <!-- Métrica Principal -->
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-icon <?php echo $filtro_periodo; ?>">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="metric-content">
                        <div class="metric-info">
                            <h3 class="metric-title"><?php echo $descricao_periodo; ?></h3>
                            <div class="metric-value"><?php echo number_format($metricas_periodo['total_propostas'], 0, ',', '.'); ?></div>
                        </div>
                        <div class="metric-details">
                            <div class="metric-detail status-submeter">
                                <i class="fas fa-check"></i>
                                <?php echo $metricas_periodo['aprovadas']; ?> aprovadas
                            </div>
                            <div class="metric-detail status-devolver">
                                <i class="fas fa-times"></i>
                                <?php echo $metricas_periodo['devolvidas']; ?> devolvidas (<?php echo $metricas_periodo['taxa_devolucao']; ?>%)
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Conteúdo Principal -->
            <div class="content-grid">
                <!-- Gráfico de Usuários -->
                <div class="content-card">
                    <div class="content-header">
                        <i class="fas fa-chart-bar"></i>
                        Propostas por Usuário (<?php echo $descricao_periodo; ?>)
                    </div>
                    <div class="content-body">
                        <?php if (!empty($estatisticas_usuarios)): ?>
                            <div class="chart-container">
                                <canvas id="usuariosChart"></canvas>
                            </div>
                            <div class="chart-legend">
                                <div class="legend-item">
                                    <div class="legend-color legend-propostas"></div>
                                    <span>Propostas Enviadas</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color legend-devolucao"></div>
                                    <span>% Devolução (exibido nas barras)</span>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="no-data">
                                <i class="fas fa-info-circle"></i>
                                Nenhum usuário enviou propostas neste período
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Distribuição por Usuários PA -->
                <div class="content-card">
                    <div class="content-header">
                        <i class="fas fa-users"></i>
                        Usuários do PA (<?php echo $descricao_periodo; ?>)
                    </div>
                    <div class="content-body">
                        <?php if (!empty($distribuicao_usuarios)): ?>
                            <div class="usuarios-pa-container">
                                <?php foreach ($distribuicao_usuarios as $usuario): ?>
                                    <div class="mesa-item">
                                        <div>
                                            <div class="mesa-nome">
                                                <?php
                                                // Buscar nome do usuário PA na API
                                                $nome_usuario_pa = isset($usuarios_api[$usuario['usuario_pa']]) ?
                                                    $usuarios_api[$usuario['usuario_pa']]['nome'] :
                                                    'ID: ' . $usuario['usuario_pa'];

                                                // Verificar se usuário está ativo
                                                $is_ativo = isset($usuarios_api[$usuario['usuario_pa']]) ?
                                                    $usuarios_api[$usuario['usuario_pa']]['ativo'] : false;

                                                $classe_usuario = $is_ativo ? '' : 'usuario-inativo';
                                                if (!$is_ativo && isset($usuarios_api[$usuario['usuario_pa']])) {
                                                    $nome_usuario_pa .= ' (INATIVO)';
                                                }
                                                ?>
                                                <span class="<?php echo $classe_usuario; ?>">
                                                    <?php echo htmlspecialchars($nome_usuario_pa); ?>
                                                </span>
                                            </div>

                                            <!-- Detalhes das mesas -->
                                            <?php if (!empty($usuario['mesas_detalhes']) && is_array($usuario['mesas_detalhes'])): ?>
                                                <div class="mesa-detalhes">
                                                    <?php foreach ($usuario['mesas_detalhes'] as $mesa_info): ?>
                                                        <span class="mesa-item-detail">
                                                            <span class="mesa-tipo"><?php echo htmlspecialchars(ucfirst($mesa_info['mesa'])); ?>:</span>
                                                            <span class="mesa-count"><?php echo $mesa_info['quantidade']; ?></span>
                                                        </span>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <div class="mesa-metrics">
                                            <span class="status-submeter"><?php echo $usuario['total']; ?> propostas</span>
                                            <span class="status-devolver"><?php echo $usuario['taxa_devolucao']; ?>% devolução</span>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="no-data">
                                <i class="fas fa-info-circle"></i>
                                Nenhum usuário do PA enviou propostas neste período
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Gráfico de Pizza por Mesa -->
                <div class="content-card">
                    <div class="content-header">
                        <i class="fas fa-chart-pie"></i>
                        Distribuição por Mesa (<?php echo $descricao_periodo; ?>)
                    </div>
                    <div class="content-body">
                        <?php if (!empty($distribuicao_mesa_pizza)): ?>
                            <div class="chart-container" style="height: 350px;">
                                <canvas id="mesaChart"></canvas>
                            </div>
                        <?php else: ?>
                            <div class="no-data">
                                <i class="fas fa-info-circle"></i>
                                Nenhum dado disponível para este período
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM carregado, iniciando gráficos...');

            // GRÁFICO DE USUÁRIOS
            <?php if (!empty($estatisticas_usuarios)): ?>
            const usuariosData = <?php echo json_encode($estatisticas_usuarios); ?>;
            const usuariosAPI = <?php echo json_encode($usuarios_api); ?>;

            console.log('Dados usuários:', usuariosData);
            console.log('API usuários:', Object.keys(usuariosAPI).length, 'usuários');

            if (usuariosData && usuariosData.length > 0) {
                const labels = usuariosData.map(user => {
                    const nome = usuariosAPI[user.usuario_pa] ? usuariosAPI[user.usuario_pa].nome : `ID: ${user.usuario_pa}`;
                    return nome.length > 15 ? nome.substring(0, 15) + '...' : nome;
                });

                const propostas = usuariosData.map(user => parseInt(user.total_propostas));

                console.log('Labels:', labels);
                console.log('Propostas:', propostas);

                const ctx = document.getElementById('usuariosChart');
                if (ctx) {
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: labels,
                            datasets: [{
                                label: 'Propostas',
                                data: propostas,
                                backgroundColor: 'rgba(0, 174, 157, 0.8)', // --secondary-color
                                borderColor: '#00AE9D',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            indexAxis: 'y',
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                x: { beginAtZero: true }
                            },
                            plugins: {
                                legend: { display: false },
                                tooltip: {
                                    callbacks: {
                                        title: function(context) {
                                            const index = context[0].dataIndex;
                                            const user = usuariosData[index];
                                            return usuariosAPI[user.usuario_pa] ? usuariosAPI[user.usuario_pa].nome : `ID: ${user.usuario_pa}`;
                                        },
                                        label: function(context) {
                                            const index = context.dataIndex;
                                            const user = usuariosData[index];
                                            return `Propostas: ${user.total_propostas} (${user.aprovadas} aprovadas, ${user.devolvidas} devolvidas) - Devolução: ${user.taxa_devolucao}%`;
                                        }
                                    }
                                }
                            }
                        },
                        plugins: [{
                            afterDatasetsDraw: function(chart) {
                                const ctx = chart.ctx;
                                chart.data.datasets.forEach((dataset, i) => {
                                    const meta = chart.getDatasetMeta(i);
                                    meta.data.forEach((bar, index) => {
                                        const user = usuariosData[index];
                                        ctx.fillStyle = '#003641'; // --primary-color
                                        ctx.font = 'bold 11px Arial';
                                        ctx.textAlign = 'left';
                                        ctx.textBaseline = 'middle';
                                        ctx.fillText(`${user.taxa_devolucao}%`, bar.x + 5, bar.y);
                                    });
                                });
                            }
                        }]
                    });
                    console.log('Gráfico de usuários criado');
                }
            }
            <?php endif; ?>

        // Gráfico de Pizza para Distribuição por Mesa
        <?php if (!empty($distribuicao_mesa_pizza)): ?>
        console.log('Dados mesa pizza:', <?php echo json_encode($distribuicao_mesa_pizza); ?>);

        const mesaData = <?php echo json_encode($distribuicao_mesa_pizza); ?>;

        if (mesaData && mesaData.length > 0) {
            const mesaLabels = mesaData.map(mesa => mesa.mesa.charAt(0).toUpperCase() + mesa.mesa.slice(1));
            const mesaValues = mesaData.map(mesa => parseInt(mesa.total));

            console.log('Mesa labels:', mesaLabels);
            console.log('Mesa values:', mesaValues);

            // Cores para o gráfico de pizza (identidade visual Sicoob)
            const mesaColors = [
                '#00AE9D', // Turquesa (cor principal)
                '#003641', // Verde escuro (cor principal)
                '#C9D200', // Verde claro (apoio)
                '#7DB61C', // Verde médio (apoio)
                '#49479D', // Roxo (apoio)
                '#4ECDC4', // Turquesa claro (variação)
                '#2C5F5D', // Verde escuro claro (variação)
                '#A8B800', // Verde claro escuro (variação)
                '#5A9615', // Verde médio escuro (variação)
                '#3A3782'  // Roxo escuro (variação)
            ];

            const ctxMesa = document.getElementById('mesaChart');
            if (ctxMesa) {
                const mesaChart = new Chart(ctxMesa, {
                    type: 'pie',
                    data: {
                        labels: mesaLabels,
                        datasets: [{
                            data: mesaValues,
                            backgroundColor: mesaColors.slice(0, mesaLabels.length),
                            borderColor: '#ffffff',
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 15,
                                    font: {
                                        size: 11
                                    }
                                }
                            },
                            tooltip: {
                                backgroundColor: 'rgba(0, 0, 0, 0.9)',
                                titleColor: '#ffffff',
                                bodyColor: '#ffffff',
                                borderColor: '#ffffff',
                                borderWidth: 1,
                                callbacks: {
                                    label: function(context) {
                                        const mesa = mesaData[context.dataIndex];
                                        const total = mesaValues.reduce((a, b) => a + b, 0);
                                        const percentage = ((mesa.total / total) * 100).toFixed(1);
                                        return `${context.label}: ${mesa.total} propostas (${percentage}%) - ${mesa.taxa_devolucao}% devolução`;
                                    }
                                }
                            }
                        }
                    },
                    plugins: [{
                        id: 'datalabels',
                        afterDatasetsDraw: function(chart) {
                            const ctx = chart.ctx;
                            const dataset = chart.data.datasets[0];
                            const total = dataset.data.reduce((a, b) => a + b, 0);

                            chart.data.datasets.forEach((dataset, i) => {
                                const meta = chart.getDatasetMeta(i);
                                meta.data.forEach((segment, index) => {
                                    const value = dataset.data[index];
                                    const percentage = ((value / total) * 100).toFixed(1);

                                    // Calcular posição do centro da fatia
                                    const angle = segment.startAngle + (segment.endAngle - segment.startAngle) / 2;
                                    const radius = (segment.innerRadius + segment.outerRadius) / 2;

                                    const x = segment.x + Math.cos(angle) * radius * 0.7;
                                    const y = segment.y + Math.sin(angle) * radius * 0.7;

                                    // Configurar texto
                                    ctx.fillStyle = '#ffffff';
                                    ctx.font = 'bold 12px Arial';
                                    ctx.textAlign = 'center';
                                    ctx.textBaseline = 'middle';

                                    // Adicionar sombra para melhor legibilidade
                                    ctx.shadowColor = 'rgba(0, 0, 0, 0.7)';
                                    ctx.shadowBlur = 3;
                                    ctx.shadowOffsetX = 1;
                                    ctx.shadowOffsetY = 1;

                                    // Desenhar percentual apenas se for maior que 5%
                                    if (parseFloat(percentage) > 5) {
                                        ctx.fillText(`${percentage}%`, x, y);
                                    }

                                    // Resetar sombra
                                    ctx.shadowColor = 'transparent';
                                    ctx.shadowBlur = 0;
                                    ctx.shadowOffsetX = 0;
                                    ctx.shadowOffsetY = 0;
                                });
                            });
                        }
                    }]
                });
                console.log('Gráfico de pizza criado com sucesso');
            } else {
                console.error('Canvas mesaChart não encontrado');
            }
        } else {
            console.log('Nenhum dado de mesa disponível');
        }
        <?php else: ?>
        console.log('Distribuição mesa pizza está vazia');
        <?php endif; ?>

        }); // Fim DOMContentLoaded

        // Função para mostrar/ocultar campos de data
        function toggleDateInputs() {
            const periodo = document.getElementById('periodo').value;
            const dateInputs = document.getElementById('dateInputs');

            if (periodo === 'personalizado') {
                dateInputs.classList.add('show');
            } else {
                dateInputs.classList.remove('show');
            }
        }
    </script>
</body>
</html>
