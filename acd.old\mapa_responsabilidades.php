<?php
require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';
require_once 'vinculos_functions.php';
session_start();

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se o usuário tem permissão para acessar a pasta ACD
if (!checkACDPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Verificar se o usuário é administrador ou gestor
if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Buscar todos os PAs com seus responsáveis
$sql = "
    SELECT 
        pa.id,
        pa.numero,
        pa.nome as pa_nome,
        v.id as vinculo_id,
        v.usuario_id,
        v.data_inicio,
        u.nome_completo as responsavel_nome,
        u.email as responsavel_email
    FROM pontos_atendimento pa
    LEFT JOIN acd_usuario_pa v ON (
        pa.id = v.pa_id 
        AND v.status = 'ativo'
        AND v.data_inicio <= CURDATE()
        AND (v.data_fim IS NULL OR v.data_fim > CURDATE())
    )
    LEFT JOIN usuarios u ON v.usuario_id = u.id
    ORDER BY pa.numero
";

$stmt = $pdo->prepare($sql);
$stmt->execute();
$pas_responsaveis = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Separar PAs com e sem responsável
$pas_com_responsavel = [];
$pas_sem_responsavel = [];

foreach ($pas_responsaveis as $pa) {
    if ($pa['responsavel_nome']) {
        $pas_com_responsavel[] = $pa;
    } else {
        $pas_sem_responsavel[] = $pa;
    }
}

// Estatísticas
$total_pas = count($pas_responsaveis);
$total_com_responsavel = count($pas_com_responsavel);
$total_sem_responsavel = count($pas_sem_responsavel);
$percentual_cobertura = $total_pas > 0 ? round(($total_com_responsavel / $total_pas) * 100, 1) : 0;
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="../assets/images/Sicoob.ico">
    <title>Mapa de Responsabilidades - Análise de Crédito</title>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <style>
        :root {
            --sicoob-turquesa: #00AE9D;
            --sicoob-verde-escuro: #003641;
            --sicoob-branco: #FFFFFF;
            --sicoob-verde-claro: #C9D200;
            --sicoob-verde-medio: #7DB61C;
            --sicoob-roxo: #49479D;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .wrapper {
            display: flex;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 2rem;
            transition: all 0.3s ease;
        }

        .content-container {
            background: var(--sicoob-branco);
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 8px rgba(0, 54, 65, 0.1);
            border-top: 4px solid var(--sicoob-turquesa);
        }

        .content-title {
            color: var(--sicoob-verde-escuro);
            margin-bottom: 2rem;
            font-weight: 600;
            position: relative;
            padding-bottom: 1rem;
        }

        .content-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--sicoob-turquesa);
        }

        .stats-card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }

        .stats-card:hover {
            transform: translateY(-2px);
        }

        .pa-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .pa-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .pa-card.sem-responsavel {
            border-left: 4px solid #ffc107;
            background: #fff8e1;
        }

        .pa-card.com-responsavel {
            border-left: 4px solid #28a745;
            background: #f8fff9;
        }

        .responsavel-info {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 0.5rem;
            margin-top: 0.5rem;
        }

        .btn-primary {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
        }

        .btn-primary:hover {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'components/sidebar.php'; ?>

        <div class="main-content">
            <div class="content-container">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="content-title mb-0">
                        <i class="fas fa-map me-2"></i>Mapa de Responsabilidades
                    </h2>
                    <a href="gerenciar_vinculos.php" class="btn btn-primary">
                        <i class="fas fa-link me-2"></i>Gerenciar Vínculos
                    </a>
                </div>

                <!-- Estatísticas -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card stats-card border-primary">
                            <div class="card-body text-center">
                                <h3 class="text-primary mb-1"><?php echo $total_pas; ?></h3>
                                <p class="card-text mb-0">Total de PAs</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card border-success">
                            <div class="card-body text-center">
                                <h3 class="text-success mb-1"><?php echo $total_com_responsavel; ?></h3>
                                <p class="card-text mb-0">Com Responsável</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card border-warning">
                            <div class="card-body text-center">
                                <h3 class="text-warning mb-1"><?php echo $total_sem_responsavel; ?></h3>
                                <p class="card-text mb-0">Sem Responsável</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card stats-card border-info">
                            <div class="card-body text-center">
                                <h3 class="text-info mb-1"><?php echo $percentual_cobertura; ?>%</h3>
                                <p class="card-text mb-0">Cobertura</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Regra de Responsabilidade Única -->
                <div class="alert alert-info mb-4">
                    <h6 class="alert-heading">
                        <i class="fas fa-info-circle me-2"></i>Regra de Responsabilidade Única
                    </h6>
                    <p class="mb-0">
                        Cada Ponto de Atendimento possui <strong>apenas UM responsável ativo</strong> por vez. 
                        Esta regra garante clareza na responsabilidade e evita conflitos.
                    </p>
                </div>

                <div class="row">
                    <!-- PAs com Responsável -->
                    <div class="col-md-6">
                        <h4 class="text-success mb-3">
                            <i class="fas fa-user-check me-2"></i>PAs com Responsável (<?php echo $total_com_responsavel; ?>)
                        </h4>
                        
                        <?php if (empty($pas_com_responsavel)): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Nenhum PA possui responsável ativo no momento.
                            </div>
                        <?php else: ?>
                            <?php foreach ($pas_com_responsavel as $pa): ?>
                                <div class="pa-card com-responsavel">
                                    <div class="card-body">
                                        <h6 class="card-title mb-2">
                                            <i class="fas fa-building me-2"></i>
                                            PA <?php echo htmlspecialchars($pa['numero']); ?> - <?php echo htmlspecialchars($pa['pa_nome']); ?>
                                        </h6>
                                        
                                        <div class="responsavel-info">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <strong>
                                                        <i class="fas fa-user me-1"></i>
                                                        <?php echo htmlspecialchars($pa['responsavel_nome']); ?>
                                                    </strong><br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-envelope me-1"></i>
                                                        <?php echo htmlspecialchars($pa['responsavel_email']); ?>
                                                    </small><br>
                                                    <small class="text-muted">
                                                        <i class="fas fa-calendar me-1"></i>
                                                        Desde: <?php echo date('d/m/Y', strtotime($pa['data_inicio'])); ?>
                                                    </small>
                                                </div>
                                                <div>
                                                    <a href="historico_vinculos.php?pa_id=<?php echo $pa['id']; ?>" 
                                                       class="btn btn-outline-info btn-sm" title="Ver Histórico">
                                                        <i class="fas fa-history"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>

                    <!-- PAs sem Responsável -->
                    <div class="col-md-6">
                        <h4 class="text-warning mb-3">
                            <i class="fas fa-user-times me-2"></i>PAs sem Responsável (<?php echo $total_sem_responsavel; ?>)
                        </h4>
                        
                        <?php if (empty($pas_sem_responsavel)): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                Excelente! Todos os PAs possuem responsável ativo.
                            </div>
                        <?php else: ?>
                            <?php foreach ($pas_sem_responsavel as $pa): ?>
                                <div class="pa-card sem-responsavel">
                                    <div class="card-body">
                                        <h6 class="card-title mb-2">
                                            <i class="fas fa-building me-2"></i>
                                            PA <?php echo htmlspecialchars($pa['numero']); ?> - <?php echo htmlspecialchars($pa['pa_nome']); ?>
                                        </h6>
                                        
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-warning">
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                Sem responsável ativo
                                            </small>
                                            <a href="gerenciar_vinculos.php" class="btn btn-warning btn-sm">
                                                <i class="fas fa-plus me-1"></i>Atribuir
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
