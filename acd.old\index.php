<?php
require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';
session_start();

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se o usuário tem permissão para acessar a pasta ACD
if (!checkACDPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Verificar se o usuário é administrador ou gestor
if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Buscar propostas analisadas da semana atual, mês e ano
try {
    // Total geral
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM acd_formularios");
    $total_geral = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Total da semana
    $stmt = $pdo->query("SELECT 
                            COUNT(*) as total_semana,
                            DATE_FORMAT(MIN(data_criacao), '%d/%m') as inicio_semana,
                            DATE_FORMAT(MAX(data_criacao), '%d/%m') as fim_semana
                         FROM acd_formularios 
                         WHERE YEARWEEK(data_criacao, 1) = YEARWEEK(CURDATE(), 1)");
    $resultado_semana = $stmt->fetch(PDO::FETCH_ASSOC);
    $total_semana = $resultado_semana['total_semana'];
    $periodo_semana = $resultado_semana['inicio_semana'] . ' a ' . $resultado_semana['fim_semana'];

    // Total do mês
    $stmt = $pdo->query("SELECT 
                            COUNT(*) as total_mes,
                            DATE_FORMAT(MIN(data_criacao), '%d/%m') as inicio_mes,
                            DATE_FORMAT(MAX(data_criacao), '%d/%m') as fim_mes
                         FROM acd_formularios 
                         WHERE YEAR(data_criacao) = YEAR(CURDATE()) 
                         AND MONTH(data_criacao) = MONTH(CURDATE())");
    $resultado_mes = $stmt->fetch(PDO::FETCH_ASSOC);
    $total_mes = $resultado_mes['total_mes'];
    $periodo_mes = $resultado_mes['inicio_mes'] . ' a ' . $resultado_mes['fim_mes'];

    // Total do ano
    $stmt = $pdo->query("SELECT 
                            COUNT(*) as total_ano,
                            DATE_FORMAT(MIN(data_criacao), '%d/%m') as inicio_ano,
                            DATE_FORMAT(MAX(data_criacao), '%d/%m') as fim_ano
                         FROM acd_formularios 
                         WHERE YEAR(data_criacao) = YEAR(CURDATE())");
    $resultado_ano = $stmt->fetch(PDO::FETCH_ASSOC);
    $total_ano = $resultado_ano['total_ano'];
    $periodo_ano = $resultado_ano['inicio_ano'] . ' a ' . $resultado_ano['fim_ano'];

    // PA com mais propostas na semana
    $stmt = $pdo->query("SELECT 
                            pa,
                            COUNT(*) as total_propostas
                         FROM acd_formularios 
                         WHERE YEARWEEK(data_criacao, 1) = YEARWEEK(CURDATE(), 1)
                         GROUP BY pa
                         ORDER BY total_propostas DESC
                         LIMIT 1");
    $resultado_pa = $stmt->fetch(PDO::FETCH_ASSOC);
    $pa_mais_propostas = $resultado_pa['pa'] ?? 'Nenhum';
    $total_pa = $resultado_pa['total_propostas'] ?? 0;

    // PA com mais propostas no mês
    $stmt = $pdo->query("SELECT 
                            pa,
                            COUNT(*) as total_propostas
                         FROM acd_formularios 
                         WHERE YEAR(data_criacao) = YEAR(CURDATE()) 
                         AND MONTH(data_criacao) = MONTH(CURDATE())
                         GROUP BY pa
                         ORDER BY total_propostas DESC
                         LIMIT 1");
    $resultado_pa_mes = $stmt->fetch(PDO::FETCH_ASSOC);
    $pa_mais_propostas_mes = $resultado_pa_mes['pa'] ?? 'Nenhum';
    $total_pa_mes = $resultado_pa_mes['total_propostas'] ?? 0;

    // PA com mais propostas no ano
    $stmt = $pdo->query("SELECT 
                            pa,
                            COUNT(*) as total_propostas
                         FROM acd_formularios 
                         WHERE YEAR(data_criacao) = YEAR(CURDATE())
                         GROUP BY pa
                         ORDER BY total_propostas DESC
                         LIMIT 1");
    $resultado_pa_ano = $stmt->fetch(PDO::FETCH_ASSOC);
    $pa_mais_propostas_ano = $resultado_pa_ano['pa'] ?? 'Nenhum';
    $total_pa_ano = $resultado_pa_ano['total_propostas'] ?? 0;

    // PA com menor taxa de devolução na semana
    $stmt = $pdo->query("SELECT 
                            pa,
                            COUNT(*) as total_propostas,
                            SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) as aprovadas,
                            ROUND((SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) / COUNT(*)) * 100) as taxa_aprovacao
                         FROM acd_formularios 
                         WHERE YEARWEEK(data_criacao, 1) = YEARWEEK(CURDATE(), 1)
                         AND pa != '0'
                         GROUP BY pa
                         HAVING COUNT(*) >= 5
                         ORDER BY taxa_aprovacao DESC, total_propostas DESC
                         LIMIT 1");
    $resultado_pa_aprovacao = $stmt->fetch(PDO::FETCH_ASSOC);
    $pa_menor_devolucao = $resultado_pa_aprovacao['pa'] ?? 'Nenhum';
    $taxa_pa_aprovacao = $resultado_pa_aprovacao['taxa_aprovacao'] ?? 0;
    $total_pa_aprovacao = $resultado_pa_aprovacao['total_propostas'] ?? 0;

    // PA com menor taxa de devolução no mês
    $stmt = $pdo->query("SELECT 
                            pa,
                            COUNT(*) as total_propostas,
                            SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) as aprovadas,
                            ROUND((SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) / COUNT(*)) * 100) as taxa_aprovacao
                         FROM acd_formularios 
                         WHERE YEAR(data_criacao) = YEAR(CURDATE()) 
                         AND MONTH(data_criacao) = MONTH(CURDATE())
                         AND pa != '0'
                         GROUP BY pa
                         HAVING COUNT(*) >= 10
                         ORDER BY taxa_aprovacao DESC, total_propostas DESC
                         LIMIT 1");
    $resultado_pa_aprovacao_mes = $stmt->fetch(PDO::FETCH_ASSOC);
    $pa_menor_devolucao_mes = $resultado_pa_aprovacao_mes['pa'] ?? 'Nenhum';
    $taxa_pa_aprovacao_mes = $resultado_pa_aprovacao_mes['taxa_aprovacao'] ?? 0;
    $total_pa_aprovacao_mes = $resultado_pa_aprovacao_mes['total_propostas'] ?? 0;

    // PA com menor taxa de devolução no ano
    $stmt = $pdo->query("SELECT 
                            pa,
                            COUNT(*) as total_propostas,
                            SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) as aprovadas,
                            ROUND((SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) / COUNT(*)) * 100) as taxa_aprovacao
                         FROM acd_formularios 
                         WHERE YEAR(data_criacao) = YEAR(CURDATE())
                         AND pa != '0'
                         GROUP BY pa
                         HAVING COUNT(*) >= 30
                         ORDER BY taxa_aprovacao DESC, total_propostas DESC
                         LIMIT 1");
    $resultado_pa_aprovacao_ano = $stmt->fetch(PDO::FETCH_ASSOC);
    $pa_menor_devolucao_ano = $resultado_pa_aprovacao_ano['pa'] ?? 'Nenhum';
    $taxa_pa_aprovacao_ano = $resultado_pa_aprovacao_ano['taxa_aprovacao'] ?? 0;
    $total_pa_aprovacao_ano = $resultado_pa_aprovacao_ano['total_propostas'] ?? 0;

    // Taxa de devolução da semana
    $stmt = $pdo->query("SELECT
                            COUNT(*) as total_semana,
                            SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas
                         FROM acd_formularios
                         WHERE YEARWEEK(data_criacao, 1) = YEARWEEK(CURDATE(), 1)");
    $resultado_taxa = $stmt->fetch(PDO::FETCH_ASSOC);
    $total_semana_taxa = $resultado_taxa['total_semana'];
    $devolvidas = $resultado_taxa['devolvidas'];
    $taxa_devolucao = $total_semana_taxa > 0 ? round(($devolvidas / $total_semana_taxa) * 100, 1) : 0;

    // Taxa de devolução do mês
    $stmt = $pdo->query("SELECT
                            COUNT(*) as total_mes,
                            SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas
                         FROM acd_formularios
                         WHERE YEAR(data_criacao) = YEAR(CURDATE())
                         AND MONTH(data_criacao) = MONTH(CURDATE())");
    $resultado_taxa_mes = $stmt->fetch(PDO::FETCH_ASSOC);
    $total_mes_taxa = $resultado_taxa_mes['total_mes'];
    $devolvidas_mes = $resultado_taxa_mes['devolvidas'];
    $taxa_devolucao_mes = $total_mes_taxa > 0 ? round(($devolvidas_mes / $total_mes_taxa) * 100, 1) : 0;

    // Taxa de devolução do ano
    $stmt = $pdo->query("SELECT
                            COUNT(*) as total_ano,
                            SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas
                         FROM acd_formularios
                         WHERE YEAR(data_criacao) = YEAR(CURDATE())");
    $resultado_taxa_ano = $stmt->fetch(PDO::FETCH_ASSOC);
    $total_ano_taxa = $resultado_taxa_ano['total_ano'];
    $devolvidas_ano = $resultado_taxa_ano['devolvidas'];
    $taxa_devolucao_ano = $total_ano_taxa > 0 ? round(($devolvidas_ano / $total_ano_taxa) * 100, 1) : 0;







    // Buscar métricas de prazo de aprovação das tabelas de importação
    // Observação: os valores estão com +1, então prazo_dia = 1 significa 0 dias reais

    // Aprovações no mesmo dia (prazo_dia = 1, que significa 0 dias reais)
    $stmt = $pdo->query("
        SELECT COUNT(*) as total FROM (
            SELECT 1 FROM acd_limites WHERE prazo_dia = 1
            UNION ALL
            SELECT 1 FROM acd_emprestimo WHERE prazo_dia = 1
            UNION ALL
            SELECT 1 FROM acd_creditorural WHERE prazo_dia = 1
            UNION ALL
            SELECT 1 FROM acd_financiamento WHERE prazo_dia = 1
        ) as aprovacoes_mesmo_dia
    ");
    $aprovacoes_mesmo_dia = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Aprovações em 1 dia (prazo_dia = 2, que significa 1 dia real)
    $stmt = $pdo->query("
        SELECT COUNT(*) as total FROM (
            SELECT 1 FROM acd_limites WHERE prazo_dia = 2
            UNION ALL
            SELECT 1 FROM acd_emprestimo WHERE prazo_dia = 2
            UNION ALL
            SELECT 1 FROM acd_creditorural WHERE prazo_dia = 2
            UNION ALL
            SELECT 1 FROM acd_financiamento WHERE prazo_dia = 2
        ) as aprovacoes_1_dia
    ");
    $aprovacoes_1_dia = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Aprovações em 2 dias (prazo_dia = 3, que significa 2 dias reais)
    $stmt = $pdo->query("
        SELECT COUNT(*) as total FROM (
            SELECT 1 FROM acd_limites WHERE prazo_dia = 3
            UNION ALL
            SELECT 1 FROM acd_emprestimo WHERE prazo_dia = 3
            UNION ALL
            SELECT 1 FROM acd_creditorural WHERE prazo_dia = 3
            UNION ALL
            SELECT 1 FROM acd_financiamento WHERE prazo_dia = 3
        ) as aprovacoes_2_dias
    ");
    $aprovacoes_2_dias = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Aprovações acima de 2 dias (prazo_dia > 3, que significa mais de 2 dias reais)
    $stmt = $pdo->query("
        SELECT COUNT(*) as total FROM (
            SELECT 1 FROM acd_limites WHERE prazo_dia > 3
            UNION ALL
            SELECT 1 FROM acd_emprestimo WHERE prazo_dia > 3
            UNION ALL
            SELECT 1 FROM acd_creditorural WHERE prazo_dia > 3
            UNION ALL
            SELECT 1 FROM acd_financiamento WHERE prazo_dia > 3
        ) as aprovacoes_acima_2_dias
    ");
    $aprovacoes_acima_2_dias = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Total geral de aprovações para calcular percentuais
    $total_aprovacoes = $aprovacoes_mesmo_dia + $aprovacoes_1_dia + $aprovacoes_2_dias + $aprovacoes_acima_2_dias;

    // Aprovações até 1 dia (soma do mesmo dia + 1 dia)
    $aprovacoes_ate_1_dia = $aprovacoes_mesmo_dia + $aprovacoes_1_dia;

} catch (Exception $e) {
    error_log("Erro ao buscar propostas: " . $e->getMessage());
    $total_geral = 0;
    $total_semana = 0;
    $total_mes = 0;
    $total_ano = 0;
    $periodo_semana = '';
    $periodo_mes = '';
    $periodo_ano = '';
    $taxa_devolucao = 0;
    $taxa_devolucao_mes = 0;
    $taxa_devolucao_ano = 0;
    $devolvidas = 0;
    $devolvidas_mes = 0;
    $devolvidas_ano = 0;
    $total_semana_taxa = 0;
    $total_mes_taxa = 0;
    $total_ano_taxa = 0;
    $pa_mais_propostas = 'Nenhum';
    $pa_mais_propostas_mes = 'Nenhum';
    $pa_mais_propostas_ano = 'Nenhum';
    $total_pa = 0;
    $total_pa_mes = 0;
    $total_pa_ano = 0;
    $pa_menor_devolucao = 'Nenhum';
    $pa_menor_devolucao_mes = 'Nenhum';
    $pa_menor_devolucao_ano = 'Nenhum';
    $taxa_pa_aprovacao = 0;
    $taxa_pa_aprovacao_mes = 0;
    $taxa_pa_aprovacao_ano = 0;
    $total_pa_aprovacao = 0;
    $total_pa_aprovacao_mes = 0;
    $total_pa_aprovacao_ano = 0;
    $podio_semana = [];
    $podio_mes = [];
    $podio_ano = [];
    $aprovacoes_mesmo_dia = 0;
    $aprovacoes_1_dia = 0;
    $aprovacoes_2_dias = 0;
    $aprovacoes_acima_2_dias = 0;
    $total_aprovacoes = 0;
    $aprovacoes_ate_1_dia = 0;
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="../assets/images/Sicoob.ico">
    <title>ACD - Dashboard Análise de Crédito</title>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <style>
        :root {
            --sidebar-width: 250px;
            --primary-color: #003641;
            --secondary-color: #00AE9D;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f5f5f5;
            --bg-white: #ffffff;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: var(--bg-light);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .wrapper {
            display: flex;
            min-height: 100vh;
        }

        /* Main Content Styles */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        /* Dashboard Cards */
        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        /* Dashboard Cards com destaque (5 colunas) */
        .dashboard-cards.with-highlight {
            grid-template-columns: repeat(5, 1fr);
        }

        .dashboard-card {
            background: var(--bg-white);
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-width: 0; /* Evita overflow */
        }

        .dashboard-card h3 {
            color: var(--primary-color);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .dashboard-card .value {
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--secondary-color);
            line-height: 1.2;
        }

        .dashboard-card .label {
            color: var(--text-dark);
            font-size: 0.75rem;
            margin-top: 0.25rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .dashboard-card small {
            font-size: 0.65rem !important;
        }

        /* Card em destaque */
        .dashboard-card.highlight {
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            border: 2px solid var(--secondary-color);
            box-shadow: 0 4px 8px rgba(0, 174, 157, 0.2);
            transform: scale(1.02);
        }

        .dashboard-card.highlight h3 {
            color: var(--secondary-color);
            font-weight: 700;
        }

        .dashboard-card.highlight .value {
            color: var(--primary-color);
            font-weight: 700;
            font-size: 1.6rem;
        }

        /* Títulos das seções */
        .section-title {
            color: var(--primary-color);
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--secondary-color);
        }

        /* Responsividade */
        @media (max-width: 1600px) {
            .dashboard-cards.with-highlight {
                grid-template-columns: repeat(3, 1fr);
            }

            .dashboard-cards.with-highlight .dashboard-card:nth-child(4) {
                grid-column: 1;
            }

            .dashboard-cards.with-highlight .dashboard-card:nth-child(5) {
                grid-column: 2 / 4;
            }
        }

        @media (max-width: 1400px) {
            .dashboard-cards {
                grid-template-columns: repeat(4, 1fr);
                gap: 0.5rem;
            }

            .dashboard-cards.with-highlight {
                grid-template-columns: repeat(2, 1fr);
            }

            .dashboard-card {
                padding: 0.75rem;
            }

            .dashboard-card h3 {
                font-size: 0.85rem;
            }

            .dashboard-card .value {
                font-size: 1.2rem;
            }

            .dashboard-card .label {
                font-size: 0.7rem;
            }
        }



        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 1rem;
        }

        .legend {
            padding: 0.5rem;
        }

        .legend-item {
            margin-bottom: 1rem;
        }

        .legend-title {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .legend-formula {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
        }

        .formula-part {
            flex: 1;
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 4px;
            text-align: center;
        }

        .weight {
            display: block;
            font-weight: 600;
            color: var(--secondary-color);
            font-size: 1.1rem;
        }

        .description {
            display: block;
            font-size: 0.8rem;
            color: #666;
        }

        .formula-plus {
            font-weight: bold;
            color: var(--primary-color);
        }

        .legend-example {
            background: #f8f9fa;
            padding: 0.5rem;
            border-radius: 4px;
            margin-bottom: 0.5rem;
        }

        .example-item {
            font-size: 0.85rem;
        }

        .example-pa {
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 0.25rem;
        }

        .example-details {
            color: #666;
            font-size: 0.8rem;
        }

        .example-total {
            margin-top: 0.25rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .legend-note {
            font-size: 0.7rem;
            color: #666;
            font-style: italic;
            text-align: center;
        }

        .user-card {
            background: var(--bg-white);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .user-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }

        .user-header h3 {
            color: var(--primary-color);
            font-size: 1.1rem;
            margin: 0;
        }

        .medals {
            display: flex;
            gap: 0.5rem;
        }

        .medal {
            font-size: 1.2rem;
            cursor: help;
        }

        .user-metrics {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .pas-responsavel {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #eee;
        }

        .pas-periods {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .pas-period {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
            border-left: 3px solid var(--secondary-color);
        }

        .pas-title {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .pas-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 0.75rem;
        }

        .pa-card {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 0.75rem;
            border-left: 3px solid var(--secondary-color);
        }

        .pa-card .pa-name {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
        }

        .pa-data-inicio {
            display: block;
            font-weight: 400;
            color: var(--text-light);
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        .pa-data-inicio i {
            margin-right: 0.25rem;
            color: var(--secondary-color);
        }

        .pa-metrics {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pa-propostas {
            font-size: 0.8rem;
            color: #666;
        }

        .pa-devolucao {
            font-size: 0.8rem;
            font-weight: 600;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            background: #e3f2fd;
            color: #1976d2;
        }

        .pa-devolucao.alta {
            background: #ffebee;
            color: #d32f2f;
        }

        .pa-devolucao.media {
            background: #fff3e0;
            color: #f57c00;
        }

        .no-pas {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .user-actions {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #eee;
            text-align: center;
        }

        .btn-pdf {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: #dc3545;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 0.85rem;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
        }

        .btn-pdf:hover {
            background: #c82333;
            color: white;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        .btn-pdf i {
            font-size: 1rem;
        }

        .metric-period {
            text-align: center;
        }

        .period-title {
            color: var(--secondary-color);
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }

        .metric-value {
            font-size: 0.9rem;
            color: var(--text-dark);
        }

        .metric-value small {
            display: block;
            color: #666;
            font-size: 0.8rem;
        }

        @media (max-width: 768px) {
            .dashboard-cards {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .user-metrics {
                grid-template-columns: 1fr;
                gap: 0.5rem;
            }

            .metric-period {
                padding: 0.5rem;
                background: #f8f9fa;
                border-radius: 4px;
            }

            .pas-periods {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .pas-grid {
                grid-template-columns: 1fr;
            }


        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar-container {
            position: relative;
            width: 48px;
            height: 48px;
        }

        .user-avatar {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--secondary-color);
        }

        .user-avatar-placeholder {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1.2rem;
            border: 2px solid var(--secondary-color);
        }

        .user-details {
            display: flex;
            flex-direction: column;
        }

        .user-details h3 {
            margin: 0;
            font-size: 1.1rem;
            color: var(--primary-color);
        }

        .user-email {
            color: #666;
            font-size: 0.8rem;
        }

        .user-pas-info {
            display: block;
            color: var(--secondary-color);
            font-size: 0.8rem;
            font-weight: 600;
            margin-top: 0.25rem;
        }

        .user-pas-info i {
            margin-right: 0.25rem;
        }

        .user-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }

        @media (max-width: 768px) {
            .user-info {
                gap: 0.5rem;
            }

            .user-avatar-container {
                width: 40px;
                height: 40px;
            }

            .user-avatar-placeholder {
                font-size: 1rem;
            }

            .user-details h3 {
                font-size: 1rem;
            }

            .user-email {
                font-size: 0.7rem;
            }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'components/sidebar.php'; ?>

        <!-- Main Content -->
        <div class="main-content" id="mainContent">
            <!-- Métricas de Prazo de Aprovação -->
            <h4 class="section-title">Métricas de Prazo de Aprovação</h4>
            <div class="dashboard-cards with-highlight">
                <div class="dashboard-card">
                    <h3>Aprovado no Mesmo Dia</h3>
                    <div class="value">
                        <?php echo number_format($aprovacoes_mesmo_dia, 0, ',', '.'); ?>
                        <small style="font-size: 0.6em; display: block; color: #666;">
                            <?php echo $total_aprovacoes > 0 ? round(($aprovacoes_mesmo_dia / $total_aprovacoes) * 100, 1) : 0; ?>% do total
                        </small>
                    </div>
                    <div class="label">Propostas aprovadas em 0 dias</div>
                </div>
                <div class="dashboard-card">
                    <h3>Aprovado em 1 Dia</h3>
                    <div class="value">
                        <?php echo number_format($aprovacoes_1_dia, 0, ',', '.'); ?>
                        <small style="font-size: 0.6em; display: block; color: #666;">
                            <?php echo $total_aprovacoes > 0 ? round(($aprovacoes_1_dia / $total_aprovacoes) * 100, 1) : 0; ?>% do total
                        </small>
                    </div>
                    <div class="label">Propostas aprovadas em 1 dia</div>
                </div>
                <div class="dashboard-card">
                    <h3>Aprovado em 2 Dias</h3>
                    <div class="value">
                        <?php echo number_format($aprovacoes_2_dias, 0, ',', '.'); ?>
                        <small style="font-size: 0.6em; display: block; color: #666;">
                            <?php echo $total_aprovacoes > 0 ? round(($aprovacoes_2_dias / $total_aprovacoes) * 100, 1) : 0; ?>% do total
                        </small>
                    </div>
                    <div class="label">Propostas aprovadas em 2 dias</div>
                </div>
                <div class="dashboard-card">
                    <h3>Aprovado Acima de 2 Dias</h3>
                    <div class="value">
                        <?php echo number_format($aprovacoes_acima_2_dias, 0, ',', '.'); ?>
                        <small style="font-size: 0.6em; display: block; color: #666;">
                            <?php echo $total_aprovacoes > 0 ? round(($aprovacoes_acima_2_dias / $total_aprovacoes) * 100, 1) : 0; ?>% do total
                        </small>
                    </div>
                    <div class="label">Propostas aprovadas em mais de 2 dias</div>
                </div>
                <div class="dashboard-card highlight">
                    <h3>✨ Aprovado até 1 Dia</h3>
                    <div class="value">
                        <?php echo number_format($aprovacoes_ate_1_dia, 0, ',', '.'); ?>
                        <small style="font-size: 0.6em; display: block; color: #666;">
                            <?php echo $total_aprovacoes > 0 ? round(($aprovacoes_ate_1_dia / $total_aprovacoes) * 100, 1) : 0; ?>% do total
                        </small>
                    </div>
                    <div class="label">Agilidade na aprovação (0 + 1 dia)</div>
                </div>
            </div>

            <!-- Dashboard Cards -->
            <h4 class="section-title">Métricas da Semana</h4>
            <div class="dashboard-cards">
                <div class="dashboard-card">
                    <h3>Propostas Analisadas</h3>
                    <div class="value">
                        <?php echo number_format($total_semana, 0, ',', '.'); ?>
                        <small style="font-size: 0.6em; display: block; color: #666;">
                            Total Geral: <?php echo number_format($total_geral, 0, ',', '.'); ?>
                        </small>
                    </div>
                    <div class="label">Semana atual (<?php echo $periodo_semana; ?>)</div>
                </div>
                <div class="dashboard-card">
                    <h3>PA em Destaque</h3>
                    <div class="value">
                        <?php echo $pa_mais_propostas; ?>
                        <small style="font-size: 0.6em; display: block; color: #666;">
                            <?php echo number_format($total_pa, 0, ',', '.'); ?> propostas
                        </small>
                    </div>
                    <div class="label">PA com mais propostas na semana</div>
                </div>
                <div class="dashboard-card">
                    <h3>Taxa de Devolução</h3>
                    <div class="value"><?php echo $taxa_devolucao; ?>%</div>
                    <div class="label">
                        <?php echo number_format($devolvidas, 0, ',', '.'); ?> de <?php echo number_format($total_semana_taxa, 0, ',', '.'); ?> propostas
                    </div>
                </div>
                <div class="dashboard-card">
                    <h3>PA Mais Eficiente</h3>
                    <div class="value">
                        <?php echo $pa_menor_devolucao; ?>
                        <small style="font-size: 0.6em; display: block; color: #666;">
                            <?php echo (100 - $taxa_pa_aprovacao); ?>% de devolução
                            (<?php echo number_format($total_pa_aprovacao, 0, ',', '.'); ?> propostas)
                        </small>
                    </div>
                    <div class="label">PA com menor taxa de devolução</div>
                </div>
            </div>

            <h4 class="section-title">Métricas do Mês</h4>
            <div class="dashboard-cards">
                <div class="dashboard-card">
                    <h3>Propostas Analisadas</h3>
                    <div class="value">
                        <?php echo number_format($total_mes, 0, ',', '.'); ?>
                        <small style="font-size: 0.6em; display: block; color: #666;">
                            Total Geral: <?php echo number_format($total_geral, 0, ',', '.'); ?>
                        </small>
                    </div>
                    <div class="label">Mês atual (<?php echo $periodo_mes; ?>)</div>
                </div>
                <div class="dashboard-card">
                    <h3>PA em Destaque</h3>
                    <div class="value">
                        <?php echo $pa_mais_propostas_mes; ?>
                        <small style="font-size: 0.6em; display: block; color: #666;">
                            <?php echo number_format($total_pa_mes, 0, ',', '.'); ?> propostas
                        </small>
                    </div>
                    <div class="label">PA com mais propostas no mês</div>
                </div>
                <div class="dashboard-card">
                    <h3>Taxa de Devolução</h3>
                    <div class="value"><?php echo $taxa_devolucao_mes; ?>%</div>
                    <div class="label">
                        <?php echo number_format($devolvidas_mes, 0, ',', '.'); ?> de <?php echo number_format($total_mes_taxa, 0, ',', '.'); ?> propostas
                    </div>
                </div>
                <div class="dashboard-card">
                    <h3>PA Mais Eficiente</h3>
                    <div class="value">
                        <?php echo $pa_menor_devolucao_mes; ?>
                        <small style="font-size: 0.6em; display: block; color: #666;">
                            <?php echo (100 - $taxa_pa_aprovacao_mes); ?>% de devolução
                            (<?php echo number_format($total_pa_aprovacao_mes, 0, ',', '.'); ?> propostas)
                        </small>
                    </div>
                    <div class="label">PA com menor taxa de devolução</div>
                </div>
            </div>

            <h4 class="section-title">Métricas do Ano</h4>
            <div class="dashboard-cards">
                <div class="dashboard-card">
                    <h3>Propostas Analisadas</h3>
                    <div class="value">
                        <?php echo number_format($total_ano, 0, ',', '.'); ?>
                        <small style="font-size: 0.6em; display: block; color: #666;">
                            Total Geral: <?php echo number_format($total_geral, 0, ',', '.'); ?>
                        </small>
                    </div>
                    <div class="label">Ano atual (<?php echo $periodo_ano; ?>)</div>
                </div>
                <div class="dashboard-card">
                    <h3>PA em Destaque</h3>
                    <div class="value">
                        <?php echo $pa_mais_propostas_ano; ?>
                        <small style="font-size: 0.6em; display: block; color: #666;">
                            <?php echo number_format($total_pa_ano, 0, ',', '.'); ?> propostas
                        </small>
                    </div>
                    <div class="label">PA com mais propostas no ano</div>
                </div>
                <div class="dashboard-card">
                    <h3>Taxa de Devolução</h3>
                    <div class="value"><?php echo $taxa_devolucao_ano; ?>%</div>
                    <div class="label">
                        <?php echo number_format($devolvidas_ano, 0, ',', '.'); ?> de <?php echo number_format($total_ano_taxa, 0, ',', '.'); ?> propostas
                    </div>
                </div>
                <div class="dashboard-card">
                    <h3>PA Mais Eficiente</h3>
                    <div class="value">
                        <?php echo $pa_menor_devolucao_ano; ?>
                        <small style="font-size: 0.6em; display: block; color: #666;">
                            <?php echo (100 - $taxa_pa_aprovacao_ano); ?>% de devolução
                            (<?php echo number_format($total_pa_aprovacao_ano, 0, ',', '.'); ?> propostas)
                        </small>
                    </div>
                    <div class="label">PA com menor taxa de devolução</div>
                </div>
            </div>



            <h4 class="section-title">Desempenho Individual</h4>
            <?php
            // Buscar apenas usuários com vínculos ativos
            $stmt = $pdo->query("
                SELECT DISTINCT
                    v.usuario_id,
                    u.email,
                    MIN(v.data_inicio) as primeiro_vinculo,
                    COUNT(v.pa_id) as total_pas_ativos
                FROM acd_usuario_pa v
                INNER JOIN usuarios u ON v.usuario_id = u.id
                WHERE v.status IN ('ativo', 1)
                AND (v.data_fim IS NULL OR v.data_fim > CURDATE())
                AND v.usuario_id != '0'
                GROUP BY v.usuario_id, u.email
                ORDER BY u.email
            ");
            $usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);



            // Função para buscar dados do usuário na API
            function getUsuarioAPI($email) {
                $apiFields = [
                    'api_user' => 'UFL7GXZ14LU9NOR',
                    'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
                    'api_module' => 'Usuarios',
                    'api_action' => 'listarUsuarios',
                    'email' => $email
                ];

                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_SSL_VERIFYHOST => false,
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => http_build_query($apiFields),
                ));
                
                $response = curl_exec($curl);
                
                if (curl_errno($curl)) {
                    error_log('Erro na API: ' . curl_error($curl));
                    curl_close($curl);
                    return null;
                }
                
                curl_close($curl);
                
                $usuarios = json_decode($response, true);
                
                if (!is_array($usuarios)) {
                    error_log('Resposta da API inválida: ' . $response);
                    return null;
                }
                
                // Log da resposta completa para debug
                error_log('Resposta da API para email ' . $email . ': ' . print_r($usuarios, true));
                
                // Procurar o usuário específico pelo email
                foreach ($usuarios as $usuario) {
                    if (isset($usuario['email']) && strcasecmp($usuario['email'], $email) === 0) {
                        // Log dos dados do usuário encontrado
                        error_log('Usuário encontrado: ' . print_r($usuario, true));
                        
                        // Tratar a foto como no dashboardtes.php
                        $foto = null;
                        if (!empty($usuario['foto'])) {
                            $foto = 'https://intranet.sicoobcredilivre.com.br/sys/conteudo/usuarios/' . $usuario['foto'];
                        }
                        
                        return [
                            'id' => $usuario['id'] ?? null,
                            'nome' => $usuario['nome'] ?? null,
                            'foto' => $foto,
                            'email' => $usuario['email'] ?? null
                        ];
                    }
                }
                
                error_log('Usuário não encontrado para o email: ' . $email);
                return null;
            }

            // Cache para armazenar dados dos usuários
            $usuariosCache = [];

            foreach ($usuarios as $usuario):
                // Buscar dados do usuário na API (com cache)
                if (!isset($usuariosCache[$usuario['usuario_id']])) {
                    $dadosUsuario = getUsuarioAPI($usuario['email']);
                    $usuariosCache[$usuario['usuario_id']] = $dadosUsuario;
                    // Log dos dados do usuário após cache
                    error_log('Dados do usuário após cache: ' . print_r($dadosUsuario, true));
                }
                $dadosUsuario = $usuariosCache[$usuario['usuario_id']];

                // Métricas da semana
                $stmt = $pdo->query("SELECT
                    COUNT(*) as total_propostas,
                    SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
                    ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as taxa_devolucao
                    FROM acd_formularios
                    WHERE usuario_id = '{$usuario['usuario_id']}'
                    AND YEARWEEK(data_criacao, 1) = YEARWEEK(CURDATE(), 1)");
                $semana = $stmt->fetch(PDO::FETCH_ASSOC);

                // Métricas do mês
                $stmt = $pdo->query("SELECT
                    COUNT(*) as total_propostas,
                    SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
                    ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as taxa_devolucao
                    FROM acd_formularios
                    WHERE usuario_id = '{$usuario['usuario_id']}'
                    AND YEAR(data_criacao) = YEAR(CURDATE())
                    AND MONTH(data_criacao) = MONTH(CURDATE())");
                $mes = $stmt->fetch(PDO::FETCH_ASSOC);

                // Métricas do ano
                $stmt = $pdo->query("SELECT
                    COUNT(*) as total_propostas,
                    SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
                    ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as taxa_devolucao
                    FROM acd_formularios
                    WHERE usuario_id = '{$usuario['usuario_id']}'
                    AND YEAR(data_criacao) = YEAR(CURDATE())");
                $ano = $stmt->fetch(PDO::FETCH_ASSOC);

                // Buscar apenas PAs com vínculos ativos
                $stmt = $pdo->prepare("
                    SELECT
                        v.pa_id,
                        v.data_inicio,
                        v.data_fim,
                        v.status,
                        p.nome as pa_nome,
                        p.numero as pa_numero
                    FROM acd_usuario_pa v
                    INNER JOIN pontos_atendimento p ON v.pa_id = p.id
                    WHERE v.usuario_id = ?
                    AND v.status IN ('ativo', 1)
                    AND (v.data_fim IS NULL OR v.data_fim > CURDATE())
                    ORDER BY v.data_inicio ASC
                ");
                $stmt->execute([$usuario['usuario_id']]);
                $vinculos_ativos = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Lista de PAs ativos para compatibilidade
                $pas_responsavel = array_column($vinculos_ativos, 'pa_id');

                // Buscar métricas dos PAs ativos para semana, mês e ano
                $metricas_pas_semana = [];
                $metricas_pas_mes = [];
                $metricas_pas_ano = [];

                if (!empty($vinculos_ativos)) {
                    foreach ($vinculos_ativos as $vinculo) {
                        $pa_id = $vinculo['pa_id'];
                        $pa_nome = $vinculo['pa_nome'];
                        $data_inicio_vinculo = $vinculo['data_inicio'];
                        $data_fim_vinculo = $vinculo['data_fim'] ?? date('Y-m-d');

                        if ($pa_nome) {
                            // Métricas do PA na semana atual
                            $stmt = $pdo->prepare("SELECT
                                COUNT(*) as total_propostas,
                                SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
                                ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100) as taxa_devolucao
                                FROM acd_formularios
                                WHERE pa = ?
                                AND YEARWEEK(data_criacao, 1) = YEARWEEK(CURDATE(), 1)
                                AND DATE(data_criacao) >= ?");
                            $stmt->execute([$pa_nome, $data_inicio_vinculo]);
                            $metrica_pa_semana = $stmt->fetch(PDO::FETCH_ASSOC);

                            if ($metrica_pa_semana['total_propostas'] > 0) {
                                $metricas_pas_semana[] = [
                                    'pa_id' => $pa_id,
                                    'pa_nome' => $pa_nome,
                                    'total_propostas' => $metrica_pa_semana['total_propostas'],
                                    'devolvidas' => $metrica_pa_semana['devolvidas'],
                                    'taxa_devolucao' => $metrica_pa_semana['taxa_devolucao'],
                                    'data_inicio' => $data_inicio_vinculo
                                ];
                            }

                            // Métricas do PA no mês atual
                            $stmt = $pdo->prepare("SELECT
                                COUNT(*) as total_propostas,
                                SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
                                ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100) as taxa_devolucao
                                FROM acd_formularios
                                WHERE pa = ?
                                AND YEAR(data_criacao) = YEAR(CURDATE())
                                AND MONTH(data_criacao) = MONTH(CURDATE())
                                AND DATE(data_criacao) >= ?");
                            $stmt->execute([$pa_nome, $data_inicio_vinculo]);
                            $metrica_pa_mes = $stmt->fetch(PDO::FETCH_ASSOC);

                            if ($metrica_pa_mes['total_propostas'] > 0) {
                                $metricas_pas_mes[] = [
                                    'pa_id' => $pa_id,
                                    'pa_nome' => $pa_nome,
                                    'total_propostas' => $metrica_pa_mes['total_propostas'],
                                    'devolvidas' => $metrica_pa_mes['devolvidas'],
                                    'taxa_devolucao' => $metrica_pa_mes['taxa_devolucao'],
                                    'data_inicio' => $data_inicio_vinculo
                                ];
                            }

                            // Métricas do PA no ano atual
                            $stmt = $pdo->prepare("SELECT
                                COUNT(*) as total_propostas,
                                SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
                                ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100) as taxa_devolucao
                                FROM acd_formularios
                                WHERE pa = ?
                                AND YEAR(data_criacao) = YEAR(CURDATE())
                                AND DATE(data_criacao) >= ?");
                            $stmt->execute([$pa_nome, $data_inicio_vinculo]);
                            $metrica_pa_ano = $stmt->fetch(PDO::FETCH_ASSOC);

                            if ($metrica_pa_ano['total_propostas'] > 0) {
                                $metricas_pas_ano[] = [
                                    'pa_id' => $pa_id,
                                    'pa_nome' => $pa_nome,
                                    'total_propostas' => $metrica_pa_ano['total_propostas'],
                                    'devolvidas' => $metrica_pa_ano['devolvidas'],
                                    'taxa_devolucao' => $metrica_pa_ano['taxa_devolucao'],
                                    'data_inicio' => $data_inicio_vinculo
                                ];
                            }
                        }
                    }
                }


            ?>
            <div class="user-card">
                <div class="user-header">
                    <div class="user-info">
                        <?php if ($dadosUsuario && !empty($dadosUsuario['foto'])): ?>
                            <div class="user-avatar-container">
                                <img src="<?php echo htmlspecialchars($dadosUsuario['foto']); ?>" 
                                     alt="Foto de <?php echo htmlspecialchars($dadosUsuario['nome'] ?? $usuario['usuario_id']); ?>" 
                                     class="user-avatar"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div class="user-avatar-placeholder" style="display: none;">
                                    <?php echo strtoupper(substr($usuario['usuario_id'], 0, 2)); ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="user-avatar-placeholder">
                                <?php echo strtoupper(substr($usuario['usuario_id'], 0, 2)); ?>
                            </div>
                        <?php endif; ?>
                        <div class="user-details">
                            <h3><?php echo htmlspecialchars($dadosUsuario['nome'] ?? $usuario['usuario_id']); ?></h3>
                            <?php if (!empty($usuario['email'])): ?>
                                <small class="user-email"><?php echo htmlspecialchars($usuario['email']); ?></small>
                            <?php endif; ?>
                            <small class="user-pas-info">
                                <i class="fas fa-building"></i>
                                <?php echo count($vinculos_ativos); ?> PA(s) ativo(s)
                                <?php if (!empty($usuario['primeiro_vinculo'])): ?>
                                    | Desde <?php echo date('m/Y', strtotime($usuario['primeiro_vinculo'])); ?>
                                <?php endif; ?>
                            </small>
                        </div>
                    </div>


                    <!-- Botão para gerar PDF das devoluções -->
                    <?php if (!empty($vinculos_ativos)): ?>
                        <div class="user-actions">
                            <a href="gerar_pdf_devolucoes.php?usuario_id=<?php echo urlencode($usuario['usuario_id']); ?>"
                               class="btn-pdf"
                               target="_blank"
                               title="Gerar PDF das devoluções dos PAs responsáveis">
                                <i class="fas fa-file-pdf"></i>
                                PDF Devoluções
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="user-metrics">
                    <div class="metric-period">
                        <div class="period-title">Semana</div>
                        <div class="metric-value">
                            <?php echo number_format($semana['total_propostas'], 0, ',', '.'); ?> propostas
                            <small>(<?php echo $semana['taxa_devolucao']; ?>% devolução)</small>
                        </div>
                    </div>
                    <div class="metric-period">
                        <div class="period-title">Mês</div>
                        <div class="metric-value">
                            <?php echo number_format($mes['total_propostas'], 0, ',', '.'); ?> propostas
                            <small>(<?php echo $mes['taxa_devolucao']; ?>% devolução)</small>
                        </div>
                    </div>
                    <div class="metric-period">
                        <div class="period-title">Ano</div>
                        <div class="metric-value">
                            <?php echo number_format($ano['total_propostas'], 0, ',', '.'); ?> propostas
                            <small>(<?php echo $ano['taxa_devolucao']; ?>% devolução)</small>
                        </div>
                    </div>
                </div>

                <!-- Seção de PAs Responsáveis -->
                <div class="pas-responsavel">
                    <div class="pas-title">
                        <i class="fas fa-building"></i>
                        PAs Responsáveis
                    </div>

                    <div class="pas-periods">
                        <!-- Semana -->
                        <div class="pas-period">
                            <div class="pas-title">
                                <i class="fas fa-calendar-week"></i>
                                Semana
                            </div>
                            <?php if (!empty($metricas_pas_semana)): ?>
                                <div class="pas-grid">
                                    <?php foreach ($metricas_pas_semana as $pa_metrica): ?>
                                        <?php
                                            $taxa = $pa_metrica['taxa_devolucao'];
                                            $classe_taxa = '';
                                            if ($taxa >= 20) {
                                                $classe_taxa = 'alta';
                                            } elseif ($taxa >= 10) {
                                                $classe_taxa = 'media';
                                            }
                                        ?>
                                        <div class="pa-card">
                                            <div class="pa-name">
                                                <?php echo htmlspecialchars($pa_metrica['pa_nome']); ?>
                                                <small class="pa-data-inicio">
                                                    <i class="fas fa-calendar-check"></i>
                                                    Desde <?php echo date('d/m/Y', strtotime($pa_metrica['data_inicio'])); ?>
                                                </small>
                                            </div>
                                            <div class="pa-metrics">
                                                <div class="pa-propostas">
                                                    <?php echo number_format($pa_metrica['total_propostas'], 0, ',', '.'); ?> propostas
                                                </div>
                                                <div class="pa-devolucao <?php echo $classe_taxa; ?>">
                                                    <?php echo $pa_metrica['taxa_devolucao']; ?>% devol.
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="no-pas">
                                    <i class="fas fa-info-circle"></i>
                                    Sem dados na semana
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Mês -->
                        <div class="pas-period">
                            <div class="pas-title">
                                <i class="fas fa-calendar-alt"></i>
                                Mês
                            </div>
                            <?php if (!empty($metricas_pas_mes)): ?>
                                <div class="pas-grid">
                                    <?php foreach ($metricas_pas_mes as $pa_metrica): ?>
                                        <?php
                                            $taxa = $pa_metrica['taxa_devolucao'];
                                            $classe_taxa = '';
                                            if ($taxa >= 20) {
                                                $classe_taxa = 'alta';
                                            } elseif ($taxa >= 10) {
                                                $classe_taxa = 'media';
                                            }
                                        ?>
                                        <div class="pa-card">
                                            <div class="pa-name">
                                                <?php echo htmlspecialchars($pa_metrica['pa_nome']); ?>
                                                <small class="pa-data-inicio">
                                                    <i class="fas fa-calendar-check"></i>
                                                    Desde <?php echo date('d/m/Y', strtotime($pa_metrica['data_inicio'])); ?>
                                                </small>
                                            </div>
                                            <div class="pa-metrics">
                                                <div class="pa-propostas">
                                                    <?php echo number_format($pa_metrica['total_propostas'], 0, ',', '.'); ?> propostas
                                                </div>
                                                <div class="pa-devolucao <?php echo $classe_taxa; ?>">
                                                    <?php echo $pa_metrica['taxa_devolucao']; ?>% devol.
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="no-pas">
                                    <i class="fas fa-info-circle"></i>
                                    Sem dados no mês
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Ano -->
                        <div class="pas-period">
                            <div class="pas-title">
                                <i class="fas fa-calendar"></i>
                                Ano
                            </div>
                            <?php if (!empty($metricas_pas_ano)): ?>
                                <div class="pas-grid">
                                    <?php foreach ($metricas_pas_ano as $pa_metrica): ?>
                                        <?php
                                            $taxa = $pa_metrica['taxa_devolucao'];
                                            $classe_taxa = '';
                                            if ($taxa >= 20) {
                                                $classe_taxa = 'alta';
                                            } elseif ($taxa >= 10) {
                                                $classe_taxa = 'media';
                                            }
                                        ?>
                                        <div class="pa-card">
                                            <div class="pa-name">
                                                <?php echo htmlspecialchars($pa_metrica['pa_nome']); ?>
                                                <small class="pa-data-inicio">
                                                    <i class="fas fa-calendar-check"></i>
                                                    Desde <?php echo date('d/m/Y', strtotime($pa_metrica['data_inicio'])); ?>
                                                </small>
                                            </div>
                                            <div class="pa-metrics">
                                                <div class="pa-propostas">
                                                    <?php echo number_format($pa_metrica['total_propostas'], 0, ',', '.'); ?> propostas
                                                </div>
                                                <div class="pa-devolucao <?php echo $classe_taxa; ?>">
                                                    <?php echo $pa_metrica['taxa_devolucao']; ?>% devol.
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <div class="no-pas">
                                    <i class="fas fa-info-circle"></i>
                                    Sem dados no ano
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>