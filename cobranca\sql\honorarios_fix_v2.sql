-- Script para corrigir a geração automática de honorários
-- Versão 2.0 - Simplificada e mais segura

-- 1. Verificar se já existe a tabela de status de honorários
CREATE TABLE IF NOT EXISTS `cbp_status_honorarios` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nome` varchar(50) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2. Inserir os status básicos (se não existirem)
INSERT IGNORE INTO `cbp_status_honorarios` (`id`, `nome`) VALUES 
(1, 'PENDENTE'),
(2, 'PAGO');

-- 3. Adicionar campos de controle nas tabelas de alvarás e parcelas se não existirem
-- Primeiro verificar se as colunas já existem
SET @table_name = 'cbp_alvaras';
SET @column_name = 'honorario_id';

SELECT COUNT(*) INTO @exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = @table_name 
AND column_name = @column_name;

SET @query = IF(@exists = 0, 
    CONCAT('ALTER TABLE ', @table_name, ' ADD COLUMN ', @column_name, ' int(11) DEFAULT NULL'),
    'SELECT "Coluna honorario_id já existe em cbp_alvaras"');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Campo honorario_status para alvarás
SET @column_name = 'honorario_status';

SELECT COUNT(*) INTO @exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = @table_name 
AND column_name = @column_name;

SET @query = IF(@exists = 0, 
    CONCAT('ALTER TABLE ', @table_name, ' ADD COLUMN ', @column_name, ' enum("PENDENTE","PAGO") DEFAULT "PENDENTE"'),
    'SELECT "Coluna honorario_status já existe em cbp_alvaras"');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Mesmas verificações para parcelas_acordo
SET @table_name = 'cbp_parcelas_acordo';
SET @column_name = 'honorario_id';

SELECT COUNT(*) INTO @exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = @table_name 
AND column_name = @column_name;

SET @query = IF(@exists = 0, 
    CONCAT('ALTER TABLE ', @table_name, ' ADD COLUMN ', @column_name, ' int(11) DEFAULT NULL'),
    'SELECT "Coluna honorario_id já existe em cbp_parcelas_acordo"');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Campo honorario_status para parcelas
SET @column_name = 'honorario_status';

SELECT COUNT(*) INTO @exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = @table_name 
AND column_name = @column_name;

SET @query = IF(@exists = 0, 
    CONCAT('ALTER TABLE ', @table_name, ' ADD COLUMN ', @column_name, ' enum("PENDENTE","PAGO") DEFAULT "PENDENTE"'),
    'SELECT "Coluna honorario_status já existe em cbp_parcelas_acordo"');

PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. Criar scripts separados para cada tipo de honorário

-- 4.1 Script para processar alvarás
INSERT INTO cbp_honorarios (
    processo_id,
    associado_id,
    advogado_id,
    alvara_id,
    tipo,
    valor_recebido,
    porcentagem_honorario,
    valor_honorario,
    status,
    data_recebimento,
    created_at,
    updated_at
)
SELECT 
    a.processo_id,
    p.associado_id,
    p.advogado_id,
    a.id,
    'ALVARA',
    a.valor,
    COALESCE((SELECT valor FROM cbp_configuracoes WHERE chave = 'porcentagem_padrao_honorarios'), 13.0435),
    a.valor * COALESCE((SELECT valor FROM cbp_configuracoes WHERE chave = 'porcentagem_padrao_honorarios'), 13.0435) / 100,
    'PENDENTE',
    a.data_recebimento,
    NOW(),
    NOW()
FROM 
    cbp_alvaras a
    JOIN cbp_processos_judiciais p ON a.processo_id = p.id
WHERE 
    a.honorario_id IS NULL
    AND NOT EXISTS (SELECT 1 FROM cbp_honorarios h WHERE h.alvara_id = a.id);

-- 4.2 Atualizar alvarás com IDs de honorários
UPDATE cbp_alvaras a
JOIN cbp_honorarios h ON a.id = h.alvara_id
SET a.honorario_id = h.id
WHERE a.honorario_id IS NULL;

-- 4.3 Script para processar parcelas de acordo
INSERT INTO cbp_honorarios (
    processo_id,
    associado_id,
    advogado_id,
    parcela_id,
    acordo_id,
    tipo,
    numero_parcela,
    total_parcelas,
    valor_recebido,
    porcentagem_honorario,
    valor_honorario,
    status,
    data_recebimento,
    created_at,
    updated_at
)
SELECT 
    a.processo_id,
    p.associado_id,
    p.advogado_id,
    pa.id,
    a.id,
    'PARCELA',
    pa.numero_parcela,
    a.quantidade_parcelas,
    LEAST(pa.valor_pago, pa.valor),
    COALESCE((SELECT valor FROM cbp_configuracoes WHERE chave = 'porcentagem_padrao_honorarios'), 13.0435),
    LEAST(pa.valor_pago, pa.valor) * COALESCE((SELECT valor FROM cbp_configuracoes WHERE chave = 'porcentagem_padrao_honorarios'), 13.0435) / 100,
    'PENDENTE',
    pa.data_pagamento,
    NOW(),
    NOW()
FROM 
    cbp_parcelas_acordo pa
    JOIN cbp_acordos a ON pa.acordo_id = a.id
    JOIN cbp_processos_judiciais p ON a.processo_id = p.id
WHERE 
    pa.status = 'PAGO'
    AND pa.honorario_id IS NULL
    AND NOT EXISTS (SELECT 1 FROM cbp_honorarios h WHERE h.parcela_id = pa.id);

-- 4.4 Atualizar parcelas com IDs de honorários
UPDATE cbp_parcelas_acordo pa
JOIN cbp_honorarios h ON pa.id = h.parcela_id
SET pa.honorario_id = h.id
WHERE pa.honorario_id IS NULL;

-- 5. Adicionar índices para melhorar a performance
ALTER TABLE `cbp_honorarios` ADD INDEX `idx_honorarios_tipo` (`tipo`);
ALTER TABLE `cbp_honorarios` ADD INDEX `idx_honorarios_status` (`status`);
ALTER TABLE `cbp_honorarios` ADD INDEX `idx_honorarios_processo` (`processo_id`);
ALTER TABLE `cbp_honorarios` ADD INDEX `idx_honorarios_associado` (`associado_id`);
ALTER TABLE `cbp_honorarios` ADD INDEX `idx_honorarios_advogado` (`advogado_id`);
ALTER TABLE `cbp_honorarios` ADD INDEX `idx_honorarios_data` (`data_recebimento`); 