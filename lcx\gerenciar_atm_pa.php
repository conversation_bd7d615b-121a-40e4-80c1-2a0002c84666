<?php
session_start();
require_once '../config/database.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user_id = $_SESSION['user_id'];

// Buscar dados do usuário
try {
    $stmt = $pdo->prepare("SELECT nome_completo FROM usuarios WHERE id = ?");
    $stmt->execute([$user_id]);
    $usuario = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$usuario) {
        header('Location: ../login.php');
        exit;
    }
} catch (Exception $e) {
    error_log("Erro ao buscar usuário: " . $e->getMessage());
    header('Location: ../login.php');
    exit;
}

// Verificar permissões LCX do usuário
try {
    $stmt = $pdo->prepare("SELECT nivel_permissao FROM lcx_permissoes WHERE usuario_id = ? AND ativo = 1");
    $stmt->execute([$user_id]);
    $permissao_lcx = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$permissao_lcx) {
        header('Location: index.php?erro=sem_permissao_lcx');
        exit;
    }
    
    $nivel_permissao = $permissao_lcx['nivel_permissao'];
    
    // Verificar se tem permissão para gerenciar ATM (admin ou gestor_master)
    if (!in_array($nivel_permissao, ['admin', 'gestor_master'])) {
        header('Location: index.php?erro=sem_permissao_admin');
        exit;
    }
} catch (Exception $e) {
    error_log("Erro ao verificar permissões: " . $e->getMessage());
    header('Location: index.php?erro=erro_sistema');
    exit;
}

// Criar tabela unificada se não existir
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS lcx_atm_atmr (
        id INT AUTO_INCREMENT PRIMARY KEY,
        ponto_atendimento_id INT NOT NULL,
        numero VARCHAR(20) NOT NULL,
        tipo ENUM('ATM', 'ATMR') NOT NULL,
        localizacao VARCHAR(255),
        status ENUM('ativo', 'inativo') DEFAULT 'ativo',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_pa (ponto_atendimento_id),
        UNIQUE KEY unique_pa_numero_tipo (ponto_atendimento_id, numero, tipo)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
} catch (Exception $e) {
    error_log("Erro ao criar tabela: " . $e->getMessage());
}

// Buscar pontos de atendimento com seus equipamentos
try {
    $stmt = $pdo->query("SELECT * FROM pontos_atendimento WHERE ativo = 1 ORDER BY nome");
    $pontos_atendimento = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Buscar equipamentos para cada PA
    foreach ($pontos_atendimento as &$pa) {
        $stmt = $pdo->prepare("
            SELECT * FROM lcx_atm_atmr 
            WHERE ponto_atendimento_id = ? 
            ORDER BY tipo, numero
        ");
        $stmt->execute([$pa['id']]);
        $pa['equipamentos'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (Exception $e) {
    error_log("Erro ao buscar pontos de atendimento: " . $e->getMessage());
    $pontos_atendimento = [];
}

// Buscar todos os PAs para o modal
try {
    $stmt = $pdo->query("SELECT id, nome, numero FROM pontos_atendimento WHERE ativo = 1 ORDER BY nome");
    $todos_pas = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    error_log("Erro ao buscar PAs: " . $e->getMessage());
    $todos_pas = [];
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar ATM/ATMR por PA - Sicoob Livro Caixa</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            /* Paleta de Cores Oficial Sicoob 2024 - Manual de Marca */
            --sicoob-turquesa: #00A091;        /* RGB: 0, 160, 145 - Cor principal */
            --sicoob-verde-escuro: #003641;    /* RGB: 0, 54, 65 - Verde escuro */
            --sicoob-verde-medio: #7DB61C;     /* RGB: 125, 182, 28 - Verde médio */
            --sicoob-verde-claro: #C9D200;     /* RGB: 201, 210, 0 - Verde claro */
            --sicoob-roxo: #49479D;            /* RGB: 73, 71, 157 - Roxo */
            --sicoob-branco: #FFFFFF;          /* RGB: 255, 255, 255 - Branco */

            /* Cores de Sistema baseadas na identidade oficial */
            --primary-color: var(--sicoob-turquesa);
            --secondary-color: var(--sicoob-verde-escuro);
            --accent-color: var(--sicoob-verde-medio);
            --accent-light: var(--sicoob-verde-claro);
            --accent-purple: var(--sicoob-roxo);
            --success-color: var(--sicoob-verde-medio);
            --warning-color: var(--sicoob-verde-claro);
            --danger-color: #D32F2F;
            --info-color: var(--sicoob-turquesa);
            --dark-color: var(--sicoob-verde-escuro);
            --light-color: #F8FFFE;
            --white-color: var(--sicoob-branco);
            --gray-color: #6B7280;
        }

        /* Layout e Estrutura - Identidade Visual Sicoob Oficial */
        body {
            background: linear-gradient(135deg, var(--light-color) 0%, rgba(0, 160, 145, 0.03) 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            position: relative;
        }

        /* Elemento gráfico sutil de fundo inspirado nos padrões oficiais Sicoob */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            right: 0;
            width: 300px;
            height: 100vh;
            background: linear-gradient(60deg,
                transparent 0%,
                rgba(0, 160, 145, 0.02) 20%,
                rgba(125, 182, 28, 0.015) 40%,
                rgba(201, 210, 0, 0.01) 60%,
                rgba(73, 71, 157, 0.005) 80%,
                transparent 100%);
            z-index: -1;
            opacity: 0.7;
        }

        /* Navbar - Identidade Visual Oficial Sicoob */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 4px 20px rgba(0, 54, 65, 0.15);
            border-bottom: 3px solid var(--accent-color);
            position: relative;
        }

        /* Padrão gráfico inspirado nos grafismos oficiais Sicoob */
        .navbar::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 100%;
            background: linear-gradient(60deg,
                transparent 30%,
                rgba(125, 182, 28, 0.12) 45%,
                rgba(201, 210, 0, 0.08) 55%,
                rgba(73, 71, 157, 0.05) 70%,
                transparent 85%);
            opacity: 0.8;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .navbar-brand .accent-text {
            color: var(--accent-color);
            font-weight: 300;
        }

        /* Cards - Padrão Oficial Sicoob */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 3px 12px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: none;
            overflow: hidden;
            margin-bottom: 20px;
            position: relative;
        }

        /* Elemento gráfico inspirado nos padrões oficiais - apenas para card principal */
        .card:not(.pa-card)::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg,
                var(--sicoob-turquesa) 0%,
                var(--sicoob-verde-medio) 33%,
                var(--sicoob-verde-claro) 66%,
                var(--sicoob-roxo) 100%);
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.12);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: var(--white-color);
            border-radius: 12px 12px 0 0 !important;
            border: none;
            padding: 15px 20px;
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }

        /* Grafismo sutil no header dos cards principais - Padrão Sicoob */
        .card:not(.pa-card) .card-header::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100%;
            background: linear-gradient(60deg,
                transparent 20%,
                rgba(255,255,255,0.12) 40%,
                rgba(255,255,255,0.08) 60%,
                transparent 80%);
        }

        .card-header h5 {
            margin: 0;
            font-size: 1.1rem;
            font-weight: 600;
        }

        /* Botões - Padrão Oficial Sicoob */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 160, 145, 0.3);
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
        }

        /* Tabelas */
        .table {
            margin: 0;
        }

        .table thead th {
            background: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
            padding: 12px 15px;
        }

        .table tbody td {
            padding: 12px 15px;
            vertical-align: middle;
            border-color: #e9ecef;
        }

        .table tbody tr:hover {
            background-color: rgba(0, 160, 145, 0.05);
        }

        /* Cards de PA */
        .pa-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            transition: all 0.2s ease;
            overflow: hidden;
            position: relative;
        }

        /* Remover pseudo-elementos dos cards de PA */
        .pa-card::before,
        .pa-card::after {
            display: none !important;
        }

        .pa-card .card-header::before,
        .pa-card .card-header::after {
            display: none !important;
        }

        .pa-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 160, 145, 0.1);
        }

        .pa-card .card-header {
            background: #f8f9fa !important;
            border-bottom: 1px solid #e0e0e0;
            padding: 12px 15px;
            position: relative;
        }

        .pa-card .card-body {
            max-height: 300px;
            overflow-y: auto;
        }

        /* Equipamentos nos cards */
        .equipamento-card {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 8px 10px;
            border-left: 3px solid var(--primary-color);
            transition: all 0.2s ease;
        }

        .equipamento-card:hover {
            background: #e9ecef;
            transform: translateX(2px);
        }

        .equipamento-card:last-child {
            margin-bottom: 0 !important;
        }

        /* Badges menores */
        .badge-sm {
            font-size: 0.65rem;
            padding: 0.25em 0.4em;
        }

        /* Filtros */
        .input-group-text {
            background-color: #f8f9fa;
            border-color: #e0e0e0;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 160, 145, 0.25);
        }

        .form-control.border-start-0 {
            border-left: none !important;
        }

        .form-control.border-start-0:focus {
            border-left: none !important;
            box-shadow: none;
        }

        .input-group:focus-within .input-group-text {
            border-color: var(--primary-color);
        }

        .input-group:focus-within .form-control {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 160, 145, 0.25);
        }

        /* Badges */
        .badge {
            font-size: 0.75rem;
            padding: 0.4em 0.6em;
            font-weight: 600;
        }

        .badge-atm {
            background-color: var(--primary-color);
        }

        .badge-atmr {
            background-color: var(--accent-color);
        }

        /* Botões secundários */
        .btn-outline-primary {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-success {
            border-color: var(--accent-color);
            color: var(--accent-color);
        }

        .btn-outline-success:hover {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }

        .btn-outline-danger:hover {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }

        /* Botões outline com cores oficiais Sicoob */
        .btn-outline-primary:hover,
        .btn-outline-primary:focus,
        .btn-outline-primary.active {
            color: white;
            transform: translateY(-1px);
        }

        .btn-outline-success:hover,
        .btn-outline-success:focus,
        .btn-outline-success.active {
            color: white;
            transform: translateY(-1px);
        }

        .btn-outline-danger {
            color: var(--danger-color);
            border-color: var(--danger-color);
        }

        .btn-outline-danger:hover,
        .btn-outline-danger:focus,
        .btn-outline-danger.active {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
            color: white;
            transform: translateY(-1px);
        }

        /* Alertas */
        .alert {
            border-radius: 8px;
            border: none;
            padding: 15px 20px;
        }

        .alert-success {
            background-color: rgba(0, 160, 145, 0.1);
            color: var(--secondary-color);
            border-left: 4px solid var(--primary-color);
        }

        .alert-danger {
            background-color: rgba(211, 47, 47, 0.1);
            color: var(--danger-color);
            border-left: 4px solid var(--danger-color);
        }

        /* Modal */
        .modal-header {
            background: var(--primary-color);
            color: white;
            border-bottom: none;
        }

        .modal-header .btn-close {
            filter: invert(1);
        }

        /* Formulários */
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 160, 145, 0.25);
        }

        /* Garantir que botões sejam clicáveis */
        .btn {
            position: relative;
            z-index: 10;
            pointer-events: auto;
        }

        .btn-outline-success {
            cursor: pointer !important;
            pointer-events: auto !important;
        }

        /* Garantir que elementos não bloqueiem cliques */
        .pa-card .card-header {
            position: relative;
            z-index: 1;
        }

        .pa-card .card-header .btn {
            position: relative;
            z-index: 20;
        }

        /* Cores de texto oficiais Sicoob */
        .text-primary {
            color: var(--sicoob-turquesa) !important;
        }

        .text-success {
            color: var(--sicoob-verde-medio) !important;
        }

        .text-info {
            color: var(--sicoob-turquesa) !important;
        }

        .text-warning {
            color: var(--sicoob-verde-claro) !important;
        }

        .text-danger {
            color: var(--danger-color) !important;
        }

        /* Badges com cores oficiais Sicoob */
        .bg-success {
            background-color: var(--sicoob-verde-medio) !important;
        }

        .bg-info {
            background-color: var(--sicoob-turquesa) !important;
        }

        .bg-warning {
            background-color: var(--sicoob-verde-claro) !important;
            color: var(--sicoob-verde-escuro) !important;
        }

        .bg-primary {
            background-color: var(--sicoob-turquesa) !important;
        }

        /* Correção para evitar duplicação visual */
        .row.g-3 {
            overflow: hidden;
            display: flex;
            flex-wrap: wrap;
        }

        .col-lg-6.col-xl-4 {
            position: relative;
            z-index: 1;
            max-width: 100%;
        }

        /* Evitar duplicação por overflow ou float */
        .pa-card {
            clear: both;
            float: none;
            position: relative;
        }

        /* Garantir que não há elementos duplicados */
        .pa-card {
            display: block !important;
            visibility: visible !important;
        }

        /* Forçar que cada coluna apareça apenas uma vez */
        .col-lg-6.col-xl-4 {
            flex: 0 0 auto;
            width: 33.333333%;
        }

        @media (max-width: 1199.98px) {
            .col-lg-6.col-xl-4 {
                width: 50%;
            }
        }

        @media (max-width: 991.98px) {
            .col-lg-6.col-xl-4 {
                width: 100%;
            }
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .container-fluid {
                padding: 15px;
            }

            .card-header {
                padding: 12px 15px;
            }

            .table {
                font-size: 0.9rem;
            }

            .equipamento-item {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-credit-card me-2" style="color: var(--accent-color);"></i>
                <span>Sicoob</span>
                <span class="accent-text"> ATM/ATMR</span>
            </a>
            
            <div class="navbar-nav ms-auto">
                <a href="index.php" class="btn btn-outline-light btn-sm">
                    <i class="fas fa-arrow-left me-2"></i>Voltar ao Dashboard
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">

        <!-- Mensagens de feedback -->
        <?php if (isset($_GET['sucesso'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php
                switch ($_GET['sucesso']) {
                    case 'equipamento_cadastrado':
                        echo 'Equipamento cadastrado com sucesso!';
                        break;
                    case 'equipamento_atualizado':
                        echo 'Equipamento atualizado com sucesso!';
                        break;
                    case 'equipamento_excluido':
                        echo 'Equipamento excluído com sucesso!';
                        break;
                    default:
                        echo 'Operação realizada com sucesso!';
                }
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['erro'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php
                switch ($_GET['erro']) {
                    case 'sem_permissao_admin':
                        echo 'Você não tem permissão para gerenciar ATM/ATMR.';
                        break;
                    case 'dados_obrigatorios':
                        echo 'Todos os campos obrigatórios devem ser preenchidos.';
                        break;
                    case 'pa_nao_encontrado':
                        echo 'Ponto de atendimento não encontrado.';
                        break;
                    case 'equipamento_ja_existe':
                        $numero = $_GET['numero'] ?? 'o número informado';
                        $tipo = $_GET['tipo'] ?? 'equipamento';
                        echo "Já existe um {$tipo} com o número \"{$numero}\" neste ponto de atendimento.";
                        break;
                    case 'erro_sistema':
                        echo 'Erro interno do sistema. Contate o administrador.';
                        break;
                    default:
                        echo 'Ocorreu um erro inesperado.';
                }
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Filtros -->
        <div class="card mb-3">
            <div class="card-body py-3">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="fas fa-search text-muted"></i>
                            </span>
                            <input type="text" class="form-control border-start-0" id="filtroEquipamentos"
                                   placeholder="Buscar por PA, equipamento ou localização...">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filtroTipo">
                            <option value="">Todos os tipos</option>
                            <option value="ATM">Apenas ATM</option>
                            <option value="ATMR">Apenas ATMR</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="filtroStatus">
                            <option value="">Todos os status</option>
                            <option value="ativo">Apenas Ativos</option>
                            <option value="inativo">Apenas Inativos</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lista de Pontos de Atendimento -->
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-building me-2"></i>Equipamentos ATM/ATMR por Ponto de Atendimento
                        <span class="badge bg-secondary ms-2" id="contadorResultados"><?php echo count($pontos_atendimento); ?> PAs</span>
                    </h5>
                    <button class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#modalNovoEquipamento">
                        <i class="fas fa-plus me-1"></i>Novo Equipamento
                    </button>
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($pontos_atendimento)): ?>
                    <!-- Debug: Total de PAs = <?php echo count($pontos_atendimento); ?> -->
                    <?php
                    // Debug: verificar se há duplicatas nos dados
                    $nomes_debug = array_column($pontos_atendimento, 'nome');
                    $duplicatas_debug = array_diff_assoc($nomes_debug, array_unique($nomes_debug));
                    if (!empty($duplicatas_debug)) {
                        echo "<!-- ATENÇÃO: Duplicatas encontradas nos dados: " . implode(', ', $duplicatas_debug) . " -->";
                    }
                    ?>
                    <div class="row g-3">
                        <?php foreach ($pontos_atendimento as $index => $pa): ?>
                            <div class="col-lg-6 col-xl-4" data-pa-index="<?php echo $index; ?>">
                                <div class="card h-100 pa-card" data-pa-id="<?php echo $pa['id']; ?>" data-pa-nome="<?php echo htmlspecialchars($pa['nome']); ?>">
                                    <!-- Header do PA -->
                                    <div class="card-header bg-light">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0 fw-bold text-dark"><?php echo htmlspecialchars($pa['nome']); ?></h6>
                                                <?php if ($pa['numero']): ?>
                                                    <small class="text-muted">PA: <?php echo htmlspecialchars($pa['numero']); ?></small>
                                                <?php endif; ?>
                                            </div>
                                            <div class="d-flex align-items-center gap-2">
                                                <span class="badge bg-primary"><?php echo count($pa['equipamentos']); ?> equip.</span>
                                                <button class="btn btn-outline-success btn-sm" onclick="novoEquipamento(<?php echo $pa['id']; ?>)" title="Adicionar equipamento">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Lista de Equipamentos -->
                                    <div class="card-body p-2">
                                        <?php if (!empty($pa['equipamentos'])): ?>
                                            <?php foreach ($pa['equipamentos'] as $equipamento): ?>
                                                <div class="equipamento-card mb-2">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div class="flex-grow-1">
                                                            <div class="d-flex align-items-center gap-2 mb-1">
                                                                <span class="badge badge-<?php echo strtolower($equipamento['tipo']); ?>">
                                                                    <?php echo $equipamento['tipo']; ?>
                                                                </span>
                                                                <span class="fw-bold"><?php echo htmlspecialchars($equipamento['numero']); ?></span>
                                                                <span class="badge <?php echo $equipamento['status'] == 'ativo' ? 'bg-success' : 'bg-secondary'; ?> badge-sm">
                                                                    <?php echo $equipamento['status'] == 'ativo' ? 'Ativo' : 'Inativo'; ?>
                                                                </span>
                                                            </div>
                                                            <?php if ($equipamento['localizacao']): ?>
                                                                <small class="text-muted d-block"><?php echo htmlspecialchars($equipamento['localizacao']); ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="btn-group btn-group-sm">
                                                            <button class="btn btn-outline-primary btn-sm" onclick="editarEquipamento(<?php echo $equipamento['id']; ?>, '<?php echo htmlspecialchars($equipamento['numero'], ENT_QUOTES); ?>', '<?php echo $equipamento['tipo']; ?>', '<?php echo htmlspecialchars($equipamento['localizacao'], ENT_QUOTES); ?>', '<?php echo $equipamento['status']; ?>', <?php echo $pa['id']; ?>)" title="Editar">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="btn btn-outline-danger btn-sm" onclick="excluirEquipamento(<?php echo $equipamento['id']; ?>, '<?php echo $equipamento['tipo']; ?> <?php echo htmlspecialchars($equipamento['numero'], ENT_QUOTES); ?>')" title="Excluir">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <div class="text-center py-3">
                                                <i class="fas fa-credit-card fa-2x text-muted mb-2"></i>
                                                <p class="text-muted mb-0 small">Nenhum equipamento cadastrado</p>
                                                <button class="btn btn-outline-primary btn-sm mt-2" onclick="novoEquipamento(<?php echo $pa['id']; ?>)">
                                                    <i class="fas fa-plus me-1"></i>Adicionar Primeiro
                                                </button>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-building fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Nenhum ponto de atendimento encontrado</h5>
                        <p class="text-muted">Não há pontos de atendimento cadastrados no sistema.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Modal Novo/Editar Equipamento -->
    <div class="modal fade" id="modalNovoEquipamento" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-credit-card me-2"></i>Cadastrar Equipamento
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form id="formEquipamento" method="POST" action="processar_equipamento.php">
                    <div class="modal-body">
                        <input type="hidden" name="acao" value="cadastrar">
                        <input type="hidden" id="equipamento_id" name="equipamento_id">

                        <div class="mb-3">
                            <label for="ponto_atendimento_id" class="form-label">Ponto de Atendimento</label>
                            <select class="form-select" id="ponto_atendimento_id" name="ponto_atendimento_id" required>
                                <option value="">Selecione o ponto de atendimento...</option>
                                <?php foreach ($todos_pas as $pa): ?>
                                    <option value="<?php echo $pa['id']; ?>">
                                        <?php echo htmlspecialchars($pa['nome']); ?>
                                        <?php if ($pa['numero']): ?>
                                            (PA: <?php echo htmlspecialchars($pa['numero']); ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="tipo" class="form-label">Tipo de Equipamento</label>
                            <select class="form-select" id="tipo" name="tipo" required>
                                <option value="">Selecione o tipo...</option>
                                <option value="ATM">ATM</option>
                                <option value="ATMR">ATMR</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="numero" class="form-label">Número do Equipamento</label>
                            <input type="text" class="form-control" id="numero" name="numero" required
                                   placeholder="Ex: 001, 002, 305, etc.">
                        </div>

                        <div class="mb-3">
                            <label for="localizacao" class="form-label">Localização</label>
                            <input type="text" class="form-control" id="localizacao" name="localizacao"
                                   placeholder="Ex: Hall principal, Área externa, etc.">
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="ativo">Ativo</option>
                                <option value="inativo">Inativo</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Cadastrar Equipamento
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Função para abrir modal de novo equipamento
        function novoEquipamento(paId) {
            console.log('novoEquipamento chamado com paId:', paId);

            document.getElementById('equipamento_id').value = '';
            document.getElementById('ponto_atendimento_id').value = paId;
            document.getElementById('ponto_atendimento_id').disabled = true;
            document.getElementById('tipo').value = '';
            document.getElementById('numero').value = '';
            document.getElementById('localizacao').value = '';
            document.getElementById('status').value = 'ativo';

            // Criar campo hidden para garantir que o PA seja enviado
            let hiddenPaField = document.getElementById('hidden_pa_id');
            if (!hiddenPaField) {
                hiddenPaField = document.createElement('input');
                hiddenPaField.type = 'hidden';
                hiddenPaField.id = 'hidden_pa_id';
                hiddenPaField.name = 'ponto_atendimento_id_hidden';
                document.getElementById('formEquipamento').appendChild(hiddenPaField);
            }
            hiddenPaField.value = paId;

            // Alterar título e ação
            document.querySelector('#modalNovoEquipamento .modal-title').innerHTML = '<i class="fas fa-credit-card me-2"></i>Cadastrar Equipamento';
            document.querySelector('#formEquipamento input[name="acao"]').value = 'cadastrar';
            document.querySelector('#formEquipamento button[type="submit"]').innerHTML = '<i class="fas fa-save me-2"></i>Cadastrar Equipamento';

            const modal = new bootstrap.Modal(document.getElementById('modalNovoEquipamento'));
            modal.show();
        }

        // Função para editar equipamento
        function editarEquipamento(id, numero, tipo, localizacao, status, paId) {
            document.getElementById('equipamento_id').value = id;
            document.getElementById('ponto_atendimento_id').value = paId;
            document.getElementById('ponto_atendimento_id').disabled = true;
            document.getElementById('tipo').value = tipo;
            document.getElementById('numero').value = numero;
            document.getElementById('localizacao').value = localizacao;
            document.getElementById('status').value = status;

            // Criar campo hidden para garantir que o PA seja enviado
            let hiddenPaField = document.getElementById('hidden_pa_id');
            if (!hiddenPaField) {
                hiddenPaField = document.createElement('input');
                hiddenPaField.type = 'hidden';
                hiddenPaField.id = 'hidden_pa_id';
                hiddenPaField.name = 'ponto_atendimento_id_hidden';
                document.getElementById('formEquipamento').appendChild(hiddenPaField);
            }
            hiddenPaField.value = paId;

            // Alterar título e ação
            document.querySelector('#modalNovoEquipamento .modal-title').innerHTML = '<i class="fas fa-edit me-2"></i>Editar Equipamento';
            document.querySelector('#formEquipamento input[name="acao"]').value = 'editar';
            document.querySelector('#formEquipamento button[type="submit"]').innerHTML = '<i class="fas fa-save me-2"></i>Atualizar Equipamento';

            const modal = new bootstrap.Modal(document.getElementById('modalNovoEquipamento'));
            modal.show();
        }

        // Função para excluir equipamento
        function excluirEquipamento(id, nome) {
            if (confirm(`Tem certeza que deseja excluir o equipamento ${nome}?\n\nEsta ação não pode ser desfeita.`)) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'processar_equipamento.php';

                const acaoInput = document.createElement('input');
                acaoInput.type = 'hidden';
                acaoInput.name = 'acao';
                acaoInput.value = 'excluir';

                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'equipamento_id';
                idInput.value = id;

                form.appendChild(acaoInput);
                form.appendChild(idInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Limpar modal ao fechar
        document.getElementById('modalNovoEquipamento').addEventListener('hidden.bs.modal', function() {
            document.getElementById('ponto_atendimento_id').disabled = false;
        });

        // Função de filtro
        function filtrarEquipamentos() {
            const filtroTexto = document.getElementById('filtroEquipamentos').value.toLowerCase();
            const filtroTipo = document.getElementById('filtroTipo').value;
            const filtroStatus = document.getElementById('filtroStatus').value;

            console.log('Filtros ativos:', { filtroTexto, filtroTipo, filtroStatus });

            const cards = document.querySelectorAll('.pa-card');
            let pasVisiveis = 0;
            let equipamentosVisiveis = 0;

            cards.forEach((card, index) => {
                const paCard = card.closest('.col-lg-6, .col-xl-4') || card;
                const nomePA = card.querySelector('h6')?.textContent.toLowerCase() || '';
                const numeroPA = card.querySelector('small')?.textContent.toLowerCase() || '';

                console.log(`PA ${index}: ${nomePA} (${numeroPA})`);

                // Buscar equipamentos dentro do card
                const equipamentos = card.querySelectorAll('.equipamento-card');
                let paTemEquipamentoVisivel = false;
                let equipamentosNoPA = 0;

                equipamentos.forEach(equipamento => {
                    const tipo = equipamento.querySelector('.badge')?.textContent || '';
                    const numero = equipamento.querySelector('.fw-bold')?.textContent || '';
                    const status = equipamento.querySelector('.bg-success, .bg-secondary')?.textContent.toLowerCase() || '';
                    const localizacao = equipamento.querySelector('small')?.textContent.toLowerCase() || '';

                    // Verificar filtros
                    const passaTexto = !filtroTexto ||
                                      nomePA.includes(filtroTexto) ||
                                      numeroPA.includes(filtroTexto) ||
                                      numero.toLowerCase().includes(filtroTexto) ||
                                      tipo.toLowerCase().includes(filtroTexto) ||
                                      localizacao.includes(filtroTexto);

                    const passaTipo = !filtroTipo || tipo === filtroTipo;
                    const passaStatus = !filtroStatus || status.includes(filtroStatus);

                    if (passaTexto && passaTipo && passaStatus) {
                        equipamento.style.display = '';
                        paTemEquipamentoVisivel = true;
                        equipamentosNoPA++;
                    } else {
                        equipamento.style.display = 'none';
                    }
                });

                // Verificar se PA deve ser visível
                const paPassaTexto = !filtroTexto || nomePA.includes(filtroTexto) || numeroPA.includes(filtroTexto);
                const paTemEquipamentos = equipamentos.length > 0;

                // Lógica corrigida: mostrar PA se:
                // 1. Passa no filtro de texto E não tem equipamentos (mostrar PAs vazios)
                // 2. Tem equipamentos visíveis após filtros
                // 3. Passa no filtro de texto E não há filtros de tipo/status ativos
                const semFiltrosTipoStatus = !filtroTipo && !filtroStatus;
                const deveExibir = (paPassaTexto && !paTemEquipamentos) ||
                                  (paTemEquipamentoVisivel && paTemEquipamentos) ||
                                  (paPassaTexto && paTemEquipamentos && semFiltrosTipoStatus);

                if (deveExibir) {
                    paCard.style.display = '';
                    pasVisiveis++;
                    equipamentosVisiveis += equipamentosNoPA;

                    // Atualizar badge do PA
                    const badge = card.querySelector('.badge.bg-primary');
                    if (badge) {
                        if (paTemEquipamentos) {
                            badge.textContent = equipamentosNoPA + ' equip.';
                        } else {
                            badge.textContent = '0 equip.';
                        }
                    }
                } else {
                    paCard.style.display = 'none';
                }
            });

            // Atualizar contador
            const contador = document.getElementById('contadorResultados');
            if (contador) {
                contador.textContent = `${pasVisiveis} PAs, ${equipamentosVisiveis} equipamentos`;
            }

            // Mostrar mensagem se não houver resultados
            mostrarMensagemSemResultados(pasVisiveis === 0);
        }

        function mostrarMensagemSemResultados(mostrar) {
            let mensagem = document.getElementById('mensagemSemResultados');

            if (mostrar && !mensagem) {
                mensagem = document.createElement('div');
                mensagem.id = 'mensagemSemResultados';
                mensagem.className = 'text-center py-5';
                mensagem.innerHTML = `
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Nenhum resultado encontrado</h5>
                    <p class="text-muted">Tente ajustar os filtros ou buscar por outros termos.</p>
                    <button class="btn btn-outline-primary" onclick="limparFiltros()">
                        <i class="fas fa-times me-1"></i>Limpar Filtros
                    </button>
                `;

                const container = document.querySelector('.card-body .row') || document.querySelector('.card-body');
                container.appendChild(mensagem);
            } else if (!mostrar && mensagem) {
                mensagem.remove();
            }
        }

        function limparFiltros() {
            document.getElementById('filtroEquipamentos').value = '';
            document.getElementById('filtroTipo').value = '';
            document.getElementById('filtroStatus').value = '';
            filtrarEquipamentos();
        }

        // Event listeners para filtros
        document.addEventListener('DOMContentLoaded', function() {
            // Debug: verificar quantos cards existem
            const cards = document.querySelectorAll('.pa-card');
            console.log('Total de cards PA encontrados:', cards.length);

            // Verificar se há duplicatas por nome
            const nomes = [];
            cards.forEach((card, index) => {
                const nome = card.querySelector('h6')?.textContent || 'Sem nome';
                const numero = card.querySelector('small')?.textContent || 'Sem número';
                console.log(`Card ${index}: ${nome} - ${numero}`);
                nomes.push(nome);
            });

            // Verificar duplicatas
            const duplicatas = nomes.filter((nome, index) => nomes.indexOf(nome) !== index);
            if (duplicatas.length > 0) {
                console.warn('DUPLICATAS ENCONTRADAS:', duplicatas);
            }

            // Garantir que todos os PAs sejam visíveis inicialmente
            cards.forEach(function(card) {
                const paCard = card.closest('.col-lg-6, .col-xl-4') || card;
                paCard.style.display = '';
            });

            document.getElementById('filtroEquipamentos').addEventListener('input', filtrarEquipamentos);
            document.getElementById('filtroTipo').addEventListener('change', filtrarEquipamentos);
            document.getElementById('filtroStatus').addEventListener('change', filtrarEquipamentos);

            // Adicionar event listeners para botões de adicionar equipamento
            document.querySelectorAll('.btn-outline-success').forEach(function(btn) {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('Botão clicado via event listener');

                    // Extrair PA ID do onclick
                    const onclickAttr = this.getAttribute('onclick');
                    if (onclickAttr) {
                        const match = onclickAttr.match(/novoEquipamento\((\d+)\)/);
                        if (match) {
                            const paId = match[1];
                            console.log('PA ID extraído:', paId);
                            novoEquipamento(paId);
                        }
                    }
                });
            });
        });

        // Validação antes do submit
        document.getElementById('formEquipamento').addEventListener('submit', function(e) {
            const paId = document.getElementById('ponto_atendimento_id').value;
            const tipo = document.getElementById('tipo').value;
            const numero = document.getElementById('numero').value;

            if (!paId) {
                e.preventDefault();
                alert('Por favor, selecione um ponto de atendimento.');
                return false;
            }

            if (!tipo) {
                e.preventDefault();
                alert('Por favor, selecione o tipo de equipamento.');
                return false;
            }

            if (!numero) {
                e.preventDefault();
                alert('Por favor, informe o número do equipamento.');
                return false;
            }
        });

        // Abrir modal geral (botão do cabeçalho)
        document.querySelector('[data-bs-target="#modalNovoEquipamento"]').addEventListener('click', function() {
            document.getElementById('equipamento_id').value = '';
            document.getElementById('ponto_atendimento_id').value = '';
            document.getElementById('ponto_atendimento_id').disabled = false;
            document.getElementById('tipo').value = '';
            document.getElementById('numero').value = '';
            document.getElementById('localizacao').value = '';
            document.getElementById('status').value = 'ativo';

            // Limpar campo hidden se existir
            const hiddenPaField = document.getElementById('hidden_pa_id');
            if (hiddenPaField) {
                hiddenPaField.value = '';
            }

            // Alterar título e ação
            document.querySelector('#modalNovoEquipamento .modal-title').innerHTML = '<i class="fas fa-credit-card me-2"></i>Cadastrar Equipamento';
            document.querySelector('#formEquipamento input[name="acao"]').value = 'cadastrar';
            document.querySelector('#formEquipamento button[type="submit"]').innerHTML = '<i class="fas fa-save me-2"></i>Cadastrar Equipamento';
        });
    </script>
</body>
</html>
