<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

try {
    // Validar dados recebidos
    if (empty($_POST['processo_id']) || empty($_POST['contrato_id'])) {
        throw new Exception('Dados incompletos');
    }

    // Remover vínculo
    $stmt = $pdo->prepare("
        DELETE FROM cbp_processos_contratos 
        WHERE processo_id = ? AND contrato_id = ?
    ");
    
    $stmt->execute([
        $_POST['processo_id'],
        $_POST['contrato_id']
    ]);

    if ($stmt->rowCount() === 0) {
        throw new Exception('Vínculo não encontrado');
    }

    echo json_encode([
        'success' => true,
        'message' => 'Vínculo removido com sucesso'
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao remover vínculo: ' . $e->getMessage()
    ]);
} 