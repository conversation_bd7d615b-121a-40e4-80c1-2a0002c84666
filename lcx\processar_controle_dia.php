<?php
session_start();
require_once '../config/database.php';
require_once 'funcoes_dias_uteis.php';
require_once 'functions/logs.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$user_id = $_SESSION['user_id'];

// Verificar se é uma requisição POST válida
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: index.php?erro=metodo_invalido');
    exit;
}

$acao = $_POST['acao'] ?? '';
$livro_id = intval($_POST['livro_id'] ?? 0);

if (!$acao || !$livro_id) {
    header('Location: index.php?erro=parametros_invalidos');
    exit;
}

// Verificar permissões LCX do usuário
try {
    $stmt = $pdo->prepare("SELECT nivel_permissao FROM lcx_permissoes WHERE usuario_id = ? AND ativo = 1");
    $stmt->execute([$user_id]);
    $permissao_lcx = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$permissao_lcx) {
        header('Location: index.php?erro=sem_permissao_lcx');
        exit;
    }
    
    $nivel_permissao = $permissao_lcx['nivel_permissao'];
    
    // Verificar se tem permissão para controlar dias
    if (!in_array($nivel_permissao, ['tesoureiro', 'gestor', 'gestor_master', 'admin'])) {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=sem_permissao_controle_dias');
        exit;
    }

    // TODOS os usuários (incluindo gestores e admins) devem ser tesoureiros do PA específico
    // Buscar PA do livro
    $stmt = $pdo->prepare("SELECT ponto_atendimento_id FROM lcx_livros_caixa WHERE id = ?");
    $stmt->execute([$livro_id]);
    $livro_pa = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$livro_pa) {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=livro_nao_encontrado');
        exit;
    }

    // Verificar se é tesoureiro deste PA (obrigatório para todos os níveis)
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM lcx_tesoureiros_pa
        WHERE usuario_id = ? AND ponto_atendimento_id = ? AND ativo = 1
    ");
    $stmt->execute([$user_id, $livro_pa['ponto_atendimento_id']]);
    $eh_tesoureiro_pa = $stmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;

    if (!$eh_tesoureiro_pa) {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=sem_permissao_pa');
        exit;
    }
} catch (Exception $e) {
    error_log("Erro ao verificar permissões: " . $e->getMessage());
    header('Location: index.php?erro=erro_sistema');
    exit;
}

// Buscar dados do livro caixa
try {
    $stmt = $pdo->prepare("SELECT * FROM lcx_livros_caixa WHERE id = ?");
    $stmt->execute([$livro_id]);
    $livro = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$livro) {
        header('Location: index.php?erro=livro_nao_encontrado');
        exit;
    }
    
    if ($livro['status'] !== 'aberto') {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=livro_fechado');
        exit;
    }
} catch (Exception $e) {
    error_log("Erro ao buscar livro: " . $e->getMessage());
    header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=erro_sistema');
    exit;
}

// Processar ação
try {
    $pdo->beginTransaction();
    
    switch ($acao) {
        case 'abrir':
            // Abrir novo dia
            $hoje = date('Y-m-d');
            $agora = date('Y-m-d H:i:s');

            // Verificar se já existe registro para hoje
            $stmt = $pdo->prepare("SELECT id, status FROM lcx_controle_dias WHERE livro_caixa_id = ? AND data_dia = ?");
            $stmt->execute([$livro_id, $hoje]);
            $dia_hoje = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($dia_hoje) {
                if ($dia_hoje['status'] === 'aberto') {
                    throw new Exception('O dia de hoje já está aberto');
                } else {
                    // Reabrir dia fechado
                    $stmt = $pdo->prepare("
                        UPDATE lcx_controle_dias
                        SET status = 'aberto', data_fechamento = NULL, fechado_por = NULL,
                            observacoes_fechamento = NULL, updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    ");
                    $stmt->execute([$dia_hoje['id']]);

                    // Log da reabertura
                    $stmt = $pdo->prepare("
                        INSERT INTO lcx_logs_dias (livro_caixa_id, data_dia, acao, usuario_id, saldo_momento, observacoes)
                        VALUES (?, ?, 'reabertura', ?, ?, 'Dia reaberto pelo usuário')
                    ");
                    $stmt->execute([$livro_id, $hoje, $user_id, $livro['saldo_atual']]);

                    $mensagem = 'dia_reaberto';
                }
            } else {
                // Verificar se há outro dia aberto (não o de hoje)
                if ($livro['dia_aberto'] && $livro['dia_aberto'] !== $hoje) {
                    $stmt = $pdo->prepare("SELECT status FROM lcx_controle_dias WHERE livro_caixa_id = ? AND data_dia = ?");
                    $stmt->execute([$livro_id, $livro['dia_aberto']]);
                    $dia_anterior = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($dia_anterior && $dia_anterior['status'] === 'aberto') {
                        // Fechar o dia anterior automaticamente
                        $stmt = $pdo->prepare("
                            UPDATE lcx_controle_dias
                            SET status = 'fechado', data_fechamento = ?, fechado_por = ?,
                                saldo_fechamento = ?, observacoes_fechamento = 'Dia fechado automaticamente ao abrir novo dia',
                                updated_at = CURRENT_TIMESTAMP
                            WHERE livro_caixa_id = ? AND data_dia = ?
                        ");
                        $stmt->execute([$agora, $user_id, $livro['saldo_atual'], $livro_id, $livro['dia_aberto']]);

                        // Log do fechamento automático
                        $stmt = $pdo->prepare("
                            INSERT INTO lcx_logs_dias (livro_caixa_id, data_dia, acao, usuario_id, saldo_momento, observacoes)
                            VALUES (?, ?, 'fechamento_automatico', ?, ?, 'Dia fechado automaticamente ao abrir novo dia')
                        ");
                        $stmt->execute([$livro_id, $livro['dia_aberto'], $user_id, $livro['saldo_atual']]);
                    }
                }

                // Criar novo dia
                $stmt = $pdo->prepare("
                    INSERT INTO lcx_controle_dias
                    (livro_caixa_id, data_dia, data_abertura, aberto_por, saldo_abertura, observacoes_abertura)
                    VALUES (?, ?, ?, ?, ?, 'Dia aberto automaticamente')
                ");
                $stmt->execute([$livro_id, $hoje, $agora, $user_id, $livro['saldo_atual']]);

                // Log da abertura
                $stmt = $pdo->prepare("
                    INSERT INTO lcx_logs_dias (livro_caixa_id, data_dia, acao, usuario_id, saldo_momento, observacoes)
                    VALUES (?, ?, 'abertura', ?, ?, 'Novo dia aberto')
                ");
                $stmt->execute([$livro_id, $hoje, $user_id, $livro['saldo_atual']]);

                $mensagem = 'dia_aberto';
            }

            // Atualizar livro com o dia aberto
            $stmt = $pdo->prepare("UPDATE lcx_livros_caixa SET dia_aberto = ? WHERE id = ?");
            $stmt->execute([$hoje, $livro_id]);

            break;
            
        case 'fechar':
            // Fechar dia atual
            if (!$livro['dia_aberto']) {
                throw new Exception('Não há dia aberto para fechar');
            }
            
            $stmt = $pdo->prepare("SELECT id, status FROM lcx_controle_dias WHERE livro_caixa_id = ? AND data_dia = ?");
            $stmt->execute([$livro_id, $livro['dia_aberto']]);
            $dia_atual = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$dia_atual || $dia_atual['status'] !== 'aberto') {
                throw new Exception('O dia atual não está aberto');
            }
            
            $agora = date('Y-m-d H:i:s');
            
            // Fechar o dia
            $stmt = $pdo->prepare("
                UPDATE lcx_controle_dias 
                SET status = 'fechado', data_fechamento = ?, fechado_por = ?, 
                    saldo_fechamento = ?, observacoes_fechamento = 'Dia fechado pelo usuário',
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $stmt->execute([$agora, $user_id, $livro['saldo_atual'], $dia_atual['id']]);
            
            // Atualizar livro
            $stmt = $pdo->prepare("
                UPDATE lcx_livros_caixa 
                SET dia_aberto = NULL, ultimo_fechamento = ? 
                WHERE id = ?
            ");
            $stmt->execute([$agora, $livro_id]);
            
            // Log do fechamento
            $stmt = $pdo->prepare("
                INSERT INTO lcx_logs_dias (livro_caixa_id, data_dia, acao, usuario_id, saldo_momento, observacoes)
                VALUES (?, ?, 'fechamento', ?, ?, 'Dia fechado pelo usuário')
            ");
            $stmt->execute([$livro_id, $livro['dia_aberto'], $user_id, $livro['saldo_atual']]);
            
            $mensagem = 'dia_fechado';
            break;
            
        case 'toggle_edicao':
            // Alternar permissão de edição de datas (manter compatibilidade)
            $novo_valor = $livro['permite_edicao_data'] ? 0 : 1;

            $stmt = $pdo->prepare("UPDATE lcx_livros_caixa SET permite_edicao_data = ? WHERE id = ?");
            $stmt->execute([$novo_valor, $livro_id]);

            $mensagem = $novo_valor ? 'edicao_data_habilitada' : 'edicao_data_desabilitada';
            break;

        case 'configurar_edicao_especifica':
            // Configurar edição retroativa específica
            $tipo_edicao = $_POST['tipo_edicao'] ?? '';
            $data_edicao_especifica = $_POST['data_edicao_especifica'] ?? null;

            if (!in_array($tipo_edicao, ['geral', 'especifica', 'desabilitar'])) {
                throw new Exception('Tipo de edição inválido');
            }

            // Validar data específica se necessário
            if ($tipo_edicao === 'especifica') {
                if (!$data_edicao_especifica || !DateTime::createFromFormat('Y-m-d', $data_edicao_especifica)) {
                    throw new Exception('Data específica é obrigatória e deve ser válida');
                }
            }

            // Configurar campos baseado no tipo
            switch ($tipo_edicao) {
                case 'geral':
                    $permite_edicao_data = 1;
                    $data_edicao_especifica = null;
                    $mensagem = 'edicao_geral_habilitada';
                    break;

                case 'especifica':
                    $permite_edicao_data = 1;
                    $mensagem = 'edicao_especifica_habilitada';
                    break;

                case 'desabilitar':
                    $permite_edicao_data = 0;
                    $data_edicao_especifica = null;
                    $mensagem = 'edicao_data_desabilitada';
                    break;
            }

            // Atualizar banco de dados
            $stmt = $pdo->prepare("
                UPDATE lcx_livros_caixa
                SET permite_edicao_data = ?,
                    data_edicao_especifica = ?,
                    habilitado_por = ?,
                    data_habilitacao = CURRENT_TIMESTAMP
                WHERE id = ?
            ");
            $stmt->execute([$permite_edicao_data, $data_edicao_especifica, $user_id, $livro_id]);

            // Buscar dados do livro para o log
            $stmt = $pdo->prepare("
                SELECT lc.nome, pa.nome as pa_nome
                FROM lcx_livros_caixa lc
                JOIN pontos_atendimento pa ON lc.ponto_atendimento_id = pa.id
                WHERE lc.id = ?
            ");
            $stmt->execute([$livro_id]);
            $dados_livro = $stmt->fetch(PDO::FETCH_ASSOC);

            // Registrar log específico baseado no tipo
            switch ($tipo_edicao) {
                case 'geral':
                    log_habilitar_edicao_geral($pdo, $livro_id, $dados_livro['nome'], $dados_livro['pa_nome']);
                    break;

                case 'especifica':
                    log_habilitar_edicao_especifica($pdo, $livro_id, $dados_livro['nome'], $dados_livro['pa_nome'], $data_edicao_especifica);
                    break;

                case 'desabilitar':
                    // Determinar tipo anterior para o log
                    $tipo_anterior = $livro['permite_edicao_data'] ?
                        ($livro['data_edicao_especifica'] ? 'específica' : 'geral') : 'desabilitada';
                    log_desabilitar_edicao($pdo, $livro_id, $dados_livro['nome'], $dados_livro['pa_nome'], $tipo_anterior);
                    break;
            }

            break;
            
        default:
            throw new Exception('Ação inválida');
    }
    
    $pdo->commit();
    
    // Log da operação
    error_log("Controle de dia executado: Ação {$acao}, Livro {$livro_id}, Usuário {$user_id}");
    
    // Adicionar suporte a redirecionamento customizado
    $redirect = $_POST['redirect'] ?? null;

    // Construir URL de redirecionamento com parâmetros adicionais
    $url_params = ['sucesso' => $mensagem];

    // Adicionar data específica se configurada
    if ($acao === 'configurar_edicao_especifica' && isset($data_edicao_especifica) && $data_edicao_especifica) {
        $url_params['data'] = $data_edicao_especifica;
    }

    $query_string = http_build_query($url_params);

    // Redirecionar com sucesso
    if ($redirect) {
        header('Location: ' . $redirect . '&' . $query_string);
    } else {
        header('Location: index.php?' . $query_string);
    }
    exit;
    
} catch (Exception $e) {
    // Reverter transação em caso de erro
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    // Log detalhado do erro
    $erro_detalhado = "Erro no controle de dia: " . $e->getMessage() .
                     " | Ação: {$acao} | Livro: {$livro_id} | Usuário: {$user_id} | " .
                     "Arquivo: " . $e->getFile() . " | Linha: " . $e->getLine();
    error_log($erro_detalhado);

    // Redirecionar com erro específico
    if (strpos($e->getMessage(), 'já existe') !== false || strpos($e->getMessage(), 'já está') !== false) {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=dia_ja_aberto');
    } elseif (strpos($e->getMessage(), 'não há') !== false || strpos($e->getMessage(), 'não está') !== false) {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=dia_nao_aberto');
    } elseif (strpos($e->getMessage(), 'foreign key') !== false || strpos($e->getMessage(), 'constraint') !== false) {
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=erro_integridade_dados');
    } else {
        // Para debug, vamos mostrar o erro específico
        $erro_codificado = urlencode($e->getMessage());
        header('Location: visualizar_livro.php?id=' . $livro_id . '&erro=erro_controle_dia&detalhes=' . $erro_codificado);
    }
    exit;
}
?>
