<?php
// Habilitar exibição de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 0); // Desabilitar exibição de erros para evitar output antes do JSON

// Log dos dados recebidos
error_log("POST data: " . print_r($_POST, true));

// Garantir que nada seja enviado antes do JSON
ob_start();

try {
    require_once '../../auth_check.php';
    require_once '../../config/database.php';
    require_once '../verificar_acesso.php';

    header('Content-Type: application/json');

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido');
    }

    if (!isset($_POST['acao'])) {
        throw new Exception('Ação não informada');
    }

    if ($_POST['acao'] === 'excluir_acordo' && TIPO_ACESSO_COBRANCA !== 'GESTOR') {
        throw new Exception('Acesso negado. Apenas gestores podem excluir acordos.');
    }

    $acao = $_POST['acao'] ?? '';
    error_log("Ação recebida: " . $acao);

    switch ($acao) {
        case 'criar_acordo':
            error_log("Iniciando criação de acordo");
            
            $processo_id = filter_input(INPUT_POST, 'processo_id', FILTER_VALIDATE_INT);
            $data_acordo = htmlspecialchars($_POST['data_acordo'] ?? '', ENT_QUOTES, 'UTF-8');
            
            // Processar valor do acordo
            $valor_acordo_str = $_POST['valor_acordo'] ?? '0';
            $valor_acordo = str_replace(['R$', ' ', '.', ','], ['', '', '', '.'], $valor_acordo_str);
            $valor_acordo = filter_var($valor_acordo, FILTER_VALIDATE_FLOAT);
            
            if (!$valor_acordo || $valor_acordo <= 0) {
                throw new Exception("Valor do acordo inválido: " . $valor_acordo_str);
            }
            
            error_log("=== PROCESSAMENTO DE VALORES ===");
            error_log("Valor acordo original: " . $valor_acordo_str);
            error_log("Valor acordo processado: " . $valor_acordo);
            
            $numero_repactuacao = htmlspecialchars($_POST['numero_repactuacao'] ?? '', ENT_QUOTES, 'UTF-8');
            $conta_corrente = htmlspecialchars($_POST['conta_corrente'] ?? '', ENT_QUOTES, 'UTF-8');
            $numero_contato = htmlspecialchars($_POST['numero_contato'] ?? '', ENT_QUOTES, 'UTF-8');
            $quantidade_parcelas = filter_input(INPUT_POST, 'quantidade_parcelas', FILTER_VALIDATE_INT);
            $observacoes = htmlspecialchars($_POST['observacoes'] ?? '', ENT_QUOTES, 'UTF-8');
            $inativar_anteriores = isset($_POST['inativar_anteriores']) && $_POST['inativar_anteriores'] === 'true';
            
            // Processamento de honorário personalizado
            $usar_honorario_personalizado = isset($_POST['usar_honorario_personalizado']) && ($_POST['usar_honorario_personalizado'] === 'on' || $_POST['usar_honorario_personalizado'] === 'true');
            $porcentagem_honorario_personalizada = null;
            
            if ($usar_honorario_personalizado && isset($_POST['porcentagem_honorario_personalizada'])) {
                $porcentagem_str = $_POST['porcentagem_honorario_personalizada'];
                $porcentagem_str = str_replace(['%', '.', ','], ['', '', '.'], $porcentagem_str);
                $porcentagem_honorario_personalizada = filter_var($porcentagem_str, FILTER_VALIDATE_FLOAT);

                if ($porcentagem_honorario_personalizada === false || $porcentagem_honorario_personalizada < 0) {
                    throw new Exception("Porcentagem de honorário inválida.");
                }

                error_log("Porcentagem de honorário personalizada: " . $porcentagem_honorario_personalizada);
            }
            
            // Processamento da Assunção de Dívida
            $assuncao_divida = isset($_POST['assuncao_divida']) && ($_POST['assuncao_divida'] === 'on' || $_POST['assuncao_divida'] === 'true');
            $assuntor_id = null;
            if ($assuncao_divida && !empty($_POST['assuntor_id'])) {
                $assuntor_id = filter_input(INPUT_POST, 'assuntor_id', FILTER_VALIDATE_INT);
                
                // Verificar se o assuntor existe
                if ($assuntor_id) {
                    $stmt = $pdo->prepare("SELECT id FROM cbp_associados WHERE id = ?");
                    $stmt->execute([$assuntor_id]);
                    if (!$stmt->fetch()) {
                        throw new Exception("Associado assuntor não encontrado.");
                    }
                } else {
                    // Se marcou assunção de dívida mas não selecionou o assuntor
                    throw new Exception("É obrigatório selecionar um associado assuntor quando a opção 'Assunção de Dívida' está marcada.");
                }
            }
            error_log("Assunção de Dívida: " . ($assuncao_divida ? 'Sim' : 'Não') . ", Assuntor ID: " . ($assuntor_id ?? 'NULL'));
            
            // Opções de personalização de parcelas
            $personalizar_parcelas = isset($_POST['personalizar_parcelas']) && $_POST['personalizar_parcelas'] === 'true';
            $tipo_personalizacao = htmlspecialchars($_POST['tipo_personalizacao'] ?? '', ENT_QUOTES, 'UTF-8');
            
            // Tipo de vencimento e cálculo
            $tipo_vencimento = htmlspecialchars($_POST['tipo_vencimento'] ?? 'mensal', ENT_QUOTES, 'UTF-8');
            $tipo_calculo = htmlspecialchars($_POST['tipo_calculo'] ?? 'automatico', ENT_QUOTES, 'UTF-8');
            
            // IMPORTANTE: Se o tipo de vencimento for manual, SEMPRE força o tipo de cálculo para manual
            if ($tipo_vencimento === 'manual') {
                $tipo_calculo = 'manual';
                error_log("AVISO: Tipo de cálculo forçado para manual pois o tipo de vencimento é manual");
            }
            
            // Dia de vencimento
            $dia_vencimento_raw = $_POST['dia_vencimento'] ?? null;
            error_log("=== PROCESSAMENTO DIA VENCIMENTO ===");
            error_log("POST original: " . print_r($_POST['dia_vencimento'], true));
            
            // Limpar e converter para inteiro
            if ($dia_vencimento_raw !== null) {
                // Remover zeros à esquerda e converter para inteiro
                $dia_vencimento = (int)ltrim($dia_vencimento_raw, '0');
                if ($dia_vencimento < 1 || $dia_vencimento > 31) {
                    $dia_vencimento = 1;
                }
            } else {
                $dia_vencimento = 1;
            }
            
            error_log("Dia vencimento (após processamento): " . $dia_vencimento);
            
            // Data de vencimento inicial (novo campo)
            $data_vencimento_inicial = isset($_POST['data_vencimento_inicial']) 
                ? htmlspecialchars($_POST['data_vencimento_inicial'], ENT_QUOTES, 'UTF-8') 
                : null;
            
            error_log("Tipo vencimento: " . $tipo_vencimento);
            error_log("Tipo cálculo: " . $tipo_calculo);
            error_log("Dia vencimento: " . $dia_vencimento);
            error_log("Data de vencimento inicial: " . ($data_vencimento_inicial ?? 'Não informada'));
            
            // Validar dia de vencimento
            if ($dia_vencimento < 1 || $dia_vencimento > 31) {
                throw new Exception("Dia de vencimento inválido: " . $dia_vencimento);
            }
            
            // Se tiver data de vencimento inicial, validar
            if ($data_vencimento_inicial) {
                $data_inicial = DateTime::createFromFormat('Y-m-d', $data_vencimento_inicial);
                if (!$data_inicial) {
                    throw new Exception("Data de vencimento inicial inválida");
                }
                
                // Extrair e processar o dia da data inicial
                $dia_data_inicial = (int)$data_inicial->format('d');
                
                error_log("=== VALIDAÇÃO DA DATA INICIAL ===");
                error_log("Data completa: " . $data_vencimento_inicial);
                error_log("Dia extraído: " . $dia_data_inicial);
                error_log("Dia vencimento para comparação: " . $dia_vencimento);
                
                // Verificar se o dia da data inicial corresponde ao dia de vencimento
                if ($dia_data_inicial !== $dia_vencimento) {
                    throw new Exception(sprintf(
                        "O dia da data de vencimento inicial (%d) deve ser igual ao dia de vencimento mensal (%d)",
                        $dia_data_inicial,
                        $dia_vencimento
                    ));
                }
            }
            
            $mes_vencimento = null;
            if ($tipo_vencimento === 'anual') {
                $mes_vencimento = filter_input(INPUT_POST, 'mes_vencimento', FILTER_VALIDATE_INT);
                if ($mes_vencimento === false || $mes_vencimento === null || $mes_vencimento < 1 || $mes_vencimento > 12) {
                    throw new Exception('Mês de vencimento inválido para vencimento anual.');
                }
            }

            // Processar entrada
            $usar_entrada = isset($_POST['usar_entrada']) && ($_POST['usar_entrada'] === 'true' || $_POST['usar_entrada'] === 'on');
            $tipo_entrada = $_POST['tipo_entrada'] ?? '';
            $valor_entrada = 0;
            $valor_alvara = 0;
            $alvara_id = null;
            
            error_log("Usar entrada: " . ($usar_entrada ? 'Sim' : 'Não'));
            error_log("Tipo entrada: " . $tipo_entrada);
            
            if ($usar_entrada) {
                if ($tipo_entrada === 'valor' || $tipo_entrada === 'misto') {
                    $valor_entrada_str = $_POST['valor_entrada'] ?? '0';
                    error_log("Valor entrada original: " . $valor_entrada_str);
                    $valor_entrada = str_replace(['R$', ' ', '.', ','], ['', '', '', '.'], $valor_entrada_str);
                    $valor_entrada = filter_var($valor_entrada, FILTER_VALIDATE_FLOAT) ?: 0;
                    error_log("Valor entrada processado: " . $valor_entrada);
                }
                
                if ($tipo_entrada === 'alvara' || $tipo_entrada === 'misto') {
                    $alvara_id = filter_input(INPUT_POST, 'alvara_id', FILTER_VALIDATE_INT);
                    error_log("Alvará ID: " . ($alvara_id ?: 'Não informado'));
                    
                    if ($alvara_id) {
                        $stmt = $pdo->prepare("SELECT valor FROM cbp_alvaras WHERE id = ?");
                        $stmt->execute([$alvara_id]);
                        $alvara = $stmt->fetch(PDO::FETCH_ASSOC);
                        if ($alvara) {
                            $valor_alvara = floatval($alvara['valor']);
                            error_log("Valor do alvará encontrado: " . $valor_alvara);
                        } else {
                            error_log("Alvará não encontrado!");
                        }
                    }
                }
            }
            
            // Calcular valor total da entrada (apenas entrada em dinheiro e alvará usado como entrada)
            $valor_total_entrada = $valor_entrada + $valor_alvara;
            error_log("=== CÁLCULO DA ENTRADA ===");
            error_log("Valor entrada em dinheiro: " . $valor_entrada);
            error_log("Valor do alvará: " . $valor_alvara);
            error_log("Valor total da entrada inicial: " . $valor_total_entrada);

            // Processar Alvará a Receber
            $usar_alvara_receber = isset($_POST['usar_alvara_receber']) && ($_POST['usar_alvara_receber'] === 'true' || $_POST['usar_alvara_receber'] === 'on');
            $valor_alvara_receber = 0;

            if ($usar_alvara_receber) {
                $valor_alvara_receber_str = $_POST['valor_alvara_receber'] ?? '0';
                $valor_alvara_receber = str_replace(['R$', ' ', '.', ','], ['', '', '', '.'], $valor_alvara_receber_str);
                $valor_alvara_receber = filter_var($valor_alvara_receber, FILTER_VALIDATE_FLOAT) ?: 0;
                
                error_log("=== PROCESSAMENTO DO ALVARÁ A RECEBER ===");
                error_log("Valor alvará a receber original: " . $valor_alvara_receber_str);
                error_log("Valor alvará a receber processado: " . $valor_alvara_receber);

                if ($valor_alvara_receber <= 0) {
                    throw new Exception("Valor do alvará a receber inválido");
                }
            }

            // Calcular valor total considerando entrada e alvará a receber
            $valor_total_abatimentos = $valor_total_entrada + $valor_alvara_receber;
            error_log("=== CÁLCULO DOS ABATIMENTOS ===");
            error_log("Valor total da entrada: " . $valor_total_entrada);
            error_log("Valor do alvará a receber: " . $valor_alvara_receber);
            error_log("Valor total dos abatimentos: " . $valor_total_abatimentos);

            if ($valor_total_abatimentos >= $valor_acordo) {
                throw new Exception(sprintf(
                    "O valor total dos abatimentos (R$ %s) não pode ser maior ou igual ao valor do acordo (R$ %s)",
                    number_format($valor_total_abatimentos, 2, ',', '.'),
                    number_format($valor_acordo, 2, ',', '.')
                ));
            }
            
            $valor_restante = $valor_acordo - $valor_total_abatimentos;
            
            error_log("=== CÁLCULO DO VALOR RESTANTE ===");
            error_log("Valor do acordo: " . $valor_acordo);
            error_log("Valor total dos abatimentos: " . $valor_total_abatimentos);
            error_log("Valor restante: " . $valor_restante);
            
            // Criar parcelas
            $valor_restante = $valor_acordo - $valor_total_abatimentos;
            
            // Preparar dados para inserção
            $dados = [
                'processo_id' => $processo_id,
                'data_acordo' => $data_acordo,
                'valor_acordo' => $valor_acordo,
                'valor_entrada' => $valor_entrada,
                'alvara_id' => $alvara_id,
                'numero_repactuacao' => $numero_repactuacao,
                'conta_corrente' => $conta_corrente,
                'numero_contato' => $numero_contato,
                'quantidade_parcelas' => $quantidade_parcelas,
                'assuncao_divida' => $assuncao_divida ? 1 : 0,
                'assuntor_id' => $assuntor_id,
                'observacoes' => $observacoes,
                'ativo' => 1,
                'status_id' => $status_vigente,
                'dia_vencimento' => $dia_vencimento,
                'tipo_calculo' => $tipo_calculo,
                'usar_honorario_personalizado' => $usar_honorario_personalizado ? 1 : 0,
                'porcentagem_honorario_personalizada' => $usar_honorario_personalizado ? $porcentagem_honorario_personalizada : null,
                'data_vencimento_inicial' => $data_vencimento_inicial
            ];

            // Iniciar transação
            $pdo->beginTransaction();
            
            try {
                // Verificar se processo existe
                $stmt = $pdo->prepare("SELECT * FROM cbp_processos_judiciais WHERE id = ?");
                    $stmt->execute([$processo_id]);
                $processo = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$processo) {
                    throw new Exception("Processo não encontrado.");
                }
                
                // Buscar status_id do status VIGENTE
                $stmt = $pdo->prepare("SELECT id FROM cbp_status_acordo WHERE nome = 'VIGENTE'");
                        $stmt->execute();
                $status_vigente = $stmt->fetchColumn();
                
                if (!$status_vigente) {
                    $status_vigente = 1; // Valor padrão se não encontrar
                }
                
                // Buscar status_id do status INATIVO
                $stmt = $pdo->prepare("SELECT id FROM cbp_status_acordo WHERE nome = 'INATIVO'");
                $stmt->execute();
                $status_inativo = $stmt->fetchColumn();
                
                if (!$status_inativo) {
                    $status_inativo = 4; // Valor padrão se não encontrar
                }
                
                // Inativar todos os acordos anteriores do processo
                $stmt = $pdo->prepare("
                    UPDATE cbp_acordos 
                    SET ativo = 0, 
                        status_id = ?, 
                        updated_at = NOW() 
                    WHERE processo_id = ? 
                    AND ativo = 1
                ");
                $stmt->execute([$status_inativo, $processo_id]);
                
                error_log("Acordos anteriores inativados com sucesso");
                error_log("Processo ID: " . $processo_id);
                error_log("Rows afetadas: " . $stmt->rowCount());
                
                // Inserir novo acordo como VIGENTE
                $stmt = $pdo->prepare("
                    INSERT INTO cbp_acordos (
                        processo_id, 
                        data_acordo, 
                        valor_acordo, 
                        valor_entrada,
                        alvara_id,
                        numero_repactuacao,
                        conta_corrente, 
                        numero_contato,
                        quantidade_parcelas,
                        assuncao_divida,
                        assuntor_id,
                        observacoes,
                        ativo,
                        status_id,
                        created_at,
                        updated_at,
                        dia_vencimento,
                        tipo_calculo,
                        usar_honorario_personalizado,
                        porcentagem_honorario_personalizada,
                        data_vencimento_inicial
                    ) VALUES (
                        :processo_id,
                        :data_acordo,
                        :valor_acordo,
                        :valor_entrada,
                        :alvara_id,
                        :numero_repactuacao,
                        :conta_corrente,
                        :numero_contato,
                        :quantidade_parcelas,
                        :assuncao_divida,
                        :assuntor_id,
                        :observacoes,
                        :ativo,
                        :status_id,
                        NOW(),
                        NOW(),
                        :dia_vencimento,
                        :tipo_calculo,
                        :usar_honorario_personalizado,
                        :porcentagem_honorario_personalizada,
                        :data_vencimento_inicial
                    )
                ");
                
                $stmt->execute($dados);
                $acordo_id = $pdo->lastInsertId();
                
                error_log("Acordo criado com ID: " . $acordo_id);
                
                // Atualizar status do processo para ACORDO JUDICIAL
                $ID_ACORDO_JUDICIAL = 2; // Status fixo para ACORDO JUDICIAL
                $stmt = $pdo->prepare("
                    UPDATE cbp_processos_judiciais 
                    SET status_id = ?, 
                        updated_at = NOW() 
                    WHERE id = ?
                ");
                $stmt->execute([$ID_ACORDO_JUDICIAL, $processo_id]);
                error_log("Status do processo atualizado para ACORDO JUDICIAL");
                
                // Criar parcelas baseadas no tipo de vencimento
                if ($tipo_calculo === 'manual' && isset($_POST['parcelas_manuais_json'])) {
                    $parcelas_manuais = json_decode($_POST['parcelas_manuais_json'], true) ?: [];
                    error_log("=== PROCESSANDO PARCELAS MANUAIS ===");
                    error_log("JSON recebido: " . $_POST['parcelas_manuais_json']);
                    error_log("Quantidade de parcelas esperada: " . $quantidade_parcelas);
                    error_log("Quantidade de parcelas recebidas: " . count($parcelas_manuais));
                    
                    if (empty($parcelas_manuais)) {
                        throw new Exception("Não foram definidas parcelas manuais.");
                    }
                    
                    if (count($parcelas_manuais) !== $quantidade_parcelas) {
                        throw new Exception("Número de parcelas manuais não corresponde ao total informado. Esperado: {$quantidade_parcelas}, Recebido: " . count($parcelas_manuais));
                    }
                    
                    // Validar o valor total das parcelas
                    $soma_parcelas = 0;
                    foreach ($parcelas_manuais as $parcela) {
                        if (!isset($parcela['valor']) || !isset($parcela['data_vencimento'])) {
                            throw new Exception("Dados incompletos na parcela #{$parcela['numero']}");
                        }
                        
                        $valor_parcela = floatval($parcela['valor']);
                        if ($valor_parcela <= 0) {
                            throw new Exception("Valor inválido para parcela #{$parcela['numero']}");
                        }
                        
                        $soma_parcelas += $valor_parcela;
                    }
                    
                    // Verificar se a soma das parcelas corresponde ao valor restante
                    $diferenca = abs($soma_parcelas - $valor_restante);
                    if ($diferenca > 0.01) {
                        throw new Exception(sprintf(
                            "A soma dos valores das parcelas (R$ %s) não corresponde ao valor restante do acordo (R$ %s)",
                            number_format($soma_parcelas, 2, ',', '.'),
                            number_format($valor_restante, 2, ',', '.')
                        ));
                    }
                    
                    // Criar as parcelas manualmente
                    foreach ($parcelas_manuais as $parcela) {
                        $data_vencimento = $parcela['data_vencimento'];
                        // Converter data de DD/MM/YYYY para YYYY-MM-DD se necessário
                        if (preg_match('/^(\\d{2})\\/(\\d{2})\\/(\\d{4})$/', $data_vencimento, $matches)) {
                            $data_vencimento = "{$matches[3]}-{$matches[2]}-{$matches[1]}";
                        }
                        $stmt = $pdo->prepare("
                            INSERT INTO cbp_parcelas_acordo (
                                acordo_id,
                                numero_parcela,
                                data_vencimento,
                                valor_parcela,
                                status,
                                created_at,
                                updated_at
                            ) VALUES (?, ?, ?, ?, 'PENDENTE', NOW(), NOW())
                        ");
                        
                        $stmt->execute([
                            $acordo_id,
                            $parcela['numero'],
                            $data_vencimento,
                            floatval($parcela['valor'])
                        ]);
                        
                        error_log("Parcela #{$parcela['numero']} criada: Valor = {$parcela['valor']}, Vencimento = {$data_vencimento}");
                    }
                    
                    error_log("=== RESUMO DA CRIAÇÃO DE PARCELAS MANUAIS ===");
                    error_log("Total de parcelas criadas: " . count($parcelas_manuais));
                    error_log("Valor total das parcelas: R$ " . number_format($soma_parcelas, 2, ',', '.'));
                } else {
                    // Código existente para criação de parcelas automáticas
                    $valor_parcela = $valor_restante / $quantidade_parcelas;
                    
                    // Definir timezone para UTC para evitar problemas com DST
                    date_default_timezone_set('UTC');
                    $data_base = new DateTime($data_acordo);
                    
                    if ($tipo_vencimento === 'anual') {
                        // Para vencimento anual, ajustar para o próximo ano e usar o mês e dia especificados
                        $data_base->modify('+1 year');
                        
                        // Criar uma nova data com o dia e mês especificados
                        $data_base = new DateTime(sprintf(
                            '%d-%02d-%02d 12:00:00',
                            $data_base->format('Y'),
                            $mes_vencimento,
                            $dia_vencimento
                        ));
                        
                        // Verificar se o dia é válido para o mês
                        $ultimo_dia = cal_days_in_month(CAL_GREGORIAN, $mes_vencimento, $data_base->format('Y'));
                        if ($dia_vencimento > $ultimo_dia) {
                            // Se o dia especificado for maior que o último dia do mês, usar o último dia
                            $data_base = new DateTime(sprintf(
                                '%d-%02d-%02d 12:00:00',
                                $data_base->format('Y'),
                                $mes_vencimento,
                                $ultimo_dia
                            ));
                        } else {
                            $data_base = new DateTime(sprintf(
                                '%d-%02d-%02d 12:00:00',
                                $data_base->format('Y'),
                                $mes_vencimento,
                                $dia_vencimento
                            ));
                        }
                    } else {
                        // Para vencimento mensal, agora vamos calcular a data corretamente
                        // Se tiver data de vencimento inicial, usar ela como base
                        if ($data_vencimento_inicial) {
                            $data_base = new DateTime($data_vencimento_inicial);
                            error_log("Usando data de vencimento inicial como base: " . $data_base->format('Y-m-d'));
                        } else {
                            // Obter a data atual para comparação
                            $data_atual = new DateTime('now');
                            
                            // Criar uma data base com o mesmo mês e ano da data atual, mas com o dia de vencimento escolhido
                            $mes_atual = $data_atual->format('m');
                            $ano_atual = $data_atual->format('Y');
                            
                            // Verificar se o dia é válido para o mês
                            $ultimo_dia = cal_days_in_month(CAL_GREGORIAN, $mes_atual, $ano_atual);
                            $dia = min($dia_vencimento, $ultimo_dia);
                            
                            // Criar uma data com o dia de vencimento no mês atual
                            $data_base = new DateTime(sprintf(
                                '%d-%02d-%02d 12:00:00',
                                $ano_atual,
                                $mes_atual,
                                $dia
                            ));
                            
                            // Se a data já passou, avançar para o próximo mês
                            if ($data_base < $data_atual) {
                                $data_base->modify('first day of next month');
                                
                                // Verificar se o dia é válido para o novo mês
                                $ultimo_dia = cal_days_in_month(
                                    CAL_GREGORIAN,
                                    $data_base->format('m'),
                                    $data_base->format('Y')
                                );
                                
                                // Se o dia de vencimento for maior que o último dia do mês
                                if ($dia_vencimento > $ultimo_dia) {
                                    error_log(sprintf(
                                        "Mês %s/%s tem apenas %d dias. Ajustando vencimento.",
                                        $data_base->format('m'),
                                        $data_base->format('Y'),
                                        $ultimo_dia
                                    ));
                                    
                                    // Usar o último dia do mês
                                    $data_base = new DateTime(sprintf(
                                        '%d-%02d-%02d 12:00:00',
                                        $data_base->format('Y'),
                                        $data_base->format('m'),
                                        $ultimo_dia
                                    ));
                                } else {
                                    // Usar o dia de vencimento normal
                                    $data_base = new DateTime(sprintf(
                                        '%d-%02d-%02d 12:00:00',
                                        $data_base->format('Y'),
                                        $data_base->format('m'),
                                        $dia_vencimento
                                    ));
                                }
                                
                                error_log(sprintf(
                                    "Parcela %d: Data de vencimento definida para %s",
                                    $i,
                                    $data_base->format('Y-m-d')
                                ));
                            }
                        }
                    }
                    
                    // Cria as parcelas baseadas no tipo de cálculo
                    for ($i = 1; $i <= $quantidade_parcelas; $i++) {
                        if ($i > 1) {
                            if ($tipo_vencimento === 'anual') {
                                // Para vencimento anual, avançar um ano mantendo o mesmo dia e mês
                                $data_base->modify('+1 year');
                                
                                // Verificar se o dia ainda é válido (caso de ano bissexto)
                                $ultimo_dia = cal_days_in_month(
                                    CAL_GREGORIAN,
                                    $mes_vencimento,
                                    $data_base->format('Y')
                                );
                                
                                if ($dia_vencimento > $ultimo_dia) {
                                    // Se o dia especificado for maior que o último dia do mês, usar o último dia
                                    $data_base = new DateTime(sprintf(
                                        '%d-%02d-%02d 12:00:00',
                                        $data_base->format('Y'),
                                        $mes_vencimento,
                                        $ultimo_dia
                                    ));
                                } else {
                                    $data_base = new DateTime(sprintf(
                                        '%d-%02d-%02d 12:00:00',
                                        $data_base->format('Y'),
                                        $mes_vencimento,
                                        $dia_vencimento
                                    ));
                                }
                            } else {
                                // Para vencimento mensal, avançar um mês
                                $data_base->modify('first day of next month');
                                
                                // Verificar se o dia é válido para o novo mês
                                $ultimo_dia = cal_days_in_month(
                                    CAL_GREGORIAN,
                                    $data_base->format('m'),
                                    $data_base->format('Y')
                                );
                                
                                // Se o dia de vencimento for maior que o último dia do mês
                                if ($dia_vencimento > $ultimo_dia) {
                                    error_log(sprintf(
                                        "Mês %s/%s tem apenas %d dias. Ajustando vencimento.",
                                        $data_base->format('m'),
                                        $data_base->format('Y'),
                                        $ultimo_dia
                                    ));
                                    
                                    // Usar o último dia do mês
                                    $data_base = new DateTime(sprintf(
                                        '%d-%02d-%02d 12:00:00',
                                        $data_base->format('Y'),
                                        $data_base->format('m'),
                                        $ultimo_dia
                                    ));
                                } else {
                                    // Usar o dia de vencimento normal
                                    $data_base = new DateTime(sprintf(
                                        '%d-%02d-%02d 12:00:00',
                                        $data_base->format('Y'),
                                        $data_base->format('m'),
                                        $dia_vencimento
                                    ));
                                }
                                
                                error_log(sprintf(
                                    "Parcela %d: Data de vencimento definida para %s",
                                    $i,
                                    $data_base->format('Y-m-d')
                                ));
                            }
                        }
                        
                        // Determinar o valor da parcela com base no tipo de cálculo
                        $valor_parcela_atual = $valor_parcela;
                        $data_vencimento_final = $data_base->format('Y-m-d');
                        
                        if ($tipo_calculo === 'manual' && !empty($parcelas_manuais)) {
                            // Encontrar a parcela correspondente pelo número
                            $parcela_encontrada = null;
                            foreach ($parcelas_manuais as $parcela) {
                                if (isset($parcela['numero']) && $parcela['numero'] == $i) {
                                    $parcela_encontrada = $parcela;
                                    error_log("Encontrada parcela manual #{$i}: " . print_r($parcela_encontrada, true));
                                    break;
                                }
                            }
                            
                            if ($parcela_encontrada) {
                                // Usar o valor da parcela manual se estiver disponível e for maior que zero
                                if (isset($parcela_encontrada['valor'])) {
                                    $valor_parcela_atual = floatval($parcela_encontrada['valor']);
                                    error_log("Parcela {$i}: Valor alterado para {$valor_parcela_atual}");
                                } else {
                                    error_log("Parcela {$i}: Valor não encontrado na parcela manual, usando valor calculado {$valor_parcela_atual}");
                                }
                                
                                // Usar a data de vencimento manual se estiver disponível
                                if (isset($parcela_encontrada['data_vencimento']) && !empty($parcela_encontrada['data_vencimento'])) {
                                    $data_vencimento_final = $parcela_encontrada['data_vencimento'];
                                }
                            } else {
                                error_log("Não foi encontrada parcela manual para a parcela #{$i}");
                            }
                        }
                        
                        $stmt = $pdo->prepare("
                            INSERT INTO cbp_parcelas_acordo (
                                acordo_id,
                                numero_parcela,
                                data_vencimento,
                                valor_parcela,
                                status,
                                created_at,
                                updated_at
                            ) VALUES (?, ?, ?, ?, 'PENDENTE', NOW(), NOW())
                        ");
                        
                        $stmt->execute([
                            $acordo_id,
                            $i,
                            $data_vencimento_final,
                            $valor_parcela_atual
                        ]);
                    }
                }
                
                // Atualizar observação do alvará se usado (em vez de atualizar status que não existe)
                if ($alvara_id) {
                    $stmt = $pdo->prepare("UPDATE cbp_alvaras SET observacoes = 'Utilizado no acordo #" . $acordo_id . "', updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$alvara_id]);
                }

                // Inserir alvará a receber se existir
                if ($usar_alvara_receber && $valor_alvara_receber > 0) {
                    $stmt = $pdo->prepare("
                        INSERT INTO cbp_alvaras_acordo (
                            acordo_id,
                            valor,
                            status,
                            created_at,
                            updated_at
                        ) VALUES (
                            :acordo_id,
                            :valor,
                            'PENDENTE',
                            NOW(),
                            NOW()
                        )
                    ");

                    $stmt->execute([
                        'acordo_id' => $acordo_id,
                        'valor' => $valor_alvara_receber
                    ]);

                    error_log("Alvará a receber inserido com sucesso para o acordo #" . $acordo_id);
                }
                
                // Registrar honorário para entrada em dinheiro, se houver
                if ($valor_entrada > 0) {
                    try {
                        // Remover o registro antigo de honorários e substituir pela execução direta do debug_entradas.php
                        error_log("Executando debug_entradas.php para registrar honorário da entrada do acordo ID: $acordo_id");
                        
                        // Passar o ID do acordo como contexto
                        $acordo_id_para_debug = $acordo_id;
                        include_once(dirname(__DIR__) . '/debug_entradas.php');
                        error_log("debug_entradas.php executado com sucesso para acordo ID: $acordo_id");
                    } catch (Exception $e) {
                        // Registrar erro mas não abortar a transação
                        error_log("Erro ao registrar honorário para entrada: " . $e->getMessage());
                    }
                }
                
                // Registrar no log
                $detalhes = "Criação de acordo - Processo ID: $processo_id - Valor: R$ " . number_format($valor_acordo, 2, ',', '.') . 
                           " - Parcelas: $quantidade_parcelas" . ($assuncao_divida ? " - Com assunção de dívida" : "");
                $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
                $stmt->execute([$_SESSION['user_id'], 'Criação de Acordo', $detalhes]);

                // Commit
                $pdo->commit();
                
                // Sucesso!
                $response = [
                    'success' => true,
                    'message' => 'Acordo criado com sucesso!',
                    'acordo_id' => $acordo_id  // Adicionando o ID do acordo na resposta
                ];
                
                echo json_encode($response);
            } catch (Exception $e) {
                $pdo->rollBack();
                
                $response = [
                    'success' => false,
                    'message' => $e->getMessage()
                ];
                
                echo json_encode($response);
            }
            break;

        case 'buscar_parcelas':
            $acordo_id = intval($_POST['acordo_id']);
            
            $stmt = $pdo->prepare("
                SELECT * FROM cbp_parcelas_acordo 
                WHERE acordo_id = ? 
                ORDER BY numero_parcela
            ");
            $stmt->execute([$acordo_id]);
            $parcelas = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'parcelas' => $parcelas
            ]);
            break;

        case 'atualizar_parcela':
            $parcela_id = intval($_POST['parcela_id']);
            $acordo_id = intval($_POST['acordo_id']);
            $data_pagamento = $_POST['data_pagamento'];
            $valor_pago = str_replace(['R$', '.', ','], ['', '', '.'], $_POST['valor_pago']);
            $observacoes = $_POST['observacoes'];
            $status = $_POST['status'];

            // Atualizar parcela
            $stmt = $pdo->prepare("
                UPDATE cbp_parcelas_acordo 
                SET status = ?,
                    data_pagamento = ?,
                    valor_pago = ?,
                    observacoes = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");

            $stmt->execute([
                $status,
                $data_pagamento,
                $valor_pago,
                $observacoes,
                $parcela_id
            ]);

            // Verificar se todas as parcelas foram pagas
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as total,
                       SUM(CASE WHEN status = 'PAGO' THEN 1 ELSE 0 END) as pagas,
                       EXISTS (
                           SELECT 1 
                           FROM cbp_parcelas_acordo 
                           WHERE acordo_id = ? 
                           AND status = 'PENDENTE' 
                           AND data_vencimento < CURRENT_DATE
                       ) as tem_atrasadas
                FROM cbp_parcelas_acordo
                WHERE acordo_id = ?
            ");
            $stmt->execute([$acordo_id, $acordo_id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            // Buscar IDs dos status
            $stmt = $pdo->prepare("SELECT id, nome FROM cbp_status_acordo");
            $stmt->execute();
            $status_ids = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

            // Determinar novo status do acordo
            $novo_status_id = null;
            if ($result['total'] == $result['pagas']) {
                $novo_status_id = $status_ids['QUITADO'];
            } elseif ($result['tem_atrasadas']) {
                $novo_status_id = $status_ids['INADIMPLENTE'];
            } else {
                $novo_status_id = $status_ids['VIGENTE'];
            }

            // Atualizar status do acordo
            $stmt = $pdo->prepare("
                UPDATE cbp_acordos 
                SET status_id = ?, 
                    updated_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$novo_status_id, $acordo_id]);

            // Se o acordo foi quitado, atualizar status do processo
            if ($result['total'] == $result['pagas']) {
                $stmt = $pdo->prepare("
                    UPDATE cbp_processos_judiciais p
                    INNER JOIN cbp_acordos a ON a.processo_id = p.id
                    SET p.status_id = 3
                    WHERE a.id = ?
                ");
                $stmt->execute([$acordo_id]);
            }

            echo json_encode([
                'success' => true,
                'message' => 'Parcela atualizada com sucesso!'
            ]);
            break;

        case 'editar_acordo':
            $acordo_id = filter_input(INPUT_POST, 'acordo_id', FILTER_VALIDATE_INT);
            $processo_id = filter_input(INPUT_POST, 'processo_id', FILTER_VALIDATE_INT);
            $data_acordo = htmlspecialchars($_POST['data_acordo'] ?? '', ENT_QUOTES, 'UTF-8');
            $valor_acordo = filter_input(INPUT_POST, 'valor_acordo', FILTER_VALIDATE_FLOAT);
            $numero_repactuacao = htmlspecialchars($_POST['numero_repactuacao'] ?? '', ENT_QUOTES, 'UTF-8');
            $conta_corrente = htmlspecialchars($_POST['conta_corrente'] ?? '', ENT_QUOTES, 'UTF-8');
            $numero_contato = htmlspecialchars($_POST['numero_contato'] ?? '', ENT_QUOTES, 'UTF-8');
            $quantidade_parcelas = filter_input(INPUT_POST, 'quantidade_parcelas', FILTER_VALIDATE_INT);
            $observacoes = htmlspecialchars($_POST['observacoes'] ?? '', ENT_QUOTES, 'UTF-8');
            
            // Processamento da Assunção de Dívida
            $assuncao_divida = isset($_POST['assuncao_divida']) && ($_POST['assuncao_divida'] === 'on' || $_POST['assuncao_divida'] === 'true');
            $assuntor_id = null;
            if ($assuncao_divida && !empty($_POST['assuntor_id'])) {
                $assuntor_id = filter_input(INPUT_POST, 'assuntor_id', FILTER_VALIDATE_INT);
                
                // Verificar se o assuntor existe
                if ($assuntor_id) {
                    $stmt = $pdo->prepare("SELECT id FROM cbp_associados WHERE id = ?");
                    $stmt->execute([$assuntor_id]);
                    if (!$stmt->fetch()) {
                        throw new Exception("Associado assuntor não encontrado.");
                    }
                } else {
                    // Se marcou assunção de dívida mas não selecionou o assuntor
                    throw new Exception("É obrigatório selecionar um associado assuntor quando a opção 'Assunção de Dívida' está marcada.");
                }
            }
            error_log("Assunção de Dívida (editar): " . ($assuncao_divida ? 'Sim' : 'Não') . ", Assuntor ID: " . ($assuntor_id ?? 'NULL'));
            
            // Opções de personalização de parcelas
            $personalizar_parcelas = isset($_POST['personalizar_parcelas']) && $_POST['personalizar_parcelas'] === 'true';
            $tipo_personalizacao = htmlspecialchars($_POST['tipo_personalizacao'] ?? '', ENT_QUOTES, 'UTF-8');
            
            // Novo: Dia de vencimento personalizado e tipo de cálculo
            $tipo_calculo = htmlspecialchars($_POST['tipo_calculo'] ?? 'automatico', ENT_QUOTES, 'UTF-8');
            $dia_vencimento = filter_input(INPUT_POST, 'dia_vencimento', FILTER_VALIDATE_INT);
            
            // Tipo de vencimento e mês para vencimento anual
            $tipo_vencimento = htmlspecialchars($_POST['tipo_vencimento'] ?? 'mensal', ENT_QUOTES, 'UTF-8');
            
            // IMPORTANTE: Se o tipo de vencimento for manual, SEMPRE força o tipo de cálculo para manual
            if ($tipo_vencimento === 'manual') {
                $tipo_calculo = 'manual';
                error_log("AVISO: Tipo de cálculo forçado para manual pois o tipo de vencimento é manual");
            }
            
            // Validar dia de vencimento apenas se não for cálculo manual
            if ($tipo_calculo !== 'manual' && ($dia_vencimento === false || $dia_vencimento === null || $dia_vencimento < 1 || $dia_vencimento > 31)) {
                throw new Exception('Dia de vencimento inválido. Por favor, informe um dia entre 1 e 31.');
            } else if ($tipo_calculo === 'manual' && ($dia_vencimento === false || $dia_vencimento === null)) {
                // Para cálculo manual, definir um valor padrão se não for informado
                $dia_vencimento = 1;
            }

            $mes_vencimento = null;
            if ($tipo_vencimento === 'anual') {
                $mes_vencimento = filter_input(INPUT_POST, 'mes_vencimento', FILTER_VALIDATE_INT);
                if ($mes_vencimento === false || $mes_vencimento === null || $mes_vencimento < 1 || $mes_vencimento > 12) {
                    throw new Exception('Mês de vencimento inválido para vencimento anual.');
                }
            }

            $parcelas_manuais = [];
            
            if ($tipo_calculo === 'manual' && isset($_POST['parcelas_manuais'])) {
                $parcelas_manuais = json_decode($_POST['parcelas_manuais'], true) ?: [];
            }
            
            // Iniciar transação
            $pdo->beginTransaction();

            try {
                // Verificar se acordo existe
                $stmt = $pdo->prepare("SELECT * FROM cbp_acordos WHERE id = ?");
                $stmt->execute([$acordo_id]);
                $acordo = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$acordo) {
                    throw new Exception("Acordo não encontrado.");
                }
                
                // Buscar status_id do status VIGENTE
                $stmt = $pdo->prepare("SELECT id FROM cbp_status_acordo WHERE nome = 'VIGENTE'");
                $stmt->execute();
                $status_vigente = $stmt->fetchColumn();
                
                if (!$status_vigente) {
                    $status_vigente = 1; // Valor padrão se não encontrar
                }
                
                // Processar entradas
                $usar_entrada = isset($_POST['usar_entrada']) && $_POST['usar_entrada'] === 'true';
                $tipo_entrada = filter_input(INPUT_POST, 'tipo_entrada', FILTER_SANITIZE_STRING);
                $valor_entrada = 0;
                $valor_alvara = 0;
                $alvara_id = null;
                $alvara_anterior_id = $acordo['alvara_id']; // Guardar ID do alvará anterior para liberá-lo se necessário
                
                if ($usar_entrada) {
                    if ($tipo_entrada === 'valor' || $tipo_entrada === 'misto') {
                        $valor_entrada_str = $_POST['valor_entrada'] ?? '0';
                        $valor_entrada = str_replace(['R$', ' ', '.', ','], ['', '', '', '.'], $valor_entrada_str);
                        $valor_entrada = filter_var($valor_entrada, FILTER_VALIDATE_FLOAT) ?: 0;
                        error_log("Valor entrada monetária original: " . $valor_entrada_str);
                        error_log("Valor entrada monetária processado: " . $valor_entrada);
                    }
                    
                    if ($tipo_entrada === 'alvara' || $tipo_entrada === 'misto') {
                        $alvara_id = filter_input(INPUT_POST, 'alvara_id', FILTER_VALIDATE_INT);
                        error_log("Alvará ID: " . ($alvara_id ?: 'Não informado'));
                        
                        if ($alvara_id) {
                            $stmt = $pdo->prepare("SELECT valor FROM cbp_alvaras WHERE id = ?");
                            $stmt->execute([$alvara_id]);
                            $alvara = $stmt->fetch(PDO::FETCH_ASSOC);
                            if ($alvara) {
                                $valor_alvara = floatval($alvara['valor']);
                                error_log("Valor do alvará encontrado: " . $valor_alvara);
                            } else {
                                error_log("Alvará não encontrado!");
                            }
                        }
                    }
                }
                
                // Calcular valor da parcela
                $valor_total_entrada = $valor_entrada + $valor_alvara;
                $valor_restante = $valor_acordo - $valor_total_entrada;
                
                // Verificar se o número de parcelas foi alterado
                if ($acordo['quantidade_parcelas'] != $quantidade_parcelas) {
                    // Excluir parcelas anteriores
                    $stmt = $pdo->prepare("DELETE FROM cbp_parcelas_acordo WHERE acordo_id = ?");
                    $stmt->execute([$acordo_id]);
                    
                    // Recalcular valor da parcela
                    $valor_restante = $valor_acordo - $valor_total_entrada;
                    $valor_parcela = $valor_restante / $quantidade_parcelas;
                    
                    // Criar novas parcelas
                    // Definir timezone para UTC para evitar problemas com DST
                    date_default_timezone_set('UTC');
                    $data_base = new DateTime($data_acordo);
                    
                    if ($tipo_vencimento === 'anual') {
                        // Para vencimento anual, ajustar para o próximo ano e usar o mês e dia especificados
                        $data_base->modify('+1 year');
                        
                        // Criar uma nova data com o dia e mês especificados
                        $data_base = new DateTime(sprintf(
                            '%d-%02d-%02d 12:00:00',
                            $data_base->format('Y'),
                            $mes_vencimento,
                            $dia_vencimento
                        ));
                        
                        // Verificar se o dia é válido para o mês
                        $ultimo_dia = cal_days_in_month(CAL_GREGORIAN, $mes_vencimento, $data_base->format('Y'));
                        if ($dia_vencimento > $ultimo_dia) {
                            // Se o dia especificado for maior que o último dia do mês, usar o último dia
                            $data_base = new DateTime(sprintf(
                                '%d-%02d-%02d 12:00:00',
                                $data_base->format('Y'),
                                $mes_vencimento,
                                $ultimo_dia
                            ));
                        } else {
                            $data_base = new DateTime(sprintf(
                                '%d-%02d-%02d 12:00:00',
                                $data_base->format('Y'),
                                $mes_vencimento,
                                $dia_vencimento
                            ));
                        }
                    } else {
                        // Para vencimento mensal, agora vamos calcular a data corretamente
                        // Se tiver data de vencimento inicial, usar ela como base
                        if ($data_vencimento_inicial) {
                            $data_base = new DateTime($data_vencimento_inicial);
                            error_log("Usando data de vencimento inicial como base: " . $data_base->format('Y-m-d'));
                        } else {
                            // Obter a data atual para comparação
                            $data_atual = new DateTime('now');
                            
                            // Criar uma data base com o mesmo mês e ano da data atual, mas com o dia de vencimento escolhido
                            $mes_atual = $data_atual->format('m');
                            $ano_atual = $data_atual->format('Y');
                            
                            // Verificar se o dia é válido para o mês
                            $ultimo_dia = cal_days_in_month(CAL_GREGORIAN, $mes_atual, $ano_atual);
                            $dia = min($dia_vencimento, $ultimo_dia);
                            
                            // Criar uma data com o dia de vencimento no mês atual
                            $data_base = new DateTime(sprintf(
                                '%d-%02d-%02d 12:00:00',
                                $ano_atual,
                                $mes_atual,
                                $dia
                            ));
                            
                            // Se a data já passou, avançar para o próximo mês
                            if ($data_base < $data_atual) {
                                $data_base->modify('first day of next month');
                                
                                // Verificar se o dia é válido para o novo mês
                                $ultimo_dia = cal_days_in_month(
                                    CAL_GREGORIAN,
                                    $data_base->format('m'),
                                    $data_base->format('Y')
                                );
                                
                                // Se o dia de vencimento for maior que o último dia do mês
                                if ($dia_vencimento > $ultimo_dia) {
                                    error_log(sprintf(
                                        "Mês %s/%s tem apenas %d dias. Ajustando vencimento.",
                                        $data_base->format('m'),
                                        $data_base->format('Y'),
                                        $ultimo_dia
                                    ));
                                    
                                    // Usar o último dia do mês
                                    $data_base = new DateTime(sprintf(
                                        '%d-%02d-%02d 12:00:00',
                                        $data_base->format('Y'),
                                        $data_base->format('m'),
                                        $ultimo_dia
                                    ));
                                } else {
                                    // Usar o dia de vencimento normal
                                    $data_base = new DateTime(sprintf(
                                        '%d-%02d-%02d 12:00:00',
                                        $data_base->format('Y'),
                                        $data_base->format('m'),
                                        $dia_vencimento
                                    ));
                                }
                                
                                error_log(sprintf(
                                    "Parcela %d: Data de vencimento definida para %s",
                                    $i,
                                    $data_base->format('Y-m-d')
                                ));
                            }
                        }
                    }
                    
                    // Cria as parcelas baseadas no tipo de cálculo
                    for ($i = 1; $i <= $quantidade_parcelas; $i++) {
                        if ($i > 1) {
                            if ($tipo_vencimento === 'anual') {
                                // Para vencimento anual, avançar um ano mantendo o mesmo dia e mês
                                $data_base->modify('+1 year');
                                
                                // Verificar se o dia ainda é válido (caso de ano bissexto)
                                $ultimo_dia = cal_days_in_month(
                                    CAL_GREGORIAN,
                                    $mes_vencimento,
                                    $data_base->format('Y')
                                );
                                
                                if ($dia_vencimento > $ultimo_dia) {
                                    // Se o dia especificado for maior que o último dia do mês, usar o último dia
                                    $data_base = new DateTime(sprintf(
                                        '%d-%02d-%02d 12:00:00',
                                        $data_base->format('Y'),
                                        $mes_vencimento,
                                        $ultimo_dia
                                    ));
                                } else {
                                    $data_base = new DateTime(sprintf(
                                        '%d-%02d-%02d 12:00:00',
                                        $data_base->format('Y'),
                                        $mes_vencimento,
                                        $dia_vencimento
                                    ));
                                }
                            } else {
                                // Para vencimento mensal, avançar um mês
                                $data_base->modify('first day of next month');
                                
                                // Verificar se o dia é válido para o novo mês
                                $ultimo_dia = cal_days_in_month(
                                    CAL_GREGORIAN,
                                    $data_base->format('m'),
                                    $data_base->format('Y')
                                );
                                
                                // Se o dia de vencimento for maior que o último dia do mês
                                if ($dia_vencimento > $ultimo_dia) {
                                    error_log(sprintf(
                                        "Mês %s/%s tem apenas %d dias. Ajustando vencimento.",
                                        $data_base->format('m'),
                                        $data_base->format('Y'),
                                        $ultimo_dia
                                    ));
                                    
                                    // Usar o último dia do mês
                                    $data_base = new DateTime(sprintf(
                                        '%d-%02d-%02d 12:00:00',
                                        $data_base->format('Y'),
                                        $data_base->format('m'),
                                        $ultimo_dia
                                    ));
                                } else {
                                    // Usar o dia de vencimento normal
                                    $data_base = new DateTime(sprintf(
                                        '%d-%02d-%02d 12:00:00',
                                        $data_base->format('Y'),
                                        $data_base->format('m'),
                                        $dia_vencimento
                                    ));
                                }
                                
                                error_log(sprintf(
                                    "Parcela %d: Data de vencimento definida para %s",
                                    $i,
                                    $data_base->format('Y-m-d')
                                ));
                            }
                        }
                        
                        // Determinar o valor da parcela com base no tipo de cálculo
                        $valor_parcela_atual = $valor_parcela;
                        $data_vencimento_final = $data_base->format('Y-m-d');
                        
                        if ($tipo_calculo === 'manual' && !empty($parcelas_manuais)) {
                            // Encontrar a parcela correspondente pelo número
                            $parcela_encontrada = null;
                            foreach ($parcelas_manuais as $parcela) {
                                if (isset($parcela['numero']) && $parcela['numero'] == $i) {
                                    $parcela_encontrada = $parcela;
                                    error_log("Encontrada parcela manual #{$i}: " . print_r($parcela_encontrada, true));
                                    break;
                                }
                            }
                            
                            if ($parcela_encontrada) {
                                // Usar o valor da parcela manual se estiver disponível e for maior que zero
                                if (isset($parcela_encontrada['valor'])) {
                                    $valor_parcela_atual = floatval($parcela_encontrada['valor']);
                                    error_log("Parcela {$i}: Valor alterado para {$valor_parcela_atual}");
                                } else {
                                    error_log("Parcela {$i}: Valor não encontrado na parcela manual, usando valor calculado {$valor_parcela_atual}");
                                }
                                
                                // Usar a data de vencimento manual se estiver disponível
                                if (isset($parcela_encontrada['data_vencimento']) && !empty($parcela_encontrada['data_vencimento'])) {
                                    $data_vencimento_final = $parcela_encontrada['data_vencimento'];
                                }
                            } else {
                                error_log("Não foi encontrada parcela manual para a parcela #{$i}");
                            }
                        }
                        
                        $stmt = $pdo->prepare("
                            INSERT INTO cbp_parcelas_acordo (
                                acordo_id,
                                numero_parcela,
                                data_vencimento,
                                valor_parcela,
                                status,
                                created_at,
                                updated_at
                            ) VALUES (?, ?, ?, ?, 'PENDENTE', NOW(), NOW())
                        ");
                        
                        $stmt->execute([
                            $acordo_id,
                            $i,
                            $data_vencimento_final,
                            $valor_parcela_atual
                        ]);
                    }
                } else if ($acordo['valor_acordo'] != $valor_acordo || $acordo['valor_entrada'] != $valor_entrada || $acordo['alvara_id'] != $alvara_id || $tipo_calculo === 'manual') {
                    // Se o valor do acordo ou da entrada mudou, ou tipo de cálculo é manual, atualizar o valor das parcelas
                    $valor_restante = $valor_acordo - $valor_total_entrada;
                    $valor_parcela = $valor_restante / $quantidade_parcelas;
                    
                    // Buscar parcelas pendentes existentes
                    $stmt = $pdo->prepare("
                        SELECT id, numero_parcela, data_vencimento 
                        FROM cbp_parcelas_acordo 
                        WHERE acordo_id = ? 
                        AND status = 'PENDENTE'
                        ORDER BY numero_parcela
                    ");
                    $stmt->execute([$acordo_id]);
                    $parcelas_pendentes = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    // Atualizar cada parcela pendente individualmente
                    foreach ($parcelas_pendentes as $parcela) {
                        $numero_parcela = $parcela['numero_parcela'];
                        $valor_parcela_atual = $valor_parcela;
                        $data_vencimento = $parcela['data_vencimento']; // Manter a data original por padrão
                        
                        // Para cálculo manual, buscar valor específico da parcela
                        if ($tipo_calculo === 'manual' && !empty($parcelas_manuais)) {
                            // Encontrar a parcela correspondente pelo número
                            $parcela_encontrada = null;
                            foreach ($parcelas_manuais as $p) {
                                if (isset($p['numero']) && $p['numero'] == $numero_parcela) {
                                    $parcela_encontrada = $p;
                                    break;
                                }
                            }
                            
                            if ($parcela_encontrada && isset($parcela_encontrada['valor']) && $parcela_encontrada['valor'] > 0) {
                                $valor_parcela_atual = floatval($parcela_encontrada['valor']);
                                error_log("Edição - Parcela {$numero_parcela}: Valor alterado para {$valor_parcela_atual}");
                            }
                            
                            // Se estiver disponível, usar a data de vencimento personalizada
                            if ($parcela_encontrada && isset($parcela_encontrada['data_vencimento']) && !empty($parcela_encontrada['data_vencimento'])) {
                                $data_vencimento = $parcela_encontrada['data_vencimento'];
                            }
                        }
                        
                        // Atualizar a parcela com os valores específicos
                        $stmt_update = $pdo->prepare("
                            UPDATE cbp_parcelas_acordo 
                            SET valor_parcela = ?, 
                                data_vencimento = ?,
                                updated_at = NOW()
                            WHERE id = ?
                        ");
                        
                        $stmt_update->execute([
                            $valor_parcela_atual,
                            $data_vencimento,
                            $parcela['id']
                        ]);
                    }
                }
                
                // Atualizar acordo
                $stmt = $pdo->prepare("
                    UPDATE cbp_acordos SET
                        data_acordo = ?,
                        valor_acordo = ?,
                        valor_entrada = ?,
                        alvara_id = ?,
                        numero_repactuacao = ?,
                        conta_corrente = ?,
                        numero_contato = ?,
                        quantidade_parcelas = ?,
                        assuncao_divida = ?,
                        assuntor_id = ?,
                        observacoes = ?,
                        status_id = ?,
                        dia_vencimento = ?,
                        tipo_calculo = ?,
                        personalizar_parcelas = ?,
                        tipo_personalizacao = ?,
                        usar_honorario_personalizado = ?,
                        porcentagem_honorario_personalizada = ?,
                        updated_at = NOW()
                    WHERE id = ?
                ");
                
                $stmt->execute([
                    $data_acordo,
                    $valor_acordo,
                    $valor_entrada,
                    $alvara_id,
                    $numero_repactuacao,
                    $conta_corrente,
                    $numero_contato,
                    $quantidade_parcelas,
                    $assuncao_divida ? 1 : 0,
                    $assuntor_id,
                    $observacoes,
                    $status_vigente,
                    $dia_vencimento,
                    $tipo_calculo,
                    $personalizar_parcelas ? 1 : 0,
                    $tipo_personalizacao,
                    $usar_honorario_personalizado ? 1 : 0,
                    $usar_honorario_personalizado ? $porcentagem_honorario_personalizada : null,
                    $acordo_id
                ]);
                
                // Atualizar observação do alvará se usado (em vez de atualizar status que não existe)
                if ($alvara_id) {
                    $stmt = $pdo->prepare("UPDATE cbp_alvaras SET observacoes = 'Utilizado no acordo #" . $acordo_id . "', updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$alvara_id]);
                }
                
                // Se havia um alvará anterior e foi removido ou alterado, atualizar observação do alvará anterior
                if ($alvara_anterior_id && $alvara_anterior_id != $alvara_id) {
                    $stmt = $pdo->prepare("UPDATE cbp_alvaras SET observacoes = 'Disponível - Removido do acordo #" . $acordo_id . "', updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$alvara_anterior_id]);
                }
                
                // Registrar no log
                $detalhes = "Edição de acordo - Processo ID: $processo_id - Valor: R$ " . number_format($valor_acordo, 2, ',', '.') . 
                           " - Parcelas: $quantidade_parcelas" . ($assuncao_divida ? " - Com assunção de dívida" : "");
                $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
                $stmt->execute([$_SESSION['user_id'], 'Edição de Acordo', $detalhes]);

                // Commit
                $pdo->commit();
                
                // Retornar sucesso
                $response = [
                    'success' => true,
                    'message' => 'Acordo atualizado com sucesso.'
                ];
                
                echo json_encode($response);
            } catch (Exception $e) {
                $pdo->rollBack();
                
                $response = [
                    'success' => false,
                    'message' => $e->getMessage()
                ];
                
                echo json_encode($response);
            }
            break;

        case 'excluir_acordo':
            try {
                $pdo->beginTransaction();

                $acordo_id = filter_input(INPUT_POST, 'acordo_id', FILTER_VALIDATE_INT);
                $justificativa = htmlspecialchars($_POST['justificativa'] ?? '', ENT_QUOTES, 'UTF-8');

                // Buscar informações do acordo antes de excluir
                $stmt = $pdo->prepare("SELECT a.*, p.numero_processo, p.id as processo_id FROM cbp_acordos a 
                                      LEFT JOIN cbp_processos_judiciais p ON a.processo_id = p.id 
                                      WHERE a.id = ?");
                $stmt->execute([$acordo_id]);
                $acordo = $stmt->fetch();

                if (!$acordo) {
                    throw new Exception('Acordo não encontrado');
                }

                // Primeiro excluir as parcelas do acordo
                $stmt = $pdo->prepare("DELETE FROM cbp_parcelas_acordo WHERE acordo_id = ?");
                $stmt->execute([$acordo_id]);

                // Depois excluir os alvarás a receber do acordo
                $stmt = $pdo->prepare("DELETE FROM cbp_alvaras_acordo WHERE acordo_id = ?");
                $stmt->execute([$acordo_id]);

                // Por fim, excluir o acordo
                $stmt = $pdo->prepare("DELETE FROM cbp_acordos WHERE id = ?");
                $stmt->execute([$acordo_id]);

                // Verificar se ainda existem acordos ativos para este processo
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as total_acordos 
                    FROM cbp_acordos 
                    WHERE processo_id = ? 
                    AND ativo = 1
                ");
                $stmt->execute([$acordo['processo_id']]);
                $result = $stmt->fetch(PDO::FETCH_ASSOC);

                // Se não houver mais acordos ativos, atualizar o status do processo para VIGENTE
                if ($result['total_acordos'] == 0) {
                    $ID_VIGENTE = 1; // Status fixo para VIGENTE
                    $stmt = $pdo->prepare("
                        UPDATE cbp_processos_judiciais 
                        SET status_id = ?,
                            updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$ID_VIGENTE, $acordo['processo_id']]);
                    error_log("Status do processo atualizado para VIGENTE após exclusão do último acordo");
                }

                // Registrar no log
                $detalhes = "Exclusão de acordo - Processo: " . $acordo['numero_processo'] . 
                           " - Valor: R$ " . number_format($acordo['valor_acordo'], 2, ',', '.') . 
                           " - Justificativa: " . $justificativa;
                
                $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
                $stmt->execute([$_SESSION['user_id'], 'Exclusão de Acordo', $detalhes]);

                $pdo->commit();
                echo json_encode(['success' => true, 'message' => 'Acordo excluído com sucesso']);

            } catch (Exception $e) {
                $pdo->rollBack();
                error_log("Erro ao excluir acordo: " . $e->getMessage());
                echo json_encode(['success' => false, 'message' => $e->getMessage()]);
            }
            break;

        case 'inativar_acordo':
            try {
                $pdo->beginTransaction();

                $acordo_id = filter_input(INPUT_POST, 'acordo_id', FILTER_VALIDATE_INT);
                
                // Buscar informações do acordo antes de inativar
                $stmt = $pdo->prepare("SELECT a.*, p.numero_processo FROM cbp_acordos a 
                                      LEFT JOIN cbp_processos_judiciais p ON a.processo_id = p.id 
                                      WHERE a.id = ?");
                $stmt->execute([$acordo_id]);
                $acordo = $stmt->fetch();

                if (!$acordo) {
                    throw new Exception('Acordo não encontrado');
                }

                // Inativar o acordo
                $stmt = $pdo->prepare("UPDATE cbp_acordos SET ativo = 0 WHERE id = ?");
                $stmt->execute([$acordo_id]);

                // Registrar no log
                $detalhes = "Inativação de acordo - Processo: " . $acordo['numero_processo'] . 
                           " - Valor: R$ " . number_format($acordo['valor_acordo'], 2, ',', '.');
                
                $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
                $stmt->execute([$_SESSION['user_id'], 'Inativação de Acordo', $detalhes]);

                $pdo->commit();
                echo json_encode(['status' => 'success']);

            } catch (Exception $e) {
                $pdo->rollBack();
                error_log("Erro ao inativar acordo: " . $e->getMessage());
                echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
            }
            break;

        case 'registrar_recebimento_alvara':
            try {
                $pdo->beginTransaction();

                $alvara_id = filter_input(INPUT_POST, 'alvara_id', FILTER_VALIDATE_INT);
                $data_recebimento = filter_input(INPUT_POST, 'data_recebimento', FILTER_SANITIZE_STRING);
                $observacoes = filter_input(INPUT_POST, 'observacoes', FILTER_SANITIZE_STRING);
                $valor_alvara = null;
                if (isset($_POST['valor_alvara'])) {
                    $valor_str = trim($_POST['valor_alvara']);
                    // Se vier no formato brasileiro (4.750,00), converte para 4750.00
                    if (preg_match('/^[\d\.]+,\d{2}$/', $valor_str)) {
                        $valor_str = str_replace('.', '', $valor_str); // remove milhar
                        $valor_str = str_replace(',', '.', $valor_str); // vírgula para ponto
                    }
                    $valor_alvara = floatval($valor_str);
                }

                if (!$alvara_id || !$data_recebimento) {
                    throw new Exception('Dados incompletos para registrar o recebimento do alvará');
                }

                // Buscar informações do alvará
                $stmt = $pdo->prepare("
                    SELECT * FROM cbp_alvaras_acordo 
                    WHERE id = ? AND status = 'PENDENTE'
                ");
                $stmt->execute([$alvara_id]);
                $alvara = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$alvara) {
                    throw new Exception('Alvará não encontrado ou já recebido');
                }

                // Atualizar o status do alvará para RECEBIDO e o valor, se informado
                if ($valor_alvara !== null && $valor_alvara > 0) {
                    $stmt = $pdo->prepare("
                        UPDATE cbp_alvaras_acordo 
                        SET status = 'RECEBIDO',
                            data_recebimento = ?,
                            observacoes = ?,
                            valor = ?,
                            updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$data_recebimento, $observacoes, $valor_alvara, $alvara_id]);
                } else {
                    $stmt = $pdo->prepare("
                        UPDATE cbp_alvaras_acordo 
                        SET status = 'RECEBIDO',
                            data_recebimento = ?,
                            observacoes = ?,
                            updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$data_recebimento, $observacoes, $alvara_id]);
                }

                // Registrar honorário usando debug_alvaras_acordos.php
                $alvara_id_para_debug = $alvara_id;
                include_once(dirname(__DIR__) . '/debug_alvaras_acordos.php');

                $pdo->commit();

                echo json_encode([
                    'success' => true,
                    'message' => 'Recebimento do alvará registrado com sucesso!'
                ]);

            } catch (Exception $e) {
                $pdo->rollBack();
                error_log("Erro ao registrar recebimento de alvará: " . $e->getMessage());
                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
            }
            break;

        default:
            throw new Exception('Ação inválida');
    }
} catch (Exception $e) {
    ob_clean();
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
    exit;
} 