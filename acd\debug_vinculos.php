<?php
/**
 * Script para debugar a estrutura dos vínculos
 */

require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

session_start();
if (!isset($_SESSION['user_id'])) {
    die("Acesso negado. Faça login para continuar.");
}

if (!checkACDPermission($_SESSION['user_id'])) {
    die("Acesso negado. Você não tem permissão para acessar o sistema ACD.");
}

echo "<h2>🔍 Debug dos Vínculos</h2>";

try {
    // Verificar se o arquivo de funções existe
    if (!file_exists('vinculos_functions.php')) {
        echo "<p>❌ Arquivo vinculos_functions.php não encontrado!</p>";
        echo "<p><a href='criar_funcoes_historico_antiga.php'>🔧 Criar Funções</a></p>";
        exit;
    }
    
    // Incluir funções
    include 'vinculos_functions.php';
    
    echo "<h3>📋 Testando Função buscarVinculosAtivos()</h3>";
    
    // Testar função
    $vinculos = buscarVinculosAtivos();
    
    echo "<p>Total de vínculos retornados: " . count($vinculos) . "</p>";
    
    if (!empty($vinculos)) {
        echo "<h4>🔍 Estrutura do Primeiro Vínculo:</h4>";
        $primeiro_vinculo = $vinculos[0];
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'><th>Chave</th><th>Valor</th><th>Tipo</th></tr>";
        
        foreach ($primeiro_vinculo as $chave => $valor) {
            echo "<tr>";
            echo "<td><strong>$chave</strong></td>";
            echo "<td>" . htmlspecialchars($valor ?? 'NULL') . "</td>";
            echo "<td>" . gettype($valor) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Verificar chaves específicas
        echo "<h4>✅ Verificação de Chaves Necessárias:</h4>";
        $chaves_necessarias = ['id', 'usuario_id', 'pa_id', 'usuario_nome', 'pa_nome', 'data_inicio'];
        
        echo "<ul>";
        foreach ($chaves_necessarias as $chave) {
            $existe = array_key_exists($chave, $primeiro_vinculo);
            $icone = $existe ? "✅" : "❌";
            $valor = $existe ? $primeiro_vinculo[$chave] : "NÃO EXISTE";
            echo "<li>$icone <strong>$chave:</strong> $valor</li>";
        }
        echo "</ul>";
        
        // Mostrar todos os vínculos
        echo "<h4>📊 Todos os Vínculos:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th>ID</th><th>Usuario ID</th><th>PA ID</th><th>Usuario Nome</th><th>PA Nome</th><th>Data Início</th>";
        echo "</tr>";
        
        foreach ($vinculos as $vinculo) {
            echo "<tr>";
            echo "<td>" . ($vinculo['id'] ?? 'N/A') . "</td>";
            echo "<td>" . ($vinculo['usuario_id'] ?? 'N/A') . "</td>";
            echo "<td>" . ($vinculo['pa_id'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($vinculo['usuario_nome'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($vinculo['pa_nome'] ?? 'N/A') . "</td>";
            echo "<td>" . ($vinculo['data_inicio'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } else {
        echo "<p>⚠️ Nenhum vínculo encontrado.</p>";
        
        // Verificar dados na tabela diretamente
        echo "<h4>🔍 Verificação Direta na Tabela:</h4>";
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM acd_usuario_pa");
        $total_registros = $stmt->fetchColumn();
        echo "<p>Total de registros na tabela: $total_registros</p>";
        
        if ($total_registros > 0) {
            echo "<p>Primeiros registros da tabela:</p>";
            $stmt = $pdo->query("SELECT * FROM acd_usuario_pa LIMIT 3");
            $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($registros)) {
                echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                echo "<tr style='background: #f8f9fa;'>";
                foreach (array_keys($registros[0]) as $campo) {
                    echo "<th>$campo</th>";
                }
                echo "</tr>";
                
                foreach ($registros as $registro) {
                    echo "<tr>";
                    foreach ($registro as $valor) {
                        echo "<td>" . htmlspecialchars($valor ?? 'NULL') . "</td>";
                    }
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
    }
    
    // Testar outras funções
    echo "<h3>📋 Testando Outras Funções</h3>";
    
    // buscarPAsSemResponsavel
    echo "<h4>🏢 PAs sem Responsável:</h4>";
    try {
        $pas_sem_responsavel = buscarPAsSemResponsavel();
        echo "<p>Total: " . count($pas_sem_responsavel) . "</p>";
        
        if (!empty($pas_sem_responsavel)) {
            echo "<ul>";
            foreach (array_slice($pas_sem_responsavel, 0, 5) as $pa) {
                echo "<li>" . htmlspecialchars($pa['numero'] . ' - ' . $pa['nome']) . "</li>";
            }
            if (count($pas_sem_responsavel) > 5) {
                echo "<li>... e mais " . (count($pas_sem_responsavel) - 5) . " PAs</li>";
            }
            echo "</ul>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Erro: " . $e->getMessage() . "</p>";
    }
    
    // Verificar se função verificarResponsavelAtivo existe
    echo "<h4>🔍 Função verificarResponsavelAtivo:</h4>";
    if (function_exists('verificarResponsavelAtivo')) {
        echo "<p>✅ Função existe</p>";
        
        // Testar com primeiro PA
        $stmt = $pdo->query("SELECT id FROM pontos_atendimento LIMIT 1");
        $primeiro_pa = $stmt->fetchColumn();
        
        if ($primeiro_pa) {
            try {
                $responsavel = verificarResponsavelAtivo($primeiro_pa);
                if ($responsavel) {
                    echo "<p>✅ PA $primeiro_pa tem responsável: " . htmlspecialchars($responsavel['nome_completo']) . "</p>";
                } else {
                    echo "<p>ℹ️ PA $primeiro_pa não tem responsável ativo</p>";
                }
            } catch (Exception $e) {
                echo "<p>❌ Erro ao testar: " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p>❌ Função não existe</p>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Erro:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<br><a href='gerenciar_vinculos.php'>← Voltar para Gerenciar Vínculos</a>";
?>

<style>
table { font-size: 12px; }
th, td { padding: 8px; text-align: left; }
</style>
