-- Renomear a coluna cpf para documento
ALTER TABLE acd_formularios CHANGE COLUMN cpf documento VARCHAR(14) NOT NULL;

-- Atualizar o índice se existir
DROP INDEX IF EXISTS idx_cpf ON acd_formularios;
CREATE INDEX idx_documento ON acd_formularios (documento);

-- Adicionar coluna usuario_id
ALTER TABLE acd_formularios
ADD COLUMN usuario_id INT NOT NULL AFTER id,
ADD CONSTRAINT fk_acd_formularios_usuario
FOREIGN KEY (usuario_id) REFERENCES usuarios(id);

-- ========================================
-- MIGRAÇÃO DA TABELA acd_usuario_pa
-- ========================================

-- Verificar se a tabela existe com a estrutura antiga
-- Se existir, fazer backup e migrar para nova estrutura

-- 1. Criar backup da tabela atual (se existir)
CREATE TABLE IF NOT EXISTS acd_usuario_pa_backup AS
SELECT * FROM acd_usuario_pa WHERE 1=0;

-- 2. Fazer backup dos dados existentes (se houver)
INSERT IGNORE INTO acd_usuario_pa_backup
SELECT * FROM acd_usuario_pa;

-- 3. Remover a tabela antiga
DROP TABLE IF EXISTS acd_usuario_pa;

-- 4. Criar nova estrutura da tabela com histórico
CREATE TABLE acd_usuario_pa (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NOT NULL,
    pa_id INT NOT NULL,
    data_inicio DATE NOT NULL,
    data_fim DATE NULL,
    status ENUM('ativo', 'inativo') DEFAULT 'ativo',
    criado_por INT NOT NULL,
    criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    desativado_por INT NULL,
    desativado_em TIMESTAMP NULL,
    observacoes TEXT NULL,

    -- Índices para performance
    INDEX idx_usuario_id (usuario_id),
    INDEX idx_pa_id (pa_id),
    INDEX idx_data_inicio (data_inicio),
    INDEX idx_status (status),
    INDEX idx_criado_por (criado_por),
    INDEX idx_desativado_por (desativado_por),

    -- Chaves estrangeiras
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    FOREIGN KEY (pa_id) REFERENCES pontos_atendimento(id) ON DELETE CASCADE,
    FOREIGN KEY (criado_por) REFERENCES usuarios(id),
    FOREIGN KEY (desativado_por) REFERENCES usuarios(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. Migrar dados do backup (se houver)
-- Converter estrutura antiga para nova estrutura
INSERT INTO acd_usuario_pa (
    usuario_id,
    pa_id,
    data_inicio,
    status,
    criado_por,
    criado_em,
    observacoes
)
SELECT
    usuario_id,
    pa_id,
    COALESCE(DATE(data_vinculo), CURDATE()) as data_inicio,
    CASE
        WHEN status = 1 THEN 'ativo'
        ELSE 'inativo'
    END as status,
    COALESCE(usuario_vinculo, 1) as criado_por, -- Usar ID 1 como fallback se usuario_vinculo for NULL
    COALESCE(data_vinculo, NOW()) as criado_em,
    'Migrado da estrutura anterior' as observacoes
FROM acd_usuario_pa_backup
WHERE EXISTS (SELECT 1 FROM usuarios WHERE id = COALESCE(usuario_vinculo, 1));

-- 6. Verificar se a migração foi bem-sucedida
-- SELECT COUNT(*) as registros_migrados FROM acd_usuario_pa;
-- SELECT COUNT(*) as registros_backup FROM acd_usuario_pa_backup;