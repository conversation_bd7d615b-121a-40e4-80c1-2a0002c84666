/* Estilos para a tabela de ranking */
div.table-responsive table.table.table-hover.ranking-table {
    border-collapse: separate;
    border-spacing: 0;
}

div.table-responsive table.table.table-hover.ranking-table thead th {
    color: #fff;
    font-weight: 500;
    border: none;
    padding: 12px 8px;
}

/* Alternância de cores para as linhas da tabela */
div.table-responsive table.table.table-hover.ranking-table tbody tr:nth-child(4n),
div.table-responsive table.table.table-hover.ranking-table tbody tr:nth-child(4n-1) {
    background-color: rgba(0, 174, 157, 0.03) !important;
}

div.table-responsive table.table.table-hover.ranking-table tbody tr:nth-child(4n-2),
div.table-responsive table.table.table-hover.ranking-table tbody tr:nth-child(4n-3) {
    background-color: #ffffff !important;
}

/* Efeito hover mais forte */
div.table-responsive table.table.table-hover.ranking-table tbody tr:hover {
    background-color: rgba(0, 174, 157, 0.1) !important;
    transition: background-color 0.2s ease;
}

div.table-responsive table.table.table-hover.ranking-table tbody tr:hover + tr {
    background-color: rgba(0, 174, 157, 0.1) !important;
    transition: background-color 0.2s ease;
}

/* Mantém o estilo dos links */
div.table-responsive table.table.table-hover.ranking-table a {
    color: inherit;
    text-decoration: none;
}

/* Ajustes para responsividade */
@media (max-width: 768px) {
    div.table-responsive table.table.table-hover.ranking-table {
        font-size: 0.9rem;
    }
} 