CREATE TABLE IF NOT EXISTS acd_limites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    numero_proposta VARCHAR(20) NOT NULL,
    cpf_cnpj VARCHAR(14) NOT NULL,
    nome VARCHAR(255) NOT NULL,
    valor DECIMAL(15,2) NOT NULL,
    prazo_dia INT NOT NULL,
    situacao VARCHAR(50) NOT NULL,
    data_inicio DATE NOT NULL,
    data_fim DATE NOT NULL,
    data_importacao DATETIME DEFAULT CURRENT_TIMESTAMP,
    importado_por VARCHAR(50) NOT NULL,
    INDEX idx_cpf_cnpj (cpf_cnpj),
    INDEX idx_data_periodo (data_inicio, data_fim),
    INDEX idx_numero_proposta (numero_proposta)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 