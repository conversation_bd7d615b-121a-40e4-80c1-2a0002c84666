<?php
/**
 * Script para verificar a estrutura atual da tabela acd_usuario_pa
 * e criar funções compatíveis
 */

require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

session_start();
if (!isset($_SESSION['user_id'])) {
    die("Acesso negado. Faça login para continuar.");
}

if (!checkACDPermission($_SESSION['user_id'])) {
    die("Acesso negado. Você não tem permissão para acessar o sistema ACD.");
}

echo "<h2>🔍 Verificação da Estrutura Atual</h2>";

try {
    // Verificar se a tabela existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'acd_usuario_pa'");
    if ($stmt->rowCount() == 0) {
        echo "<p>❌ Tabela acd_usuario_pa não existe!</p>";
        echo "<p>💡 <strong>Solução:</strong> A tabela precisa ser criada.</p>";
        
        echo "<h3>🏗️ Criar Tabela com Estrutura Simples</h3>";
        echo "<p>Clique no botão abaixo para criar a tabela:</p>";
        
        if (isset($_POST['criar_tabela'])) {
            echo "<p>🔨 Criando tabela...</p>";
            
            $sql_criar = "
            CREATE TABLE acd_usuario_pa (
                id INT AUTO_INCREMENT PRIMARY KEY,
                usuario_id INT NOT NULL COMMENT 'ID do usuário local',
                pa_id INT NOT NULL COMMENT 'ID do PA',
                data_vinculo DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '1=ativo, 0=inativo',
                
                INDEX idx_usuario_id (usuario_id),
                INDEX idx_pa_id (pa_id),
                INDEX idx_status (status),
                
                FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
                FOREIGN KEY (pa_id) REFERENCES pontos_atendimento(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            COMMENT='Vínculos entre usuários e PAs - Estrutura Simples'";
            
            $pdo->exec($sql_criar);
            echo "<p>✅ Tabela criada com sucesso!</p>";
            echo "<p><a href='gerenciar_vinculos.php'>← Voltar para Gerenciar Vínculos</a></p>";
        } else {
            echo "<form method='POST'>";
            echo "<button type='submit' name='criar_tabela' class='btn btn-primary'>🔨 Criar Tabela</button>";
            echo "</form>";
        }
        
        exit;
    }
    
    echo "<p>✅ Tabela acd_usuario_pa existe</p>";
    
    // Mostrar estrutura atual
    echo "<h3>📋 Estrutura Atual</h3>";
    $stmt = $pdo->query("DESCRIBE acd_usuario_pa");
    $colunas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $campos = [];
    foreach ($colunas as $coluna) {
        $campos[] = $coluna['Field'];
        echo "<tr>";
        echo "<td><strong>{$coluna['Field']}</strong></td>";
        echo "<td>{$coluna['Type']}</td>";
        echo "<td>{$coluna['Null']}</td>";
        echo "<td>{$coluna['Key']}</td>";
        echo "<td>{$coluna['Default']}</td>";
        echo "<td>{$coluna['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Análise da estrutura
    echo "<h3>🔍 Análise da Estrutura</h3>";
    
    $tem_usuario_id = in_array('usuario_id', $campos);
    $tem_usuario_api_id = in_array('usuario_api_id', $campos);
    $tem_usuario_vinculo = in_array('usuario_vinculo', $campos);
    $tem_criado_por = in_array('criado_por', $campos);
    $tem_data_inicio = in_array('data_inicio', $campos);
    $tem_data_vinculo = in_array('data_vinculo', $campos);
    
    echo "<ul>";
    echo "<li>usuario_id: " . ($tem_usuario_id ? "✅ Presente" : "❌ Ausente") . "</li>";
    echo "<li>usuario_api_id: " . ($tem_usuario_api_id ? "✅ Presente" : "❌ Ausente") . "</li>";
    echo "<li>usuario_vinculo: " . ($tem_usuario_vinculo ? "✅ Presente" : "❌ Ausente") . "</li>";
    echo "<li>criado_por: " . ($tem_criado_por ? "✅ Presente" : "❌ Ausente") . "</li>";
    echo "<li>data_inicio: " . ($tem_data_inicio ? "✅ Presente" : "❌ Ausente") . "</li>";
    echo "<li>data_vinculo: " . ($tem_data_vinculo ? "✅ Presente" : "❌ Ausente") . "</li>";
    echo "</ul>";
    
    // Determinar tipo de estrutura
    if ($tem_usuario_api_id) {
        $tipo_estrutura = "Nova (API)";
        $cor = "#d4edda";
    } elseif ($tem_criado_por && $tem_data_inicio) {
        $tipo_estrutura = "Nova (Histórico)";
        $cor = "#d4edda";
    } elseif ($tem_usuario_vinculo) {
        $tipo_estrutura = "Antiga (com usuario_vinculo)";
        $cor = "#fff3cd";
    } elseif ($tem_usuario_id) {
        $tipo_estrutura = "Simples (básica)";
        $cor = "#d1ecf1";
    } else {
        $tipo_estrutura = "Desconhecida";
        $cor = "#f8d7da";
    }
    
    echo "<div style='background: $cor; border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4>📊 Tipo de Estrutura: $tipo_estrutura</h4>";
    echo "</div>";
    
    // Verificar dados existentes
    echo "<h3>📊 Dados Existentes</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) FROM acd_usuario_pa");
    $total_registros = $stmt->fetchColumn();
    echo "<p>Total de registros: <strong>$total_registros</strong></p>";
    
    if ($total_registros > 0) {
        echo "<p>Primeiros 5 registros:</p>";
        $stmt = $pdo->query("SELECT * FROM acd_usuario_pa LIMIT 5");
        $registros = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        if (!empty($registros)) {
            echo "<tr style='background: #f8f9fa;'>";
            foreach (array_keys($registros[0]) as $campo) {
                echo "<th>$campo</th>";
            }
            echo "</tr>";
            
            foreach ($registros as $registro) {
                echo "<tr>";
                foreach ($registro as $valor) {
                    echo "<td>" . htmlspecialchars($valor ?? '') . "</td>";
                }
                echo "</tr>";
            }
        }
        echo "</table>";
    }
    
    // Verificar constraints
    echo "<h3>🔗 Constraints</h3>";
    $stmt = $pdo->query("
        SELECT 
            CONSTRAINT_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'acd_usuario_pa' 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    $constraints = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($constraints)) {
        echo "<p>Nenhuma constraint encontrada.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'><th>Nome</th><th>Coluna</th><th>Tabela Ref.</th><th>Coluna Ref.</th></tr>";
        foreach ($constraints as $constraint) {
            $cor_linha = ($constraint['CONSTRAINT_NAME'] == 'acd_usuario_pa_ibfk_3') ? 'background: #f8d7da;' : '';
            echo "<tr style='$cor_linha'>";
            echo "<td>{$constraint['CONSTRAINT_NAME']}</td>";
            echo "<td>{$constraint['COLUMN_NAME']}</td>";
            echo "<td>{$constraint['REFERENCED_TABLE_NAME']}</td>";
            echo "<td>{$constraint['REFERENCED_COLUMN_NAME']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Recomendações
    echo "<h3>💡 Recomendações</h3>";
    
    if ($tem_usuario_api_id) {
        echo "<p>✅ Sua estrutura está atualizada para trabalhar com API.</p>";
        echo "<p>🔧 <a href='atualizar_funcoes_api.php'>Atualizar funções para API</a></p>";
    } elseif ($tem_usuario_vinculo) {
        echo "<p>⚠️ Estrutura antiga detectada.</p>";
        echo "<p>🔧 <a href='correcao_rapida_constraint.php'>Aplicar correção rápida</a></p>";
        echo "<p>🏗️ <a href='reestruturar_vinculos_api.php'>Migrar para estrutura nova</a></p>";
    } elseif ($tem_usuario_id) {
        echo "<p>ℹ️ Estrutura simples detectada.</p>";
        echo "<p>🔧 <a href='criar_funcoes_simples.php'>Criar funções compatíveis</a></p>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Erro:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "<br><a href='gerenciar_vinculos.php'>← Voltar para Gerenciar Vínculos</a>";
?>

<style>
table { font-size: 12px; }
th, td { padding: 8px; text-align: left; }
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    background: #007bff; 
    color: white; 
    text-decoration: none; 
    border-radius: 4px; 
    border: none;
    cursor: pointer;
}
.btn:hover { background: #0056b3; }
</style>
