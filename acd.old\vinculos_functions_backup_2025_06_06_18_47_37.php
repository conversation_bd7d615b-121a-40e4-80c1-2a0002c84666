<?php
/**
 * Funções para gerenciar vínculos com histórico
 * Compatível com estrutura antiga (usuario_id) e nova (usuario_api_id)
 */

// Função para buscar usuários da API
function buscarTodosUsuarios() {
    $apiFields = [
        'api_user' => 'UFL7GXZ14LU9NOR',
        'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
        'api_module' => 'Usuarios',
        'api_action' => 'listarUsuarios'
    ];

    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => http_build_query($apiFields),
    ));

    $response = curl_exec($curl);
    curl_close($curl);

    if (!$response) return [];

    $usuarios = json_decode($response, true);
    return is_array($usuarios) ? $usuarios : [];
}

// Função para detectar estrutura da tabela
function detectarEstruturaTabelaVinculos() {
    global $pdo;

    try {
        $stmt = $pdo->query("DESCRIBE acd_usuario_pa");
        $colunas = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $campos = array_column($colunas, 'Field');

        return [
            'tem_usuario_id' => in_array('usuario_id', $campos),
            'tem_usuario_api_id' => in_array('usuario_api_id', $campos),
            'tem_usuario_vinculo' => in_array('usuario_vinculo', $campos),
            'tem_criado_por' => in_array('criado_por', $campos),
            'campos' => $campos
        ];
    } catch (Exception $e) {
        return [
            'tem_usuario_id' => false,
            'tem_usuario_api_id' => false,
            'tem_usuario_vinculo' => false,
            'tem_criado_por' => false,
            'campos' => []
        ];
    }
}

/**
 * Criar novo vínculo entre usuário e PA
 * REGRA: Cada PA pode ter apenas UM responsável ativo por vez
 */
function criarVinculo($usuario_id, $pa_id, $data_inicio, $criado_por, $observacoes = null) {
    global $pdo;

    try {
        $pdo->beginTransaction();

        // 1. Verificar se já existe responsável ativo para este PA
        $sql_verificar = "
            SELECT v.id, v.usuario_id, u.nome_completo as usuario_nome
            FROM acd_usuario_pa v
            INNER JOIN usuarios u ON v.usuario_id = u.id
            WHERE v.pa_id = ?
            AND v.status = 'ativo'
            AND v.data_inicio <= ?
            AND (v.data_fim IS NULL OR v.data_fim > ?)
        ";

        $stmt = $pdo->prepare($sql_verificar);
        $stmt->execute([$pa_id, $data_inicio, $data_inicio]);
        $responsavel_atual = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($responsavel_atual && $responsavel_atual['usuario_id'] != $usuario_id) {
            $pdo->rollBack();
            return [
                'sucesso' => false,
                'erro' => "PA já possui responsável ativo: {$responsavel_atual['usuario_nome']}. Desative o vínculo atual primeiro."
            ];
        }

        // 2. Desativar TODOS os vínculos ativos para este PA (garantir responsável único)
        $sql_desativar_pa = "
            UPDATE acd_usuario_pa
            SET status = 'inativo',
                data_fim = ?,
                desativado_por = ?,
                desativado_em = NOW(),
                observacoes = CONCAT(IFNULL(observacoes, ''), ' | Substituído por novo responsável')
            WHERE pa_id = ?
            AND status = 'ativo'
        ";

        $stmt = $pdo->prepare($sql_desativar_pa);
        $stmt->execute([$data_inicio, $criado_por, $pa_id]);
        $vinculos_desativados = $stmt->rowCount();

        // 3. Criar novo vínculo
        $sql_criar = "
            INSERT INTO acd_usuario_pa (
                usuario_id, pa_id, data_inicio, status,
                criado_por, criado_em, observacoes
            ) VALUES (?, ?, ?, 'ativo', ?, NOW(), ?)
        ";

        $stmt = $pdo->prepare($sql_criar);
        $stmt->execute([$usuario_id, $pa_id, $data_inicio, $criado_por, $observacoes]);

        $novo_vinculo_id = $pdo->lastInsertId();

        $pdo->commit();

        $mensagem = 'Vínculo criado com sucesso';
        if ($vinculos_desativados > 0) {
            $mensagem .= " ($vinculos_desativados vínculo(s) anterior(es) desativado(s))";
        }

        return [
            'sucesso' => true,
            'vinculo_id' => $novo_vinculo_id,
            'mensagem' => $mensagem,
            'vinculos_desativados' => $vinculos_desativados
        ];

    } catch (Exception $e) {
        $pdo->rollBack();
        return [
            'sucesso' => false,
            'erro' => $e->getMessage()
        ];
    }
}

/**
 * Desativar vínculo existente
 */
function desativarVinculo($vinculo_id, $data_fim, $desativado_por, $observacoes = null) {
    global $pdo;
    
    try {
        $sql = "
            UPDATE acd_usuario_pa 
            SET status = 'inativo',
                data_fim = ?,
                desativado_por = ?,
                desativado_em = NOW(),
                observacoes = CONCAT(IFNULL(observacoes, ''), ' | ', ?)
            WHERE id = ? 
            AND status = 'ativo'
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$data_fim, $desativado_por, $observacoes, $vinculo_id]);
        
        if ($stmt->rowCount() > 0) {
            return [
                'sucesso' => true,
                'mensagem' => 'Vínculo desativado com sucesso'
            ];
        } else {
            return [
                'sucesso' => false,
                'erro' => 'Vínculo não encontrado ou já inativo'
            ];
        }
        
    } catch (Exception $e) {
        return [
            'sucesso' => false,
            'erro' => $e->getMessage()
        ];
    }
}

/**
 * Buscar vínculos ativos em uma data específica
 */
function buscarVinculosAtivosNaData($data) {
    global $pdo;
    
    $sql = "
        SELECT 
            v.id,
            v.usuario_id,
            v.pa_id,
            v.data_inicio,
            v.data_fim,
            u.nome_completo as usuario_nome,
            u.email as usuario_email,
            p.nome as pa_nome,
            p.numero as pa_numero
        FROM acd_usuario_pa v
        INNER JOIN usuarios u ON v.usuario_id = u.id
        INNER JOIN pontos_atendimento p ON v.pa_id = p.id
        WHERE v.data_inicio <= ?
        AND (v.data_fim IS NULL OR v.data_fim > ?)
        AND v.status = 'ativo'
        ORDER BY p.nome, u.nome_completo
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$data, $data]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Buscar responsável por um PA em uma data específica
 */
function buscarResponsavelPANaData($pa_id, $data) {
    global $pdo;
    
    $sql = "
        SELECT 
            v.id,
            v.usuario_id,
            v.data_inicio,
            v.data_fim,
            u.nome_completo as usuario_nome,
            u.email as usuario_email
        FROM acd_usuario_pa v
        INNER JOIN usuarios u ON v.usuario_id = u.id
        WHERE v.pa_id = ?
        AND v.data_inicio <= ?
        AND (v.data_fim IS NULL OR v.data_fim > ?)
        AND v.status = 'ativo'
        ORDER BY v.data_inicio DESC
        LIMIT 1
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$pa_id, $data, $data]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * Buscar histórico completo de vínculos de um usuário
 */
function buscarHistoricoUsuario($usuario_id) {
    global $pdo;
    
    $sql = "
        SELECT 
            v.*,
            p.nome as pa_nome,
            p.numero as pa_numero,
            uc.nome_completo as criado_por_nome,
            ud.nome_completo as desativado_por_nome
        FROM acd_usuario_pa v
        INNER JOIN pontos_atendimento p ON v.pa_id = p.id
        LEFT JOIN usuarios uc ON v.criado_por = uc.id
        LEFT JOIN usuarios ud ON v.desativado_por = ud.id
        WHERE v.usuario_id = ?
        ORDER BY v.data_inicio DESC, v.criado_em DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$usuario_id]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Buscar histórico completo de vínculos de um PA
 */
function buscarHistoricoPA($pa_id) {
    global $pdo;
    
    $sql = "
        SELECT 
            v.*,
            u.nome_completo as usuario_nome,
            u.email as usuario_email,
            uc.nome_completo as criado_por_nome,
            ud.nome_completo as desativado_por_nome
        FROM acd_usuario_pa v
        INNER JOIN usuarios u ON v.usuario_id = u.id
        LEFT JOIN usuarios uc ON v.criado_por = uc.id
        LEFT JOIN usuarios ud ON v.desativado_por = ud.id
        WHERE v.pa_id = ?
        ORDER BY v.data_inicio DESC, v.criado_em DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$pa_id]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Buscar vínculos ativos atuais
 * Compatível com estrutura antiga e nova
 */
function buscarVinculosAtivos() {
    global $pdo;

    $estrutura = detectarEstruturaTabelaVinculos();

    if ($estrutura['tem_usuario_api_id']) {
        // Nova estrutura - buscar dados da API
        $usuarios_api = buscarTodosUsuarios();
        $usuarios_map = [];
        foreach ($usuarios_api as $usuario) {
            $usuarios_map[$usuario['id']] = $usuario;
        }

        $sql = "
            SELECT
                v.id,
                v.usuario_api_id,
                v.pa_id,
                v.data_inicio,
                p.nome as pa_nome,
                p.numero as pa_numero
            FROM acd_usuario_pa v
            INNER JOIN pontos_atendimento p ON v.pa_id = p.id
            WHERE v.status = 'ativo'
            AND (v.data_fim IS NULL OR v.data_fim > CURDATE())
            ORDER BY p.nome
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $vinculos = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Enriquecer com dados da API
        foreach ($vinculos as &$vinculo) {
            $usuario_api = $usuarios_map[$vinculo['usuario_api_id']] ?? null;
            if ($usuario_api) {
                $vinculo['usuario_nome'] = $usuario_api['nome'] ?? 'Usuário não encontrado';
                $vinculo['usuario_email'] = $usuario_api['email'] ?? '';
            } else {
                $vinculo['usuario_nome'] = 'Usuário não encontrado (ID: ' . $vinculo['usuario_api_id'] . ')';
                $vinculo['usuario_email'] = '';
            }
        }

        return $vinculos;

    } else {
        // Estrutura antiga - usar join com tabela usuarios local
        $sql = "
            SELECT
                v.id,
                v.usuario_id,
                v.pa_id,
                COALESCE(v.data_inicio, DATE(v.data_vinculo)) as data_inicio,
                u.nome_completo as usuario_nome,
                u.email as usuario_email,
                p.nome as pa_nome,
                p.numero as pa_numero
            FROM acd_usuario_pa v
            INNER JOIN usuarios u ON v.usuario_id = u.id
            INNER JOIN pontos_atendimento p ON v.pa_id = p.id
            WHERE (v.status = 'ativo' OR v.status = 1)
            AND (v.data_fim IS NULL)
            ORDER BY p.nome, u.nome_completo
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}

/**
 * Verificar se usuário tem vínculo ativo com PA
 */
function verificarVinculoAtivo($usuario_id, $pa_id, $data = null) {
    global $pdo;

    if ($data === null) {
        $data = date('Y-m-d');
    }

    $sql = "
        SELECT COUNT(*)
        FROM acd_usuario_pa
        WHERE usuario_id = ?
        AND pa_id = ?
        AND data_inicio <= ?
        AND (data_fim IS NULL OR data_fim > ?)
        AND status = 'ativo'
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$usuario_id, $pa_id, $data, $data]);

    return $stmt->fetchColumn() > 0;
}

/**
 * Verificar se PA já tem responsável ativo
 * REGRA: Cada PA pode ter apenas UM responsável ativo
 */
function verificarPATemResponsavel($pa_id, $data = null) {
    global $pdo;

    if ($data === null) {
        $data = date('Y-m-d');
    }

    $sql = "
        SELECT
            v.id,
            v.usuario_id,
            u.nome_completo as usuario_nome,
            u.email as usuario_email,
            v.data_inicio
        FROM acd_usuario_pa v
        INNER JOIN usuarios u ON v.usuario_id = u.id
        WHERE v.pa_id = ?
        AND v.data_inicio <= ?
        AND (v.data_fim IS NULL OR v.data_fim > ?)
        AND v.status = 'ativo'
        ORDER BY v.data_inicio DESC
        LIMIT 1
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute([$pa_id, $data, $data]);

    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * Buscar PAs sem responsável ativo
 * Compatível com estrutura antiga e nova
 */
function buscarPAsSemResponsavel($data = null) {
    global $pdo;

    if ($data === null) {
        $data = date('Y-m-d');
    }

    $estrutura = detectarEstruturaTabelaVinculos();

    if ($estrutura['tem_usuario_api_id']) {
        // Nova estrutura
        $sql = "
            SELECT
                pa.id,
                pa.numero,
                pa.nome
            FROM pontos_atendimento pa
            LEFT JOIN acd_usuario_pa v ON (
                pa.id = v.pa_id
                AND v.data_inicio <= ?
                AND (v.data_fim IS NULL OR v.data_fim > ?)
                AND v.status = 'ativo'
            )
            WHERE v.id IS NULL
            ORDER BY pa.numero
        ";
    } else {
        // Estrutura antiga
        $sql = "
            SELECT
                pa.id,
                pa.numero,
                pa.nome
            FROM pontos_atendimento pa
            LEFT JOIN acd_usuario_pa v ON (
                pa.id = v.pa_id
                AND (v.status = 'ativo' OR v.status = 1)
            )
            WHERE v.id IS NULL
            ORDER BY pa.numero
        ";
    }

    $stmt = $pdo->prepare($sql);
    if ($estrutura['tem_usuario_api_id']) {
        $stmt->execute([$data, $data]);
    } else {
        $stmt->execute();
    }

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Buscar PAs de um usuário em uma data específica
 */
function buscarPAsUsuarioNaData($usuario_id, $data = null) {
    global $pdo;
    
    if ($data === null) {
        $data = date('Y-m-d');
    }
    
    $sql = "
        SELECT 
            p.id,
            p.nome,
            p.numero,
            v.data_inicio
        FROM acd_usuario_pa v
        INNER JOIN pontos_atendimento p ON v.pa_id = p.id
        WHERE v.usuario_id = ?
        AND v.data_inicio <= ?
        AND (v.data_fim IS NULL OR v.data_fim > ?)
        AND v.status = 'ativo'
        ORDER BY p.nome
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$usuario_id, $data, $data]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>
