<?php
require_once '../auth_check.php';
require_once '../config/database.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit;
}

try {
    // Validar dados recebidos
    if (!isset($_POST['data_fila']) || !isset($_POST['quantidade_linhas'])) {
        throw new Exception('Dados incompletos');
    }

    $data_fila = $_POST['data_fila'];
    $quantidade_linhas = intval($_POST['quantidade_linhas']);

    // Validar data
    if (!strtotime($data_fila)) {
        throw new Exception('Data inválida');
    }

    // Validar quantidade
    if ($quantidade_linhas <= 0) {
        throw new Exception('Quantidade de linhas deve ser maior que zero');
    }

    // Inserir registro
    $stmt = $pdo->prepare("
        INSERT INTO cad_fila (data_fila, quantidade_linhas, usuario) 
        VALUES (:data_fila, :quantidade_linhas, :usuario)
    ");

    $stmt->execute([
        ':data_fila' => $data_fila,
        ':quantidade_linhas' => $quantidade_linhas,
        ':usuario' => $_SESSION['user_id']
    ]);

    // Registrar no log
    $stmt_log = $pdo->prepare("
        INSERT INTO logs (usuario_id, acao, detalhes, data_hora) 
        VALUES (:usuario_id, :acao, :detalhes, NOW())
    ");

    $detalhes = "Data da fila: " . date('d/m/Y H:i:s', strtotime($data_fila)) . 
                " | Quantidade de linhas: " . $quantidade_linhas;

    $stmt_log->execute([
        ':usuario_id' => $_SESSION['user_id'],
        ':acao' => 'LANÇAR_FILA',
        ':detalhes' => $detalhes
    ]);

    echo json_encode(['success' => true, 'message' => 'Data da fila lançada com sucesso']);

} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} 