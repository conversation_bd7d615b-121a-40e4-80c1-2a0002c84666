<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

$acordo_id = isset($_GET['acordo_id']) ? intval($_GET['acordo_id']) : 0;

// Buscar informações do acordo
$stmt = $pdo->prepare("
    SELECT 
        a.*,
        p.associado_nome as nome_cliente,
        p.associado_documento as cpf_cnpj,
        p.numero_processo,
        sa.nome as status_nome,
        sa.cor as status_cor,
        COUNT(pa.id) as total_parcelas,
        SUM(CASE WHEN pa.status = 'PAGO' THEN 1 ELSE 0 END) as parcelas_pagas,
        COALESCE(SUM(CASE WHEN pa.status = 'PAGO' THEN pa.valor_pago ELSE 0 END), 0) as total_pago,
        alv.data_recebimento as data_alvara,
        alv.valor as valor_alvara,
        ass.nome as assuntor_nome,
        ass.cpf_cnpj as assuntor_documento,
        (SELECT valor FROM cbp_configuracoes WHERE chave = 'porcentagem_padrao_honorarios' LIMIT 1) as porcentagem_padrao_honorarios
    FROM cbp_acordos a
    INNER JOIN cbp_processos_judiciais p ON a.processo_id = p.id
    LEFT JOIN cbp_status_acordo sa ON a.status_id = sa.id
    LEFT JOIN cbp_parcelas_acordo pa ON a.id = pa.acordo_id
    LEFT JOIN cbp_alvaras alv ON a.alvara_id = alv.id
    LEFT JOIN cbp_associados ass ON a.assuntor_id = ass.id
    WHERE a.id = :acordo_id
    GROUP BY a.id, p.associado_nome, p.associado_documento, p.numero_processo, sa.nome, sa.cor, alv.data_recebimento, alv.valor, ass.nome, ass.cpf_cnpj
");
$stmt->execute(['acordo_id' => $acordo_id]);
$acordo = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$acordo) {
    echo '<div class="alert alert-danger">Acordo não encontrado.</div>';
    exit;
}

// Buscar alvarás a receber
$stmt = $pdo->prepare("
    SELECT * FROM cbp_alvaras_acordo 
    WHERE acordo_id = ? 
    ORDER BY created_at
");
$stmt->execute([$acordo_id]);
$alvaras_receber = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Buscar parcelas
$stmt = $pdo->prepare("
    SELECT *, 
    CASE 
        WHEN status = 'PAGO' THEN 'PAGO'
        WHEN data_vencimento < CURDATE() THEN 'EM ATRASO'
        ELSE 'PENDENTE'
    END as status_exibicao
    FROM cbp_parcelas_acordo 
    WHERE acordo_id = ? 
    ORDER BY numero_parcela
");
$stmt->execute([$acordo_id]);
$parcelas = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<style>
/* Estilos para o modal de acordo quando sobreposto */
#modalVisualizarAcordo {
    background-color: rgba(0, 0, 0, 0.5);
}

#modalVisualizarAcordo .modal-dialog {
    max-width: 800px;
}

#modalVisualizarAcordo .modal-content {
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    border: none;
    border-radius: 8px;
}

#modalVisualizarAcordo .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
}

#modalVisualizarAcordo .modal-body {
    padding: 1.5rem;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 50rem;
    font-size: 0.85rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 100px;
    text-align: center;
    color: #fff;
}

.status-badge.pendente { background-color: #ffc107; }
.status-badge.pago { background-color: #28a745; }
.status-badge.em-atraso { background-color: #dc3545; }
.status-badge.recebido { background-color: #28a745; }
</style>

<!-- Informações do Acordo -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">Informações do Acordo</h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>Cliente:</strong> <?php echo htmlspecialchars($acordo['nome_cliente']); ?></p>
                <p><strong>CPF/CNPJ:</strong> <?php echo htmlspecialchars($acordo['cpf_cnpj']); ?></p>
                <p><strong>Processo:</strong> <?php echo htmlspecialchars($acordo['numero_processo']); ?></p>
                <p><strong>Honorários:</strong> 
                    <?php if ($acordo['usar_honorario_personalizado'] == 1 && !empty($acordo['porcentagem_honorario_personalizada'])): ?>
                        <span class="badge bg-warning">Personalizado: <?php echo number_format($acordo['porcentagem_honorario_personalizada'], 4, ',', '.'); ?>%</span>
                    <?php else: ?>
                        <span class="badge bg-secondary">Padrão</span>
                    <?php endif; ?>
                </p>
                
                <?php if (!empty($acordo['observacoes'])): ?>
                <div class="alert alert-secondary mt-2">
                    <h6 class="mb-1"><i class="fas fa-comment-alt me-2"></i><strong>Observações</strong></h6>
                    <p class="mb-0"><?php echo nl2br(htmlspecialchars($acordo['observacoes'])); ?></p>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($acordo['assuntor_id']) && !empty($acordo['assuntor_nome'])): ?>
                <div class="alert alert-warning mt-3">
                    <h6 class="mb-2"><i class="fas fa-exchange-alt me-2"></i><strong>Assunção de Dívida</strong></h6>
                    <p class="mb-1"><strong>Assuntor:</strong> <?php echo htmlspecialchars($acordo['assuntor_nome']); ?></p>
                    <p class="mb-1"><strong>CPF/CNPJ:</strong> <?php 
                        $doc = $acordo['assuntor_documento'];
                        if (strlen($doc) === 11) {
                            echo substr($doc, 0, 3) . '.' . substr($doc, 3, 3) . '.' . substr($doc, 6, 3) . '-' . substr($doc, 9, 2);
                        } else if (strlen($doc) === 14) {
                            echo substr($doc, 0, 2) . '.' . substr($doc, 2, 3) . '.' . substr($doc, 5, 3) . '/' . substr($doc, 8, 4) . '-' . substr($doc, 12, 2);
                        } else {
                            echo htmlspecialchars($doc);
                        }
                    ?></p>
                </div>
                <?php endif; ?>
            </div>
            <div class="col-md-6">
                <p><strong>Data do Acordo:</strong> <?php echo date('d/m/Y', strtotime($acordo['data_acordo'])); ?></p>
                <p><strong>Valor do Acordo:</strong> R$ <?php echo number_format($acordo['valor_acordo'], 2, ',', '.'); ?></p>
                <?php if (!empty($acordo['numero_repactuacao'])): ?>
                <p><strong>Número de Repactuação:</strong> <?php echo htmlspecialchars($acordo['numero_repactuacao']); ?></p>
                <?php endif; ?>
                
                <p><strong>Conta Corrente:</strong> 
                    <?php if (!empty($acordo['conta_corrente'])): ?>
                        <?php echo htmlspecialchars($acordo['conta_corrente']); ?>
                    <?php else: ?>
                        <span class="badge bg-secondary">Não informada</span>
                    <?php endif; ?>
                </p>
                
                <?php if (!empty($acordo['numero_contato'])): ?>
                <p><strong><i class="fas fa-phone-alt me-1"></i> Contato:</strong> <?php echo htmlspecialchars($acordo['numero_contato']); ?></p>
                <?php endif; ?>
                <p><strong>Quantidade de Parcelas:</strong> <?php echo htmlspecialchars($acordo['quantidade_parcelas']); ?></p>
                
                <?php if ($acordo['valor_entrada'] > 0 || $acordo['alvara_id']): ?>
                <div class="alert alert-info mt-3">
                    <p class="mb-1"><strong>Entrada:</strong></p>
                    <?php if ($acordo['valor_entrada'] > 0 && $acordo['alvara_id']): ?>
                        <p class="mb-1">Valor: R$ <?php echo number_format($acordo['valor_entrada'], 2, ',', '.'); ?></p>
                        <p class="mb-1">Alvará:</p>
                        <ul class="mb-0">
                            <li>Data: <?php echo date('d/m/Y', strtotime($acordo['data_alvara'])); ?></li>
                            <li>Valor: R$ <?php echo number_format($acordo['valor_alvara'], 2, ',', '.'); ?></li>
                        </ul>
                    <?php elseif ($acordo['alvara_id']): ?>
                        <p class="mb-1">Data do Alvará: <?php echo (isset($acordo['data_alvara']) && !empty($acordo['data_alvara'])) ? date('d/m/Y', strtotime($acordo['data_alvara'])) : 'Não informada'; ?></p>
                        <p class="mb-1">Valor do Alvará: R$ <?php echo (isset($acordo['valor_alvara'])) ? number_format($acordo['valor_alvara'], 2, ',', '.') : '0,00'; ?></p>
                    <?php else: ?>
                        <p class="mb-0">Valor: R$ <?php echo number_format($acordo['valor_entrada'], 2, ',', '.'); ?></p>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Alvarás a Receber -->
<?php if (!empty($alvaras_receber)): ?>
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">Alvarás a Receber</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Valor</th>
                        <th>Status</th>
                        <th>Data Recebimento</th>
                        <th>Observações</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($alvaras_receber as $alvara): ?>
                        <tr>
                            <td>R$ <?php echo number_format($alvara['valor'], 2, ',', '.'); ?></td>
                            <td>
                                <span class="status-badge <?php echo strtolower($alvara['status']); ?>">
                                    <?php echo $alvara['status']; ?>
                                </span>
                            </td>
                            <td>
                                <?php echo $alvara['data_recebimento'] ? date('d/m/Y', strtotime($alvara['data_recebimento'])) : '-'; ?>
                            </td>
                            <td><?php echo htmlspecialchars($alvara['observacoes'] ?? ''); ?></td>
                            <td>
                                <?php if ($alvara['status'] === 'PENDENTE'): ?>
                                    <button type="button" class="btn btn-primary btn-sm" 
                                            onclick="registrarRecebimentoAlvara(<?php echo $alvara['id']; ?>, <?php echo $alvara['valor']; ?>)">
                                        <i class="fas fa-check me-1"></i> Registrar Recebimento
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Modal de Recebimento de Alvará -->
<div class="modal fade" id="modalRecebimentoAlvara" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Registrar Recebimento de Alvará</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="formRecebimentoAlvara">
                    <input type="hidden" id="alvara_id" name="alvara_id">
                    <div class="mb-3">
                        <label class="form-label">Valor do Alvará</label>
                        <input type="text" class="form-control money" id="valor_alvara" name="valor_alvara">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Data de Recebimento</label>
                        <input type="date" class="form-control" id="data_recebimento" name="data_recebimento" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Observações</label>
                        <textarea class="form-control" id="observacoes" name="observacoes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" onclick="salvarRecebimentoAlvara()">Salvar</button>
            </div>
        </div>
    </div>
</div>

<!-- Parcelas -->
<div class="card">
    <div class="card-header">
        <h6 class="mb-0">Parcelas do Acordo</h6>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Nº</th>
                        <th>Vencimento</th>
                        <th>Valor</th>
                        <th>Status</th>
                        <th>Data Pagamento</th>
                        <th>Valor Pago</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($parcelas as $parcela): ?>
                        <tr>
                            <td><?php echo $parcela['numero_parcela']; ?></td>
                            <td><?php echo date('d/m/Y', strtotime($parcela['data_vencimento'])); ?></td>
                            <td>R$ <?php echo number_format($parcela['valor_parcela'], 2, ',', '.'); ?></td>
                            <td>
                                <span class="status-badge <?php echo strtolower(str_replace(' ', '-', $parcela['status_exibicao'])); ?>">
                                    <?php echo $parcela['status_exibicao']; ?>
                                </span>
                            </td>
                            <td>
                                <?php echo $parcela['data_pagamento'] ? date('d/m/Y', strtotime($parcela['data_pagamento'])) : '-'; ?>
                            </td>
                            <td>
                                <?php echo $parcela['valor_pago'] ? 'R$ ' . number_format($parcela['valor_pago'], 2, ',', '.') : '-'; ?>
                            </td>
                            <td>
                                <?php if ($parcela['status'] !== 'PAGO' && $acordo['ativo'] == 1): ?>
                                    <button type="button" class="btn btn-sm btn-success" onclick="abrirModalPagamento(<?php echo $parcela['id']; ?>)">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </button>
                                <?php elseif ($parcela['status'] !== 'PAGO' && $acordo['ativo'] == 0): ?>
                                    <button type="button" class="btn btn-sm btn-success" disabled title="Acordo inativo - não é possível registrar pagamentos">
                                        <i class="fas fa-money-bill-wave"></i>
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal de Pagamento -->
<div class="modal fade" id="modalPagamento" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Registrar Pagamento</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="formPagamento" onsubmit="return false;">
                    <input type="hidden" name="parcela_id" id="parcela_id">
                    <input type="hidden" name="acordo_id" value="<?php echo $acordo_id; ?>">
                    
                    <div class="mb-3">
                        <label class="form-label">Data do Pagamento</label>
                        <input type="date" name="data_pagamento" class="form-control" required 
                               value="<?php echo date('Y-m-d'); ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Valor Pago</label>
                        <input type="text" name="valor_pago" class="form-control money" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Observações</label>
                        <textarea name="observacoes" class="form-control" rows="2"></textarea>
                    </div>

                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-success">
                            Confirmar Pagamento
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function abrirModalPagamento(parcelaId) {
    const modal = new bootstrap.Modal(document.getElementById('modalPagamento'));
    document.getElementById('parcela_id').value = parcelaId;
    
    // Buscar valor da parcela e data de vencimento
    const row = document.querySelector(`button[onclick="abrirModalPagamento(${parcelaId})"]`).closest('tr');
    const valorParcelaTexto = row.querySelector('td:nth-child(3)').textContent.trim();
    const dataVencimentoTexto = row.querySelector('td:nth-child(2)').textContent.trim();
    
    // Converter a data do formato brasileiro (dd/mm/yyyy) para o formato do input date (yyyy-mm-dd)
    const [dia, mes, ano] = dataVencimentoTexto.split('/');
    const dataVencimentoFormatada = `${ano}-${mes}-${dia}`;
    
    // Definir a data de vencimento como valor padrão
    document.querySelector('input[name="data_pagamento"]').value = dataVencimentoFormatada;
    
    console.log('Valor da parcela capturado:', valorParcelaTexto);
    console.log('Data de vencimento capturada:', dataVencimentoFormatada);
    
    // Verificar se o valor já está no formato correto (R$ X.XXX,XX)
    if (valorParcelaTexto.startsWith('R$')) {
        document.querySelector('input[name="valor_pago"]').value = valorParcelaTexto;
    } else {
        // Se não estiver formatado, converter para número e formatar
        const valorLimpo = valorParcelaTexto.replace(/[^\d,]/g, '').replace(',', '.');
        const valorNumerico = parseFloat(valorLimpo);
        if (!isNaN(valorNumerico)) {
            const valorFormatado = valorNumerico.toLocaleString('pt-BR', {
                style: 'currency',
                currency: 'BRL',
                minimumFractionDigits: 2
            });
            document.querySelector('input[name="valor_pago"]').value = valorFormatado;
        }
    }
    
    modal.show();
    
    // Reiniciar o formulário para garantir que os event listeners sejam aplicados
    configurarFormularioPagamento();
}

function configurarFormularioPagamento() {
    // Inicializar máscara para campos monetários
    document.querySelectorAll('.money').forEach(function(input) {
        // Limpar eventos anteriores, se houver
        const clone = input.cloneNode(true);
        input.parentNode.replaceChild(clone, input);
        
        // Definir valor inicial como numérico sem máscara
        if (clone.value && clone.value.includes('R$')) {
            // Se já tiver um valor formatado, extrair apenas o número
            const rawValue = clone.value.replace('R$', '').replace(/\./g, '').trim();
            clone.value = rawValue;
        }
        
        // Adicionar atributo para indicar formato
        clone.setAttribute('placeholder', 'R$ 0,00');
        
        // Evento ao ganhar foco (remover formatação para facilitar edição)
        clone.addEventListener('focus', function() {
            const value = this.value.replace('R$', '').replace(/\./g, '').trim();
            this.value = value;
        });
        
        // Evento ao perder foco (formatar como moeda)
        clone.addEventListener('blur', function() {
            if (!this.value || this.value.trim() === '') {
                return;
            }
            
            // Remover formatação antes de processar
            let cleanValue = this.value.replace('R$', '').replace(/\./g, '').trim();
            
            // Se após limpeza o valor estiver vazio, não faz nada
            if (cleanValue === '') {
                return;
            }
            
            // Substituir vírgula por ponto para cálculos
            let value = cleanValue.replace(',', '.');
            
            // Converter para número
            let numValue = parseFloat(value);
            if (isNaN(numValue)) numValue = 0;
            
            // Formatar o número como moeda
            const formattedValue = numValue.toLocaleString('pt-BR', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
            
            this.value = 'R$ ' + formattedValue;
        });
    });

    // Reiniciar o evento de submissão
    const form = document.getElementById('formPagamento');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    // Remover eventos antigos e adicionar novo
    const newSubmitBtn = submitBtn.cloneNode(true);
    submitBtn.parentNode.replaceChild(newSubmitBtn, submitBtn);
    
    // Adicionar evento de clique ao botão de submissão
    newSubmitBtn.addEventListener('click', function() {
        const formData = new FormData(form);
        
        // Ajustar valor pago para formato correto
        let valorPagoInput = formData.get('valor_pago');
        console.log('Valor original do input:', valorPagoInput);
        
        // Remover formatação e converter para número decimal
        let valorPago;
        
        if (valorPagoInput.includes('R$')) {
            // Formato brasileiro: R$ 1.234,56
            valorPago = valorPagoInput
                .replace('R$', '')
                .replace(/\./g, '')
                .replace(',', '.')
                .trim();
        } else {
            // Pode estar no formato simples: 1234,56
            valorPago = valorPagoInput.replace(',', '.').trim();
        }
            
        console.log('Valor após conversão:', valorPago);
        
        // Garantir que é um número válido
        const valorNumerico = parseFloat(valorPago);
        if (isNaN(valorNumerico)) {
            Swal.fire('Erro!', 'O valor informado não é válido', 'error');
            return;
        }
        
        // Usar o valor numérico para garantir que está correto
        formData.set('valor_pago', valorNumerico.toString());
        
        // Mostrar loading
        Swal.fire({
            title: 'Processando...',
            text: 'Registrando pagamento',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });
        
        $.ajax({
            url: 'ajax/processar_pagamento.php',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('Resposta recebida:', response);
                let data;
                
                // Verificar se a resposta já é um objeto ou precisa ser parseada
                if (typeof response === 'object') {
                    data = response;
                } else {
                    try {
                        data = JSON.parse(response);
                    } catch (e) {
                        console.error('Erro ao parsear resposta:', e);
                        Swal.fire('Erro!', 'Resposta inválida do servidor', 'error');
                        return;
                    }
                }
                
                if (data.success) {
                    // Fechar o modal
                    const modalPagamento = bootstrap.Modal.getInstance(document.getElementById('modalPagamento'));
                    modalPagamento.hide();
                    
                    // Mostrar mensagem de sucesso
                    Swal.fire({
                        icon: 'success',
                        title: 'Sucesso!',
                        text: 'Pagamento registrado com sucesso',
                        showConfirmButton: false,
                        timer: 1500
                    });
                    
                    // Recarregar a modal com os dados atualizados
                    setTimeout(function() {
                        // Recarregar a página principal para atualizar a tabela
                        window.parent.location.reload();
                    }, 1600);
                } else {
                    Swal.fire('Erro!', data.message || 'Erro ao registrar pagamento', 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('Erro AJAX:', xhr, status, error);
                Swal.fire('Erro!', 'Erro ao processar pagamento. Por favor, tente novamente.', 'error');
            }
        });
    });
}

// Configurar formulário quando a página carrega
document.addEventListener('DOMContentLoaded', configurarFormularioPagamento);

function registrarRecebimentoAlvara(alvaraId, valor) {
    $('#alvara_id').val(alvaraId);
    $('#valor_alvara').val('R$ ' + valor.toLocaleString('pt-BR', { minimumFractionDigits: 2 }));
    $('#data_recebimento').val('').focus();
    $('#observacoes').val('');
    
    const modal = new bootstrap.Modal(document.getElementById('modalRecebimentoAlvara'));
    modal.show();
}

function salvarRecebimentoAlvara() {
    const formData = new FormData();
    formData.append('acao', 'registrar_recebimento_alvara');
    formData.append('alvara_id', $('#alvara_id').val());
    formData.append('data_recebimento', $('#data_recebimento').val());
    formData.append('observacoes', $('#observacoes').val());
    // Adicionar o valor do alvará editado, removendo a máscara corretamente
    let valorAlvara = $('#valor_alvara').val();
    if (valorAlvara) {
        // Remove prefixo, espaços, depois remove pontos (milhar), depois troca vírgula por ponto
        valorAlvara = valorAlvara.replace('R$', '').replace(/\s/g, '').replace(/\./g, '').replace(',', '.');
    }
    formData.append('valor_alvara', valorAlvara);

    $.ajax({
        url: 'ajax/gerenciar_acordo.php',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Sucesso!',
                    text: response.message
                }).then(() => {
                    // Fechar o modal de recebimento
                    bootstrap.Modal.getInstance(document.getElementById('modalRecebimentoAlvara')).hide();
                    // Atualizar a visualização do acordo
                    atualizarDetalhesAcordo(<?php echo $acordo_id; ?>);
                });
            } else {
                Swal.fire('Erro!', response.message, 'error');
            }
        },
        error: function() {
            Swal.fire('Erro!', 'Erro ao processar a requisição.', 'error');
        }
    });
}

// Adicionar máscara monetária ao campo de valor do alvará ao abrir o modal
$(document).ready(function() {
    if (window.Inputmask) {
        $('#modalRecebimentoAlvara').on('shown.bs.modal', function() {
            $('#valor_alvara').inputmask({
                alias: 'currency',
                radixPoint: ',',
                groupSeparator: '.',
                allowMinus: false,
                prefix: 'R$ ',
                digits: 2,
                digitsOptional: false,
                rightAlign: false,
                unmaskAsNumber: true
            });
        });
    }
});
</script> 