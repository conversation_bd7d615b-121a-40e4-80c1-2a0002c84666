<?php
/**
 * Script para adicionar sistema de histórico à estrutura antiga
 * Mantém usuario_id (usuários locais) mas adiciona controle de histórico
 */

require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

session_start();
if (!isset($_SESSION['user_id'])) {
    die("Acesso negado. Faça login para continuar.");
}

if (!checkACDPermission($_SESSION['user_id'])) {
    die("Acesso negado. Você não tem permissão para acessar o sistema ACD.");
}

if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    die("Acesso negado. Apenas administradores e gestores podem executar esta operação.");
}

echo "<h2>📈 Adicionar Sistema de Histórico</h2>";
echo "<p>Este script adiciona controle de histórico à estrutura antiga mantendo usuários locais.</p>";

try {
    echo "<h3>📋 Verificando Estrutura Atual</h3>";
    
    // Verificar se a tabela existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'acd_usuario_pa'");
    if ($stmt->rowCount() == 0) {
        echo "<p>❌ Tabela acd_usuario_pa não existe!</p>";
        echo "<p><a href='verificar_reversao.php'>🔍 Verificar Status da Reversão</a></p>";
        exit;
    }
    
    // Verificar estrutura atual
    $stmt = $pdo->query("DESCRIBE acd_usuario_pa");
    $colunas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $campos = array_column($colunas, 'Field');
    
    echo "<p>Campos atuais: " . implode(', ', $campos) . "</p>";
    
    $tem_usuario_id = in_array('usuario_id', $campos);
    $tem_pa_id = in_array('pa_id', $campos);
    $tem_data_vinculo = in_array('data_vinculo', $campos);
    $tem_status = in_array('status', $campos);
    
    // Verificar campos de histórico
    $tem_data_inicio = in_array('data_inicio', $campos);
    $tem_data_fim = in_array('data_fim', $campos);
    $tem_criado_por = in_array('criado_por', $campos);
    $tem_criado_em = in_array('criado_em', $campos);
    $tem_desativado_por = in_array('desativado_por', $campos);
    $tem_desativado_em = in_array('desativado_em', $campos);
    $tem_observacoes = in_array('observacoes', $campos);
    
    if (!$tem_usuario_id || !$tem_pa_id) {
        echo "<p>❌ Estrutura base não encontrada. Campos usuario_id e pa_id são obrigatórios.</p>";
        exit;
    }
    
    echo "<p>✅ Estrutura base confirmada (usuario_id + pa_id)</p>";
    
    // Verificar dados existentes
    echo "<h3>📊 Dados Existentes</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) FROM acd_usuario_pa");
    $total_vinculos = $stmt->fetchColumn();
    echo "<p>Total de vínculos: $total_vinculos</p>";
    
    if ($total_vinculos > 0) {
        echo "<p>Primeiros registros:</p>";
        $stmt = $pdo->query("SELECT * FROM acd_usuario_pa LIMIT 3");
        $exemplos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($exemplos)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            foreach (array_keys($exemplos[0]) as $campo) {
                echo "<th>$campo</th>";
            }
            echo "</tr>";
            
            foreach ($exemplos as $exemplo) {
                echo "<tr>";
                foreach ($exemplo as $valor) {
                    echo "<td>" . htmlspecialchars($valor ?? '') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    echo "<h3>🔧 Adicionando Sistema de Histórico</h3>";
    
    if (isset($_POST['adicionar_historico'])) {
        try {
            // Fazer backup antes de alterar
            $backup_table = 'acd_usuario_pa_backup_historico_' . date('Y_m_d_H_i_s');
            $pdo->exec("CREATE TABLE $backup_table AS SELECT * FROM acd_usuario_pa");
            echo "<p>💾 Backup criado: $backup_table</p>";
            
            // Adicionar colunas de histórico uma por uma
            $colunas_adicionar = [];
            
            if (!$tem_data_inicio) {
                $colunas_adicionar[] = [
                    'nome' => 'data_inicio',
                    'sql' => 'ALTER TABLE acd_usuario_pa ADD COLUMN data_inicio DATE NULL AFTER pa_id'
                ];
            }
            
            if (!$tem_data_fim) {
                $colunas_adicionar[] = [
                    'nome' => 'data_fim',
                    'sql' => 'ALTER TABLE acd_usuario_pa ADD COLUMN data_fim DATE NULL AFTER data_inicio'
                ];
            }
            
            if (!$tem_criado_por) {
                $colunas_adicionar[] = [
                    'nome' => 'criado_por',
                    'sql' => 'ALTER TABLE acd_usuario_pa ADD COLUMN criado_por INT NULL AFTER data_fim'
                ];
            }
            
            if (!$tem_criado_em) {
                $colunas_adicionar[] = [
                    'nome' => 'criado_em',
                    'sql' => 'ALTER TABLE acd_usuario_pa ADD COLUMN criado_em TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP AFTER criado_por'
                ];
            }
            
            if (!$tem_desativado_por) {
                $colunas_adicionar[] = [
                    'nome' => 'desativado_por',
                    'sql' => 'ALTER TABLE acd_usuario_pa ADD COLUMN desativado_por INT NULL AFTER criado_em'
                ];
            }
            
            if (!$tem_desativado_em) {
                $colunas_adicionar[] = [
                    'nome' => 'desativado_em',
                    'sql' => 'ALTER TABLE acd_usuario_pa ADD COLUMN desativado_em TIMESTAMP NULL AFTER desativado_por'
                ];
            }
            
            if (!$tem_observacoes) {
                $colunas_adicionar[] = [
                    'nome' => 'observacoes',
                    'sql' => 'ALTER TABLE acd_usuario_pa ADD COLUMN observacoes TEXT NULL AFTER desativado_em'
                ];
            }
            
            // Executar alterações
            foreach ($colunas_adicionar as $coluna) {
                try {
                    $pdo->exec($coluna['sql']);
                    echo "<p>✅ Coluna {$coluna['nome']} adicionada</p>";
                } catch (Exception $e) {
                    echo "<p>⚠️ Erro ao adicionar {$coluna['nome']}: " . $e->getMessage() . "</p>";
                }
            }
            
            // Migrar dados existentes
            if ($total_vinculos > 0) {
                echo "<p>🔄 Migrando dados existentes...</p>";
                
                // Atualizar data_inicio baseado em data_vinculo
                if (!$tem_data_inicio && $tem_data_vinculo) {
                    $stmt = $pdo->prepare("UPDATE acd_usuario_pa SET data_inicio = DATE(data_vinculo) WHERE data_inicio IS NULL");
                    $stmt->execute();
                    $atualizados = $stmt->rowCount();
                    echo "<p>✅ data_inicio atualizada para $atualizados registros</p>";
                }
                
                // Definir criado_por como usuário atual para registros existentes
                if (!$tem_criado_por) {
                    $stmt = $pdo->prepare("UPDATE acd_usuario_pa SET criado_por = ? WHERE criado_por IS NULL");
                    $stmt->execute([$_SESSION['user_id']]);
                    $atualizados = $stmt->rowCount();
                    echo "<p>✅ criado_por definido para $atualizados registros</p>";
                }
                
                // Definir criado_em baseado em data_vinculo
                if (!$tem_criado_em && $tem_data_vinculo) {
                    $stmt = $pdo->prepare("UPDATE acd_usuario_pa SET criado_em = data_vinculo WHERE criado_em IS NULL");
                    $stmt->execute();
                    $atualizados = $stmt->rowCount();
                    echo "<p>✅ criado_em atualizado para $atualizados registros</p>";
                }
                
                // Adicionar observação para registros migrados
                if (!$tem_observacoes) {
                    $stmt = $pdo->prepare("UPDATE acd_usuario_pa SET observacoes = 'Migrado para sistema com histórico' WHERE observacoes IS NULL");
                    $stmt->execute();
                    $atualizados = $stmt->rowCount();
                    echo "<p>✅ observacoes adicionadas para $atualizados registros</p>";
                }
            }
            
            // Adicionar índices para performance
            echo "<p>🔧 Adicionando índices...</p>";
            
            $indices = [
                'CREATE INDEX idx_data_inicio ON acd_usuario_pa (data_inicio)',
                'CREATE INDEX idx_data_fim ON acd_usuario_pa (data_fim)',
                'CREATE INDEX idx_criado_por ON acd_usuario_pa (criado_por)',
                'CREATE INDEX idx_desativado_por ON acd_usuario_pa (desativado_por)'
            ];
            
            foreach ($indices as $indice) {
                try {
                    $pdo->exec($indice);
                    echo "<p>✅ Índice criado</p>";
                } catch (Exception $e) {
                    // Índice pode já existir
                }
            }
            
            // Adicionar foreign keys para auditoria
            echo "<p>🔗 Adicionando foreign keys...</p>";
            
            $foreign_keys = [
                'ALTER TABLE acd_usuario_pa ADD CONSTRAINT fk_acd_usuario_pa_criado_por FOREIGN KEY (criado_por) REFERENCES usuarios(id)',
                'ALTER TABLE acd_usuario_pa ADD CONSTRAINT fk_acd_usuario_pa_desativado_por FOREIGN KEY (desativado_por) REFERENCES usuarios(id)'
            ];
            
            foreach ($foreign_keys as $fk) {
                try {
                    $pdo->exec($fk);
                    echo "<p>✅ Foreign key criada</p>";
                } catch (Exception $e) {
                    echo "<p>⚠️ Foreign key não criada: " . $e->getMessage() . "</p>";
                }
            }
            
            echo "<h3>✅ Sistema de Histórico Adicionado!</h3>";
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h4>🎉 Sucesso!</h4>";
            echo "<p>O sistema de histórico foi adicionado à estrutura antiga:</p>";
            echo "<ul>";
            echo "<li>✅ Mantém usuario_id (usuários locais)</li>";
            echo "<li>✅ Adiciona controle de período (data_inicio, data_fim)</li>";
            echo "<li>✅ Adiciona auditoria (criado_por, desativado_por)</li>";
            echo "<li>✅ Adiciona observações para relatórios</li>";
            echo "<li>✅ Dados existentes migrados</li>";
            echo "</ul>";
            echo "</div>";
            
            echo "<h3>📋 Próximos Passos</h3>";
            echo "<ol>";
            echo "<li><a href='criar_funcoes_historico_antiga.php' class='btn btn-primary'>🔧 Criar Funções com Histórico</a></li>";
            echo "<li><a href='gerenciar_vinculos.php' class='btn btn-success'>🔗 Testar Sistema</a></li>";
            echo "</ol>";
            
        } catch (Exception $e) {
            echo "<h3>❌ Erro durante a adição do histórico:</h3>";
            echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<p>O sistema de histórico adicionará as seguintes funcionalidades:</p>";
        
        echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>📈 Funcionalidades do Histórico</h4>";
        echo "<ul>";
        echo "<li><strong>data_inicio/data_fim:</strong> Controle de período de responsabilidade</li>";
        echo "<li><strong>criado_por/desativado_por:</strong> Auditoria de quem fez alterações</li>";
        echo "<li><strong>criado_em/desativado_em:</strong> Timestamps das operações</li>";
        echo "<li><strong>observacoes:</strong> Notas para relatórios detalhados</li>";
        echo "<li><strong>Relatórios:</strong> Histórico completo por usuário/PA/período</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>⚠️ O que será mantido</h4>";
        echo "<ul>";
        echo "<li>✅ usuario_id (usuários locais do banco)</li>";
        echo "<li>✅ pa_id (PAs locais)</li>";
        echo "<li>✅ Todos os dados existentes</li>";
        echo "<li>✅ Estrutura compatível com usuários locais</li>";
        echo "</ul>";
        echo "</div>";
        
        $colunas_faltando = [];
        if (!$tem_data_inicio) $colunas_faltando[] = 'data_inicio';
        if (!$tem_data_fim) $colunas_faltando[] = 'data_fim';
        if (!$tem_criado_por) $colunas_faltando[] = 'criado_por';
        if (!$tem_criado_em) $colunas_faltando[] = 'criado_em';
        if (!$tem_desativado_por) $colunas_faltando[] = 'desativado_por';
        if (!$tem_desativado_em) $colunas_faltando[] = 'desativado_em';
        if (!$tem_observacoes) $colunas_faltando[] = 'observacoes';
        
        if (!empty($colunas_faltando)) {
            echo "<p><strong>Colunas que serão adicionadas:</strong> " . implode(', ', $colunas_faltando) . "</p>";
            
            echo "<form method='POST'>";
            echo "<button type='submit' name='adicionar_historico' class='btn btn-primary'>📈 Adicionar Sistema de Histórico</button>";
            echo "</form>";
        } else {
            echo "<p>✅ Todas as colunas de histórico já existem!</p>";
            echo "<p><a href='criar_funcoes_historico_antiga.php' class='btn btn-primary'>🔧 Criar Funções com Histórico</a></p>";
        }
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Erro:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "<br><a href='gerenciar_vinculos.php'>← Voltar para Gerenciar Vínculos</a>";
?>

<style>
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    color: white; 
    text-decoration: none; 
    border-radius: 4px; 
    border: none;
    cursor: pointer;
    margin: 5px;
}
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn:hover { opacity: 0.8; }
table { font-size: 12px; }
th, td { padding: 8px; text-align: left; }
</style>
