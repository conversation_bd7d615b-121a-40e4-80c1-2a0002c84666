<?php
// Teste de conexão com o banco de dados usando o mesmo require dos relatórios
require_once __DIR__ . '/../../config/database.php';

if (isset($pdo) && $pdo instanceof PDO) {
    echo '<span style="color:green;font-weight:bold">Conexão OK</span>';
    try {
        $result = $pdo->query('SELECT DATABASE()')->fetchColumn();
        echo '<br>Banco selecionado: <b>' . htmlspecialchars($result) . '</b>';
    } catch (Exception $e) {
        echo '<br>Erro ao executar SELECT DATABASE(): ' . htmlspecialchars($e->getMessage());
    }
} else {
    echo '<span style="color:red;font-weight:bold">Sem conexão PDO</span>';
}
?>
