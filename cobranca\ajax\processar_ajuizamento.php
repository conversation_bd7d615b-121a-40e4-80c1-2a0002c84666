<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    // Validar se recebeu os dados via POST
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido');
    }

    // Validar campos obrigatórios
    $required_fields = ['processo_id', 'data_ajuizamento', 'valor_ajuizado', 'numero_processo'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || $_POST[$field] === '') {
            throw new Exception("Campo obrigatório não informado: {$field}");
        }
    }

    // Validar e formatar dados
    $processo_id = intval($_POST['processo_id']);
    $data_ajuizamento = $_POST['data_ajuizamento'];
    $valor_ajuizado = str_replace(['R$', '.', ','], ['', '', '.'], $_POST['valor_ajuizado']);
    $numero_processo = $_POST['numero_processo'];

    // Validar processo
    $stmt = $pdo->prepare("SELECT id FROM cbp_processos_judiciais WHERE id = ?");
    $stmt->execute([$processo_id]);
    if (!$stmt->fetch()) {
        throw new Exception('Processo não encontrado');
    }
    
    // Verificar se tem acordos
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_acordos WHERE processo_id = ?");
    $stmt->execute([$processo_id]);
    $tem_acordos = $stmt->fetchColumn() > 0;
    
    // Verificar se tem alvarás
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_alvaras WHERE processo_id = ?");
    $stmt->execute([$processo_id]);
    $tem_alvaras = $stmt->fetchColumn() > 0;
    
    // Se tiver acordos ou alvarás, não permitir a edição
    if ($tem_acordos || $tem_alvaras) {
        throw new Exception('Não é possível editar as informações de ajuizamento deste processo pois ele possui acordos ou alvarás cadastrados.');
    }

    // Validar data
    if (!strtotime($data_ajuizamento)) {
        throw new Exception('Data de ajuizamento inválida');
    }

    // Validar valor
    if (!is_numeric($valor_ajuizado) || floatval($valor_ajuizado) <= 0) {
        throw new Exception('Valor ajuizado inválido');
    }

    // Validar formato do número do processo
    if (!preg_match('/^[0-9]{7}-[0-9]{2}\.[0-9]{4}\.[0-9]\.[0-9]{2}\.[0-9]{4}$/', $numero_processo)) {
        throw new Exception('O formato do número do processo deve ser 0000000-00.0000.0.00.0000');
    }

    // Atualizar processo
    $stmt = $pdo->prepare("
        UPDATE cbp_processos_judiciais 
        SET 
            numero_processo = ?,
            data_ajuizamento = ?,
            valor_ajuizado = ?,
            updated_at = NOW()
        WHERE id = ?
    ");

    $stmt->execute([
        $numero_processo,
        $data_ajuizamento,
        $valor_ajuizado,
        $processo_id
    ]);

    echo json_encode([
        'success' => true,
        'message' => 'Ajuizamento cadastrado com sucesso!'
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 