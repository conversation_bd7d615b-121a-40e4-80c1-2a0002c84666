<?php
require_once '../auth_check.php';
require_once $_SERVER['DOCUMENT_ROOT'] . '/d/config/database.php';
require_once '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Dompdf\Dompdf;

// Consulta sintética
$stmt = $pdo->query("SELECT id, numero, associado_id, data_distribuicao FROM cbp_processos_judiciais ORDER BY data_distribuicao DESC LIMIT 1000");
$dados = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Verifica se há dados
if (empty($dados)) {
    require_once '../includes/header.php';
    echo '<div class="container"><h2>Relatório Sintético de Processos</h2>';
    echo '<div class="alert alert-warning">Nenhum dado encontrado para gerar o relatório.</div>';
    echo '</div>';
    require_once '../includes/footer.php';
    exit;
}

// Tipo de exportação
$formato = $_GET['formato'] ?? 'html';

if ($formato === 'excel') {
    $spreadsheet = new Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    $sheet->fromArray(array_keys($dados[0]), NULL, 'A1');
    $sheet->fromArray($dados, NULL, 'A2');
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="processos_sintetico.xlsx"');
    ob_clean();
    flush();
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;
} elseif ($formato === 'pdf') {
    $html = '<h2>Relatório Sintético de Processos</h2><table border="1" cellpadding="4" cellspacing="0"><tr>';
    foreach (array_keys($dados[0]) as $col) {
        $html .= '<th>' . htmlspecialchars($col) . '</th>';
    }
    $html .= '</tr>';
    foreach ($dados as $row) {
        $html .= '<tr>';
        foreach ($row as $cell) {
            $html .= '<td>' . htmlspecialchars($cell) . '</td>';
        }
        $html .= '</tr>';
    }
    $html .= '</table>';
    $dompdf = new Dompdf();
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();
    $dompdf->stream('processos_sintetico.pdf');
    exit;
}

require_once '../includes/header.php';
echo '<div class="container"><h2>Relatório Sintético de Processos</h2>';
echo '<a href="?formato=excel" class="btn btn-success">Exportar Excel</a> ';
echo '<a href="?formato=pdf" class="btn btn-danger">Exportar PDF</a>';
echo '<table class="table table-bordered mt-3"><thead><tr>';
foreach (array_keys($dados[0]) as $col) {
    echo '<th>' . htmlspecialchars($col) . '</th>';
}
echo '</tr></thead><tbody>';
foreach ($dados as $row) {
    echo '<tr>';
    foreach ($row as $cell) {
        echo '<td>' . htmlspecialchars($cell) . '</td>';
    }
    echo '</tr>';
}
echo '</tbody></table></div>';
require_once '../includes/footer.php';
