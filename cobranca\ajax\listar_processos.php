<?php
require_once '../../config/database.php';

// Verificar e atualizar status dos processos
include_once 'verificar_status_todos.php';

// Definir o tipo de conteúdo como JSON
header('Content-Type: application/json');

try {
    // Parâmetros do DataTables
    $draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
    $start = isset($_POST['start']) ? intval($_POST['start']) : 0;
    $length = isset($_POST['length']) ? intval($_POST['length']) : 10;
    $search = isset($_POST['search']['value']) ? $_POST['search']['value'] : '';
    $order_column = isset($_POST['order'][0]['column']) ? intval($_POST['order'][0]['column']) : 0;
    $order_dir = isset($_POST['order'][0]['dir']) ? $_POST['order'][0]['dir'] : 'asc';

    // Mapeamento das colunas para ordenação
    $columns = [
        'p.numero_processo',
        'p.associado_nome',
        'pa.nome',
        'a.nome',
        'p.valor_ajuizado',
        'p.data_ajuizamento',
        's.nome'
    ];

    // Construir a query base
    $query = "
        SELECT 
            p.id,
            p.numero_processo,
            COALESCE(a.nome, p.associado_nome) as nome,
            COALESCE(a.cpf_cnpj, p.associado_documento) as cpf_cnpj,
            pa.nome as pa_nome,
            pa.numero as pa_numero,
            adv.nome as advogado_nome,
            p.valor_ajuizado,
            DATE_FORMAT(p.data_ajuizamento, '%d/%m/%Y') as data_ajuizamento,
            s.nome as status_nome,
            p.status_id
        FROM cbp_processos_judiciais p
        LEFT JOIN cbp_processos_contratos pc ON p.id = pc.processo_id
        LEFT JOIN cbp_contratos c ON pc.contrato_id = c.id
        LEFT JOIN cbp_associados a ON c.associado_id = a.id
        LEFT JOIN pontos_atendimento pa ON p.pa_id = pa.id
        LEFT JOIN cbp_modalidades_processo m ON p.modalidade_id = m.id
        LEFT JOIN cbp_advogados adv ON p.advogado_id = adv.id
        LEFT JOIN cbp_status_processo s ON p.status_id = s.id
        WHERE 1=1
    ";

    // Adicionar condição de busca
    if (!empty($search)) {
        $query .= " AND (
            p.numero_processo COLLATE utf8mb4_general_ci LIKE :search
            OR COALESCE(a.nome, p.associado_nome) COLLATE utf8mb4_general_ci LIKE :search
            OR COALESCE(a.cpf_cnpj, p.associado_documento) COLLATE utf8mb4_general_ci LIKE :search
            OR pa.nome COLLATE utf8mb4_general_ci LIKE :search
            OR m.nome COLLATE utf8mb4_general_ci LIKE :search
            OR adv.nome COLLATE utf8mb4_general_ci LIKE :search
            OR s.nome COLLATE utf8mb4_general_ci LIKE :search
        )";
    }

    // Filtros adicionais
    if (!empty($_POST['status'])) {
        $query .= " AND p.status_id = :status";
    }

    if (!empty($_POST['advogado'])) {
        $query .= " AND p.advogado_id = :advogado";
    }

    if (!empty($_POST['pa'])) {
        $query .= " AND p.pa_id = :pa";
    }

    if (!empty($_POST['busca'])) {
        $query .= " AND (
            p.numero_processo COLLATE utf8mb4_general_ci LIKE :busca
            OR COALESCE(a.nome, p.associado_nome) COLLATE utf8mb4_general_ci LIKE :busca
            OR COALESCE(a.cpf_cnpj, p.associado_documento) COLLATE utf8mb4_general_ci LIKE :busca
        )";
    }

    // Adicionar ordenação
    $query .= " GROUP BY p.id";
    $query .= " ORDER BY " . $columns[$order_column] . " " . $order_dir;

    // Adicionar paginação
    $query .= " LIMIT :start, :length";

    // Preparar e executar a query
    $stmt = $pdo->prepare($query);

    // Bind dos parâmetros
    if (!empty($search)) {
        $search_param = "%{$search}%";
        $stmt->bindParam(':search', $search_param, PDO::PARAM_STR);
    }

    // Bind dos parâmetros de filtro adicionais
    if (!empty($_POST['status'])) {
        $stmt->bindParam(':status', $_POST['status'], PDO::PARAM_INT);
    }

    if (!empty($_POST['advogado'])) {
        $stmt->bindParam(':advogado', $_POST['advogado'], PDO::PARAM_INT);
    }

    if (!empty($_POST['pa'])) {
        $stmt->bindParam(':pa', $_POST['pa'], PDO::PARAM_INT);
    }

    if (!empty($_POST['busca'])) {
        $busca = "%{$_POST['busca']}%";
        $stmt->bindParam(':busca', $busca, PDO::PARAM_STR);
    }

    $stmt->bindParam(':start', $start, PDO::PARAM_INT);
    $stmt->bindParam(':length', $length, PDO::PARAM_INT);

    $stmt->execute();
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Formatar os dados para exibição
    foreach ($data as &$row) {
        // Formatar CPF/CNPJ
        $cpf_cnpj = preg_replace('/[^0-9]/', '', $row['cpf_cnpj']);
        if (strlen($cpf_cnpj) === 11) {
            $row['cpf_cnpj_formatado'] = substr($cpf_cnpj, 0, 3) . '.' . 
                                        substr($cpf_cnpj, 3, 3) . '.' . 
                                        substr($cpf_cnpj, 6, 3) . '-' . 
                                        substr($cpf_cnpj, 9, 2);
        } else {
            $row['cpf_cnpj_formatado'] = substr($cpf_cnpj, 0, 2) . '.' . 
                                        substr($cpf_cnpj, 2, 3) . '.' . 
                                        substr($cpf_cnpj, 5, 3) . '/' . 
                                        substr($cpf_cnpj, 8, 4) . '-' . 
                                        substr($cpf_cnpj, 12, 2);
        }

        // Formatar número do processo
        $row['processo_contrato'] = $row['numero_processo'];
        
        // Formatação para o nome e CPF/CNPJ será feita diretamente no DataTable
        // Manter o nome e CPF/CNPJ formatado separados
        $row['nome_cpf'] = $row['nome']; // Mantemos por compatibilidade, mas usaremos 'nome' e 'cpf_cnpj_formatado' diretamente
        
        // Formatar valor ajuizado
        if ($row['valor_ajuizado'] === null || $row['data_ajuizamento'] === null) {
            $row['valor_ajuizado_formatado'] = '<span class="badge bg-warning">Não ajuizado</span>';
            $tem_ajuizamento = false;
        } else {
            $row['valor_ajuizado_formatado'] = 'R$ ' . number_format($row['valor_ajuizado'], 2, ',', '.');
            $tem_ajuizamento = true;
        }
        
        // Verificar se o processo tem acordos ou alvarás
        $stmt_acordos = $pdo->prepare("SELECT COUNT(*) FROM cbp_acordos WHERE processo_id = :processo_id");
        $stmt_acordos->bindParam(':processo_id', $row['id'], PDO::PARAM_INT);
        $stmt_acordos->execute();
        $tem_acordos = $stmt_acordos->fetchColumn() > 0;
        
        // Verificar se tem alvarás
        $stmt_alvaras = $pdo->prepare("SELECT COUNT(*) FROM cbp_alvaras WHERE processo_id = :processo_id");
        $stmt_alvaras->bindParam(':processo_id', $row['id'], PDO::PARAM_INT);
        $stmt_alvaras->execute();
        $tem_alvaras = $stmt_alvaras->fetchColumn() > 0;
        
        // Verificar valor total dos contratos vinculados
        $stmt_contratos = $pdo->prepare("
            SELECT COALESCE(SUM(valor_contrato), 0) as valor_total 
            FROM cbp_processos_contratos 
            WHERE processo_id = :processo_id
        ");
        $stmt_contratos->bindParam(':processo_id', $row['id'], PDO::PARAM_INT);
        $stmt_contratos->execute();
        $valor_total_contratos = $stmt_contratos->fetchColumn();
        
        // Verificar se o valor total dos contratos é igual ao valor do ajuizamento
        $valores_iguais = abs($valor_total_contratos - $row['valor_ajuizado']) < 0.01;
        
        // Formatar botões de ação
        $row['acoes'] = '
            <div class="btn-group">
                <button type="button" class="btn btn-sm btn-turquesa" onclick="visualizarProcesso(' . $row['id'] . ')" title="Visualizar">
                    <i class="fas fa-eye"></i>
                </button>
                <button type="button" class="btn btn-sm btn-verde-claro" onclick="editarProcesso(' . $row['id'] . ')" 
                        data-tem-acordo="' . ($tem_acordos || $tem_alvaras ? '1' : '0') . '"
                        title="Editar' . (($tem_acordos || $tem_alvaras) ? ' (campos de ajuizamento serão bloqueados)' : '') . '">
                    <i class="fas fa-edit"></i>
                </button>';

        if (!$tem_ajuizamento) {
            // Se não tem ajuizamento, mostrar o botão de remover
            $row['acoes'] .= '
                <button type="button" class="btn btn-sm btn-danger" 
                        onclick="removerProcesso(' . $row['id'] . ')" 
                        data-tem-alvaras="' . ($tem_alvaras ? '1' : '0') . '"
                        title="Remover Processo">
                    <i class="fas fa-trash"></i>
                </button>';
        } else {
            // Se tem ajuizamento, mostrar os botões de quitar e acordo
            $row['acoes'] .= '
                <button type="button" class="btn btn-sm btn-verde-escuro" 
                        onclick="' . ($row['status_id'] == 3 ? 'reabrirProcesso(' : 'quitarProcesso(') . $row['id'] . ')" 
                        ' . (!$tem_ajuizamento || !$valores_iguais ? 'data-bs-toggle="tooltip" title="É necessário ter informações de ajuizamento e contratos vinculados com valor total igual ao valor do ajuizamento para quitar o processo" disabled' : 'title="' . ($row['status_id'] == 3 ? 'Reabrir Processo' : 'Quitar Processo') . '"') . '>
                    <i class="fas ' . ($row['status_id'] == 3 ? 'fa-undo' : 'fa-check-circle') . '"></i>
                </button>
                <button type="button" class="btn btn-sm btn-verde-medio" 
                        onclick="incluirAcordo(' . $row['id'] . ')" 
                        ' . (!$tem_ajuizamento || !$valores_iguais ? 'data-bs-toggle="tooltip" title="É necessário ter informações de ajuizamento e contratos vinculados com valor total igual ao valor do ajuizamento para criar um acordo" disabled' : 'title="Incluir Acordo"') . '>
                    <i class="fas fa-handshake"></i>
                </button>';
        }

        $row['acoes'] .= '</div>';
    }

    // Contar total de registros
    $stmt = $pdo->query("
        SELECT COUNT(DISTINCT p.id)
        FROM cbp_processos_judiciais p
        LEFT JOIN cbp_processos_contratos pc ON p.id = pc.processo_id
    ");
    $recordsTotal = $stmt->fetchColumn();

    // Contar total de registros filtrados
    $where_conditions_count = [];
    $params_count = [];

    if (!empty($search)) {
        $where_conditions_count[] = "(
            p.numero_processo COLLATE utf8mb4_general_ci LIKE :search
            OR COALESCE(a.nome, p.associado_nome) COLLATE utf8mb4_general_ci LIKE :search
            OR COALESCE(a.cpf_cnpj, p.associado_documento) COLLATE utf8mb4_general_ci LIKE :search
            OR pa.nome COLLATE utf8mb4_general_ci LIKE :search
            OR m.nome COLLATE utf8mb4_general_ci LIKE :search
            OR adv.nome COLLATE utf8mb4_general_ci LIKE :search
            OR s.nome COLLATE utf8mb4_general_ci LIKE :search
        )";
        $params_count[':search'] = "%{$search}%";
    }

    if (!empty($_POST['status'])) {
        $where_conditions_count[] = "p.status_id = :status";
        $params_count[':status'] = $_POST['status'];
    }

    if (!empty($_POST['advogado'])) {
        $where_conditions_count[] = "p.advogado_id = :advogado";
        $params_count[':advogado'] = $_POST['advogado'];
    }

    if (!empty($_POST['pa'])) {
        $where_conditions_count[] = "p.pa_id = :pa";
        $params_count[':pa'] = $_POST['pa'];
    }

    if (!empty($_POST['busca'])) {
        $where_conditions_count[] = "(
            p.numero_processo COLLATE utf8mb4_general_ci LIKE :busca
            OR COALESCE(a.nome, p.associado_nome) COLLATE utf8mb4_general_ci LIKE :busca
            OR COALESCE(a.cpf_cnpj, p.associado_documento) COLLATE utf8mb4_general_ci LIKE :busca
        )";
        $params_count[':busca'] = "%{$_POST['busca']}%";
    }

    $query_count = "
        SELECT COUNT(DISTINCT p.id) 
        FROM cbp_processos_judiciais p
        LEFT JOIN cbp_processos_contratos pc ON p.id = pc.processo_id
        LEFT JOIN cbp_contratos c ON pc.contrato_id = c.id
        LEFT JOIN cbp_associados a ON c.associado_id = a.id
        LEFT JOIN pontos_atendimento pa ON p.pa_id = pa.id
        LEFT JOIN cbp_modalidades_processo m ON p.modalidade_id = m.id
        LEFT JOIN cbp_advogados adv ON p.advogado_id = adv.id
        LEFT JOIN cbp_status_processo s ON p.status_id = s.id
    ";

    if (!empty($where_conditions_count)) {
        $query_count .= " WHERE " . implode(" AND ", $where_conditions_count);
    }

    $stmt = $pdo->prepare($query_count);
    foreach ($params_count as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $recordsFiltered = $stmt->fetchColumn();

    // Preparar a resposta no formato do DataTables
    $response = [
        "draw" => $draw,
        "recordsTotal" => $recordsTotal,
        "recordsFiltered" => $recordsFiltered,
        "data" => $data
    ];

    // Enviar a resposta como JSON
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
} catch (Exception $e) {
    // Em caso de erro, retornar uma resposta de erro no formato do DataTables
    $response = [
        "draw" => $draw,
        "recordsTotal" => 0,
        "recordsFiltered" => 0,
        "data" => [],
        "error" => "Erro ao processar a requisição: " . $e->getMessage()
    ];
    
    // Enviar a resposta de erro como JSON
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
} 