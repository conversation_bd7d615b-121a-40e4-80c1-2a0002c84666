<?php
require_once '../config/database.php';
require_once '../config/funcoes.php';
session_start();

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se o arquivo foi especificado
if (!isset($_GET['file'])) {
    die('Arquivo não especificado');
}

// Obter o nome do arquivo
$filename = basename($_GET['file']);

// Verificar se o arquivo existe
$filepath = '../temp/' . $filename;
if (!file_exists($filepath)) {
    die('Arquivo não encontrado');
}

// Verificar se é um arquivo SQL
if (pathinfo($filename, PATHINFO_EXTENSION) !== 'sql') {
    die('Tipo de arquivo inválido');
}

// Configurar headers para download
header('Content-Type: application/sql');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Content-Length: ' . filesize($filepath));
header('Cache-Control: no-cache, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Enviar o arquivo
readfile($filepath);

// Remover o arquivo após o download
unlink($filepath);
exit; 