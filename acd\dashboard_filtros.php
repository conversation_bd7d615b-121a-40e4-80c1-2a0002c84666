<?php
session_start();
require_once '../config/database.php';

// Dashboard com filtros - requer login e permissões ACD

try {
    // Processar filtros
    $filtro_periodo = $_GET['periodo'] ?? '';
    $filtro_data_inicio = $_GET['data_inicio'] ?? '';
    $filtro_data_fim = $_GET['data_fim'] ?? '';



    // Se não há período definido, usar semana como padrão
    if (empty($filtro_periodo)) {
        $filtro_periodo = 'semana';
    }

    // 1. TÍTULO - Informações gerais
    $titulo = "Dashboard - Análise de Crédito e Devoluções";

    // Calcular período para exibição
    switch ($filtro_periodo) {
        case 'semana':
            $inicio_semana = date('d/m', strtotime('monday this week'));
            $fim_semana = date('d/m/Y', strtotime('sunday this week'));
            $periodo_exibicao = "Semana atual ($inicio_semana a $fim_semana)";
            break;
        case 'mes':
            $periodo_exibicao = 'Mês atual (' . date('m/Y') . ')';
            break;
        case 'ano':
            $periodo_exibicao = 'Ano atual (' . date('Y') . ')';
            break;
        case 'personalizado':
            if (!empty($filtro_data_inicio) && !empty($filtro_data_fim)) {
                $periodo_exibicao = 'Período: ' . date('d/m/Y', strtotime($filtro_data_inicio)) . ' a ' . date('d/m/Y', strtotime($filtro_data_fim));
            } else {
                $periodo_exibicao = 'Período personalizado (selecione as datas)';
            }
            break;
        default:
            // Padrão: semana atual
            $inicio_semana = date('d/m', strtotime('monday this week'));
            $fim_semana = date('d/m/Y', strtotime('sunday this week'));
            $periodo_exibicao = "Semana atual ($inicio_semana a $fim_semana)";
            break;
    }

    // Função para buscar dados do usuário na API
    function getUsuarioAPI($usuario_id) {
        $apiFields = [
            'api_user' => 'UFL7GXZ14LU9NOR',
            'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
            'api_module' => 'Usuarios',
            'api_action' => 'listarUsuarios'
        ];

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => http_build_query($apiFields),
        ));

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            curl_close($curl);
            return null;
        }

        curl_close($curl);

        $usuarios = json_decode($response, true);

        if (!is_array($usuarios)) {
            return null;
        }

        // Procurar o usuário específico pelo ID
        foreach ($usuarios as $usuario) {
            if (isset($usuario['id']) && $usuario['id'] == $usuario_id) {
                $foto = null;
                if (!empty($usuario['foto'])) {
                    $foto = 'https://intranet.sicoobcredilivre.com.br/sys/conteudo/usuarios/' . $usuario['foto'];
                }

                return [
                    'id' => $usuario['id'] ?? null,
                    'nome' => $usuario['nome'] ?? null,
                    'foto' => $foto,
                    'loginAD' => $usuario['loginAD'] ?? null,
                    'nomeAgencia' => $usuario['nomeAgencia'] ?? null
                ];
            }
        }

        return null;
    }

    // Função para buscar dados do usuário na API por loginAD
    function getUsuarioAPIByLoginAD($loginAD) {
        $apiFields = [
            'api_user' => 'UFL7GXZ14LU9NOR',
            'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
            'api_module' => 'Usuarios',
            'api_action' => 'listarUsuarios'
        ];

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => http_build_query($apiFields),
        ));

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            curl_close($curl);
            return null;
        }

        curl_close($curl);

        $usuarios = json_decode($response, true);

        if (!is_array($usuarios)) {
            return null;
        }

        // Procurar o usuário específico pelo loginAD
        foreach ($usuarios as $usuario) {
            if (isset($usuario['loginAD']) && $usuario['loginAD'] == $loginAD) {
                $foto = null;
                if (!empty($usuario['foto'])) {
                    $foto = 'https://intranet.sicoobcredilivre.com.br/sys/conteudo/usuarios/' . $usuario['foto'];
                }

                return [
                    'id' => $usuario['id'] ?? null,
                    'nome' => $usuario['nome'] ?? null,
                    'foto' => $foto,
                    'loginAD' => $usuario['loginAD'] ?? null,
                    'nomeAgencia' => $usuario['nomeAgencia'] ?? null
                ];
            }
        }

        return null;
    }

    // 2. PROPOSTAS POR PA COM FILTROS
    // Construir condição de período
    $condicao_periodo = '';
    $params = [];

    switch ($filtro_periodo) {
        case 'semana':
            $condicao_periodo = "AND YEARWEEK(f.data_criacao, 1) = YEARWEEK(CURDATE(), 1)";
            break;
        case 'mes':
            $condicao_periodo = "AND YEAR(f.data_criacao) = YEAR(CURDATE()) AND MONTH(f.data_criacao) = MONTH(CURDATE())";
            break;
        case 'ano':
            $condicao_periodo = "AND YEAR(f.data_criacao) = YEAR(CURDATE())";
            break;
        case 'personalizado':
            if (!empty($filtro_data_inicio) && !empty($filtro_data_fim)) {
                $condicao_periodo = "AND DATE(f.data_criacao) BETWEEN ? AND ?";
                $params = [$filtro_data_inicio, $filtro_data_fim];
            } else {
                // Se não tiver as datas, usar semana atual como fallback
                $condicao_periodo = "AND YEARWEEK(f.data_criacao, 1) = YEARWEEK(CURDATE(), 1)";
            }
            break;
        default:
            // Padrão: semana atual
            $condicao_periodo = "AND YEARWEEK(f.data_criacao, 1) = YEARWEEK(CURDATE(), 1)";
            break;
    }

    $sql = "
        SELECT
            pa.nome as pa,
            COALESCE(COUNT(f.id), 0) as total_propostas,
            COALESCE(SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END), 0) as devolvidas,
            CASE
                WHEN COUNT(f.id) > 0 THEN ROUND((SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(f.id)) * 100, 1)
                ELSE 0
            END as taxa_devolucao
        FROM pontos_atendimento pa
        LEFT JOIN acd_formularios f ON pa.nome COLLATE utf8mb4_general_ci = f.pa COLLATE utf8mb4_general_ci
            $condicao_periodo
            AND f.pa != '0' AND f.pa IS NOT NULL
        WHERE pa.nome != '0' AND pa.nome IS NOT NULL AND pa.nome != 'UAD'
        GROUP BY pa.nome
        ORDER BY total_propostas DESC, pa.nome ASC
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $propostas_por_pa = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // NOVO: array ordenado por menor taxa de devolução
    $propostas_por_pa_devolucao = $propostas_por_pa;
    usort($propostas_por_pa_devolucao, function($a, $b) {
        return $a['taxa_devolucao'] <=> $b['taxa_devolucao'];
    });

    // 3. QUANTIDADE DE PROPOSTAS POR USUÁRIO_PA (TOP 5)
    $condicao_periodo_simples = str_replace('f.', '', $condicao_periodo);

    $sql = "
        SELECT
            usuario_pa,
            COUNT(*) as total_propostas
        FROM acd_formularios
        WHERE 1=1 " . $condicao_periodo_simples . "
        AND pa != '0' AND pa IS NOT NULL
        AND usuario_pa IS NOT NULL AND usuario_pa != ''
        GROUP BY usuario_pa
        ORDER BY total_propostas DESC
        LIMIT 5
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $propostas_por_usuario = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 4. DADOS DO PERÍODO SELECIONADO
    if ($filtro_periodo === 'personalizado' && !empty($filtro_data_inicio) && !empty($filtro_data_fim)) {
        // Consulta apenas para o período personalizado
        $sql = "
            SELECT
                COUNT(*) as total_propostas,
                SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
                SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) as aprovadas,
                ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as taxa_devolucao,
                ROUND((SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as taxa_aprovacao
            FROM acd_formularios
            WHERE DATE(data_criacao) BETWEEN ? AND ?
            AND pa != '0' AND pa IS NOT NULL
        ";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$filtro_data_inicio, $filtro_data_fim]);
        $dados_personalizado = $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        // Consulta padrão para semana/mês/ano
        $sql = "
            SELECT
                COUNT(*) as total_propostas,
                SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
                SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) as aprovadas,
                ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as taxa_devolucao,
                ROUND((SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as taxa_aprovacao
            FROM acd_formularios
            WHERE 1=1 " . $condicao_periodo_simples . "
            AND pa != '0' AND pa IS NOT NULL
        ";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $dados_periodo = $stmt->fetch(PDO::FETCH_ASSOC);
        // Para compatibilidade com o código existente
        $dados_semana = $dados_periodo;
        $dados_mes = $dados_periodo;
        $dados_ano = $dados_periodo;
    }

    // 5. USUÁRIOS COM MENOR PERCENTUAL DE DEVOLUÇÃO (ENTRE QUEM ENVIOU PROPOSTAS)
    $sql = "
        SELECT
            usuario_pa,
            COUNT(*) as total_propostas,
            SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) as aprovadas,
            SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
            ROUND((SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 1) as taxa_devolucao
        FROM acd_formularios
        WHERE 1=1 " . $condicao_periodo_simples . "
        AND pa != '0' AND pa IS NOT NULL
        AND usuario_pa IS NOT NULL AND usuario_pa != ''
        GROUP BY usuario_pa
        HAVING COUNT(*) >= 2
        ORDER BY taxa_devolucao ASC, total_propostas DESC
        LIMIT 5
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $melhores_usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 6. USUÁRIOS DESTAQUE - PRESENTES EM AMBOS OS RANKINGS
    // Encontrar usuários que estão tanto no top propostas quanto no menor % devolução
    $usuarios_top_propostas = array_column($propostas_por_usuario, 'usuario_pa');
    $usuarios_menor_devolucao = array_column($melhores_usuarios, 'usuario_pa');
    $usuarios_destaque = array_intersect($usuarios_top_propostas, $usuarios_menor_devolucao);

    $top_usuarios_completos = [];
    if (!empty($usuarios_destaque)) {
        // Buscar dados completos dos usuários que estão em ambos os rankings
        foreach ($usuarios_destaque as $usuario_pa) {
            // Buscar dados do ranking de propostas
            $dados_propostas = array_filter($propostas_por_usuario, function($u) use ($usuario_pa) {
                return $u['usuario_pa'] === $usuario_pa;
            });
            $dados_propostas = reset($dados_propostas);

            // Buscar dados do ranking de menor devolução
            $dados_devolucao = array_filter($melhores_usuarios, function($u) use ($usuario_pa) {
                return $u['usuario_pa'] === $usuario_pa;
            });
            $dados_devolucao = reset($dados_devolucao);

            if ($dados_propostas && $dados_devolucao) {
                $top_usuarios_completos[] = [
                    'usuario_pa' => $usuario_pa,
                    'total_propostas' => $dados_propostas['total_propostas'],
                    'taxa_devolucao' => $dados_devolucao['taxa_devolucao'],
                    'aprovadas' => $dados_devolucao['aprovadas'],
                    'devolvidas' => $dados_devolucao['devolvidas'],
                    // Pontuação baseada em estar em ambos os rankings
                    'pontuacao' => round(($dados_propostas['total_propostas'] * 0.3) + ((100 - $dados_devolucao['taxa_devolucao']) * 0.7), 1)
                ];
            }
        }

        // Ordenar por pontuação
        usort($top_usuarios_completos, function($a, $b) {
            return $b['pontuacao'] <=> $a['pontuacao'];
        });

        // Limitar a 3 usuários
        $top_usuarios_completos = array_slice($top_usuarios_completos, 0, 3);
    }

    // 7. USUÁRIO DESTAQUE DO PERÍODO (DO BANCO LOCAL)
    $condicao_periodo_usuario = str_replace('data_criacao', 'f.data_criacao', $condicao_periodo_simples);

    $sql = "
        SELECT
            u.id as usuario_id,
            u.username,
            COUNT(f.id) as total_analisadas,
            SUM(CASE WHEN f.acao = 'submeter' THEN 1 ELSE 0 END) as aprovadas,
            SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
            ROUND((SUM(CASE WHEN f.acao = 'submeter' THEN 1 ELSE 0 END) / COUNT(f.id)) * 100, 1) as taxa_aprovacao
        FROM acd_formularios f
        JOIN usuarios u ON f.usuario_id = u.id
        WHERE 1=1 " . $condicao_periodo_usuario . "
        AND f.pa != '0' AND f.pa IS NOT NULL
        GROUP BY f.usuario_id, u.username
        HAVING COUNT(f.id) >= 3
        ORDER BY total_analisadas DESC, taxa_aprovacao DESC
        LIMIT 1
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $usuario_destaque = $stmt->fetch(PDO::FETCH_ASSOC);

    // Buscar dados da API para o usuário destaque usando username = loginAD
    $dados_usuario_destaque = null;
    if ($usuario_destaque) {
        $dados_usuario_destaque = getUsuarioAPIByLoginAD($usuario_destaque['username']);
    }
    
} catch (Exception $e) {
    die('Erro ao buscar dados: ' . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard ACD</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #003641;
            --secondary-color: #00AE9D;
            --accent-color: #FFB800;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --bg-light: #f8f9fa;
            --border-color: #dee2e6;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --info-color: #17a2b8;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: var(--text-dark);
            line-height: 1.4;
            height: 100vh;
            width: 100vw;
            margin: 0;
            padding: 8px;
            overflow: hidden;
        }

        .container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 0.6rem 1rem;
            border-radius: 8px;
            margin-bottom: 0.6rem;
            box-shadow: 0 4px 15px rgba(0, 54, 65, 0.3);
            text-align: center;
            flex-shrink: 0;
        }

        .header h1 {
            font-size: 1.4rem;
            margin-bottom: 0.2rem;
            font-weight: 700;
        }

        .header p {
            font-size: 0.8rem;
            opacity: 0.9;
            margin: 0;
        }

        .header-filters {
            display: flex;
            align-items: center;
            gap: 0.6rem;
            margin: 0.6rem 0 0.3rem 0;
            justify-content: center;
            flex-wrap: wrap;
        }

        .header-filters .filter-group {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }

        .header-filters label {
            font-size: 0.75rem;
            font-weight: 600;
            color: white;
        }

        .header-filters select,
        .header-filters input[type="date"] {
            padding: 0.25rem 0.4rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.75rem;
            backdrop-filter: blur(10px);
        }

        .header-filters select:focus,
        .header-filters input[type="date"]:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.6);
            background: rgba(255, 255, 255, 0.2);
        }

        .header-filters select option {
            background: var(--primary-color);
            color: white;
        }

        .header-filters span {
            color: white;
            font-size: 0.85rem;
        }

        /* Dashboard Grid 3x3 com alturas customizadas */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            grid-template-rows: 1.8fr 0.8fr 1.8fr;
            gap: 0.5rem;
            flex: 1;
            height: 100%;
            min-height: 0;
        }

        /* Grid positioning */
        .grid-item-1 { grid-column: 1; grid-row: 1 / 4; }
        .grid-item-2 { grid-column: 2; grid-row: 1; }
        .grid-item-3 { grid-column: 2; grid-row: 2; }
        .grid-item-4 { grid-column: 2; grid-row: 3; }
        .grid-item-5 { grid-column: 3; grid-row: 1; }
        .grid-item-6 { grid-column: 3; grid-row: 2; height: 20%; }
        .grid-item-7 { grid-column: 3; grid-row: 3; height: 40%; }

        /* Cards */
        .card {
            background: white;
            border-radius: 6px;
            padding: 0.6rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            height: 100%;
            min-height: 0;
            flex: 1 1 0;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            margin-bottom: 0.4rem;
            padding-bottom: 0.3rem;
            border-bottom: 1px solid var(--bg-light);
            flex-shrink: 0;
        }

        .card-title {
            font-size: 0.8rem;
            font-weight: 700;
            color: var(--text-dark);
            text-align: center;
        }

        .card-subtitle {
            font-size: 0.65rem;
            font-weight: 400;
            color: var(--text-light);
            text-align: center;
            margin-top: 0.2rem;
            font-style: italic;
        }

        .card-content {
            flex: 1 1 0;
            min-height: 0;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        /* Specific card styles */
        .card-pa .card-icon { background: var(--primary-color); }
        .card-usuario .card-icon { background: var(--secondary-color); }
        .card-periodo .card-icon { background: var(--info-color); }
        .card-melhores .card-icon { background: var(--success-color); }
        .card-destaque .card-icon { background: var(--accent-color); }

        /* Chart-like lists */
        .chart-list {
            display: flex;
            flex-direction: column;
            gap: 0.3rem;
            height: 100%;
            overflow: hidden;
        }

        .chart-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0.25rem 0.4rem;
            background: var(--bg-light);
            border-radius: 3px;
            border-left: 2px solid var(--primary-color);
            min-height: 22px;
            flex-shrink: 0;
        }

        .chart-name {
            font-weight: 600;
            color: var(--text-dark);
            flex: 1;
            font-size: 0.65rem;
            line-height: 1.1;
        }

        .chart-values {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.05rem;
            min-width: 50px;
        }

        .chart-value {
            font-weight: 700;
            color: var(--primary-color);
            font-size: 0.65rem;
            text-align: right;
        }

        .chart-percent {
            font-size: 0.55rem;
            padding: 0.05rem 0.2rem;
            border-radius: 6px;
            font-weight: 600;
            text-align: right;
        }

        /* Gráfico de barras para PAs */
        .bar-chart {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            gap: 0.3rem;
            height: 100%;
            flex: 1 1 0;
            padding: 0.5rem 0;
        }

        .bar-item {
            flex: 1 1 0;
            min-height: 0;
            display: grid;
            grid-template-columns: 60px 1fr auto;
            align-items: center;
            gap: 0.3rem;
        }

        .bar-label {
            font-size: 0.6rem;
            font-weight: 600;
            color: var(--text-dark);
            text-align: left;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .bar-container {
            height: 12px;
            background: var(--bg-light);
            border-radius: 6px;
            position: relative;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .bar-fill {
            height: 100%;
            border-radius: 7px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transition: width 0.8s ease;
            position: relative;
            min-width: 20px;
        }

        .bar-count {
            position: absolute;
            left: 4px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.6rem;
            font-weight: 700;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.7);
            z-index: 2;
        }

        .bar-percent {
            font-size: 0.55rem;
            padding: 0.1rem 0.3rem;
            border-radius: 6px;
            font-weight: 600;
            white-space: nowrap;
            min-width: 45px;
            text-align: center;
        }

        /* Animação para texto de alta devolução */
        .bar-percent.percent-high {
            animation: blinkRed 1.5s infinite;
        }

        @keyframes blinkRed {
            0% {
                background: #dc3545;
                color: white;
                transform: scale(1);
            }
            50% {
                background: #ff4757;
                color: white;
                transform: scale(1.05);
                box-shadow: 0 0 8px rgba(220, 53, 69, 0.8);
            }
            100% {
                background: #dc3545;
                color: white;
                transform: scale(1);
            }
        }

        /* Period stats */
        .period-stats {
            display: flex;
            justify-content: space-between;
            gap: 0.4rem;
            height: 100%;
            align-items: center;
        }

        .period-item {
            flex: 1;
            text-align: center;
            padding: 0.3rem 0.2rem;
            background: var(--bg-light);
            border-radius: 4px;
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-height: 60px;
        }

        .period-label {
            font-size: 0.6rem;
            color: var(--text-light);
            margin-bottom: 0.1rem;
            font-weight: 600;
        }

        .period-value {
            font-size: 0.9rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.1rem;
            line-height: 1;
        }

        .period-percent {
            font-size: 0.55rem;
            padding: 0.1rem 0.2rem;
            border-radius: 6px;
            font-weight: 600;
        }

        .percent-low { background: #d4edda; color: #155724; }
        .percent-medium { background: #fff3cd; color: #856404; }
        .percent-high { background: #f8d7da; color: #721c24; }



        /* Destaque do usuário */
        .user-highlight {
            background: var(--bg-light);
            border: 2px solid var(--primary-color);
            text-align: center;
            padding: 1rem;
            border-radius: 10px;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            margin: 0 auto 0.5rem;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .user-avatar-placeholder {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            font-weight: 700;
            margin: 0 auto 0.5rem;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .user-highlight .user-name {
            font-size: 0.8rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            line-height: 1.2;
        }

        .user-highlight .user-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.4rem;
        }

        .user-stat {
            background: white;
            padding: 0.4rem;
            border-radius: 4px;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        .user-stat-value {
            font-size: 0.9rem;
            font-weight: 700;
            color: var(--primary-color);
            line-height: 1;
        }

        .user-stat-label {
            font-size: 0.65rem;
            color: var(--text-light);
            line-height: 1.1;
        }

        /* Responsive - Específico para 1366x720 */
        @media (max-width: 1366px) and (max-height: 720px) {
            body {
                padding: 2px;
                line-height: 1.2;
            }
            .dashboard-grid {
                gap: 0.2rem;
                grid-template-rows: 2fr 0.6fr 1.4fr;
            }
            .card {
                padding: 0.2rem;
            }
            .card-header {
                margin-bottom: 0.15rem;
                padding-bottom: 0.1rem;
            }
            .card-title {
                font-size: 0.6rem;
            }
            .bar-chart {
                gap: 0.12rem;
                padding: 0.1rem 0;
            }
            .bar-label {
                font-size: 0.48rem;
            }
            .bar-count {
                font-size: 0.48rem;
            }
            .bar-percent {
                font-size: 0.42rem;
                padding: 0.05rem 0.12rem;
                min-width: 28px;
            }
            .bar-container {
                height: 8px;
            }
        }

        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr 1fr;
                grid-template-rows: auto;
            }

            .grid-item-1 { grid-column: 1; grid-row: 1; }
            .grid-item-2 { grid-column: 2; grid-row: 1; }
            .grid-item-3 { grid-column: 1 / 3; grid-row: 2; }
            .grid-item-4 { grid-column: 1; grid-row: 3; }
            .grid-item-5 { grid-column: 2; grid-row: 3; }
            .grid-item-6 { grid-column: 1 / 3; grid-row: 4; height: auto; }
            .grid-item-7 { grid-column: 1 / 3; grid-row: 5; height: auto; }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
                grid-template-rows: auto;
                gap: 1rem;
                height: auto;
            }

            .grid-item-1,
            .grid-item-2,
            .grid-item-3,
            .grid-item-4,
            .grid-item-5,
            .grid-item-6,
            .grid-item-7 {
                grid-column: 1;
                grid-row: auto;
            }

            .period-stats {
                flex-direction: column;
                gap: 0.75rem;
            }

            .podium {
                flex-direction: column;
                align-items: center;
                gap: 0.75rem;
            }

            .podium-item {
                width: 100%;
                max-width: 200px;
                height: auto !important;
                min-height: 100px;
            }

            .user-highlight .user-stats {
                grid-template-columns: 1fr;
            }

            .chart-list {
                gap: 0.5rem;
            }

            .chart-item {
                padding: 0.5rem;
            }
        }

        /* Scrollbar customization */
        .card-content::-webkit-scrollbar {
            width: 4px;
        }

        .card-content::-webkit-scrollbar-track {
            background: var(--bg-light);
            border-radius: 2px;
        }

        .card-content::-webkit-scrollbar-thumb {
            background: var(--secondary-color);
            border-radius: 2px;
        }

        .card-content::-webkit-scrollbar-thumb:hover {
            background: var(--primary-color);
        }

        /* Pódio Simples - Usuários Destaque */
        .podium-simple {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            padding: 0.5rem;
        }

        .podium-user {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.5);
            border: 1px solid rgba(0, 174, 157, 0.2);
            transition: all 0.3s ease;
        }

        .podium-user:hover {
            background: rgba(0, 174, 157, 0.1);
            border-color: var(--secondary-color);
            transform: translateY(-1px);
        }

        .podium-position-1 {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 215, 0, 0.1));
            border-color: #FFD700;
        }

        .podium-position-2 {
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.2), rgba(192, 192, 192, 0.1));
            border-color: #C0C0C0;
        }

        .podium-position-3 {
            background: linear-gradient(135deg, rgba(205, 127, 50, 0.2), rgba(205, 127, 50, 0.1));
            border-color: #CD7F32;
        }

        .user-position {
            font-size: 1rem;
            font-weight: 700;
            color: var(--primary-color);
            min-width: 25px;
            text-align: center;
        }

        .user-photo {
            position: relative;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            border: 2px solid var(--secondary-color);
            background: var(--secondary-color);
        }

        .user-photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-initials {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--secondary-color);
            color: white;
            font-size: 0.8rem;
            font-weight: 700;
        }

        .user-info {
            flex: 1;
            min-width: 0;
        }

        .user-name {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--text-dark);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-pa {
            font-size: 0.7rem;
            color: var(--text-light);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Destaque da Semana - Layout Compacto */
        .user-highlight-compact {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem;
        }

        .user-avatar-compact {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--secondary-color);
        }

        .user-avatar-placeholder-compact {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--secondary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            font-weight: 700;
            border: 2px solid var(--secondary-color);
        }

        .user-info-compact {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .user-name-compact {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-dark);
        }

        .user-metrics-compact {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.75rem;
            color: var(--text-light);
        }

        .metric-item {
            font-weight: 500;
        }

        .metric-separator {
            color: var(--secondary-color);
            font-weight: 700;
        }

        /* Destaque da Semana - Estatísticas Expandidas */
        .user-stats-expanded {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            padding: 1rem;
        }

        .user-stats-expanded .user-stat {
            text-align: center;
            padding: 0.75rem;
            background: rgba(0, 174, 157, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(0, 174, 157, 0.2);
        }

        .user-stats-expanded .user-stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.25rem;
        }

        .user-stats-expanded .user-stat-label {
            font-size: 0.8rem;
            color: var(--text-light);
            font-weight: 500;
        }

        /* Pódio dos PAs */
        .podium-pas {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            padding: 0.5rem;
        }

        .podium-pa {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.3rem;
            border-radius: 4px;
            border: 1px solid rgba(0, 174, 157, 0.2);
            background: rgba(255, 255, 255, 0.5);
            transition: all 0.3s ease;
        }

        .podium-pa:hover {
            background: rgba(0, 174, 157, 0.1);
            border-color: var(--secondary-color);
            transform: translateY(-1px);
        }

        .podium-pa-1 {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 215, 0, 0.05));
            border-color: #FFD700;
        }

        .podium-pa-2 {
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.15), rgba(192, 192, 192, 0.05));
            border-color: #C0C0C0;
        }

        .podium-pa-3 {
            background: linear-gradient(135deg, rgba(205, 127, 50, 0.15), rgba(205, 127, 50, 0.05));
            border-color: #CD7F32;
        }

        .pa-position {
            font-size: 0.7rem;
            font-weight: 700;
            color: var(--primary-color);
            min-width: 15px;
            text-align: center;
        }

        .pa-info {
            flex: 1;
            min-width: 0;
        }

        .pa-name {
            font-size: 0.65rem;
            font-weight: 600;
            color: var(--text-dark);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 0.05rem;
        }

        .pa-metrics {
            font-size: 0.6rem;
            color: var(--text-light);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Elementos compactos para o card do usuário destaque */
        .user-highlight-compact {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.3rem;
        }

        .user-avatar-compact {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 1px solid white;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
        }

        .user-avatar-placeholder-compact {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            font-weight: 700;
            border: 1px solid white;
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
        }

        .user-info-compact {
            flex: 1;
            min-width: 0;
        }

        .user-name-compact {
            font-size: 0.7rem;
            font-weight: 600;
            color: var(--primary-color);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 0.1rem;
        }

        .user-metrics-compact {
            font-size: 0.6rem;
            color: var(--text-light);
        }

        .metric-item {
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 1. TÍTULO -->
        <div class="header">
            <h1><i class="fas fa-chart-line"></i> <?php echo $titulo; ?></h1>
            <div class="header-filters">
                <div class="filter-group">
                    <label for="periodoFilter"><i class="fas fa-calendar-alt"></i> Período:</label>
                    <select id="periodoFilter">
                        <option value="semana" <?php echo $filtro_periodo === 'semana' || empty($filtro_periodo) ? 'selected' : ''; ?>>Semana Atual</option>
                        <option value="mes" <?php echo $filtro_periodo === 'mes' ? 'selected' : ''; ?>>Mês Atual</option>
                        <option value="ano" <?php echo $filtro_periodo === 'ano' ? 'selected' : ''; ?>>Ano Atual</option>
                        <option value="personalizado" <?php echo $filtro_periodo === 'personalizado' ? 'selected' : ''; ?>>Período Personalizado</option>
                    </select>
                </div>
                <div class="filter-group" id="dataPersonalizada" style="display: <?php echo $filtro_periodo === 'personalizado' ? 'flex' : 'none'; ?>;">
                    <input type="date" id="dataInicio" value="<?php echo htmlspecialchars($filtro_data_inicio); ?>">
                    <span>até</span>
                    <input type="date" id="dataFim" value="<?php echo htmlspecialchars($filtro_data_fim); ?>">
                    <button type="button" onclick="aplicarFiltro()" style="padding: 0.25rem 0.5rem; margin-left: 0.5rem; background: var(--secondary-color); color: white; border: none; border-radius: 3px; font-size: 0.75rem;">
                        <i class="fas fa-search"></i> Aplicar
                    </button>
                </div>

            </div>
            <p><i class="fas fa-info-circle"></i> <?php echo $periodo_exibicao; ?></p>
        </div>

        <!-- Dashboard Grid 3x3 -->
        <div class="dashboard-grid">
            <!-- 1ª Coluna: Propostas por PA (TODA COLUNA) -->
            <div class="card card-pa grid-item-1">
                <div class="card-header">
                    <div class="card-title" id="paCardTitle" style="cursor:pointer; user-select:none;">Propostas por PA</div>
                </div>
                <div class="card-content">
                    <div class="bar-chart" id="barChartPropostas">
                        <?php
                        if (!empty($propostas_por_pa)) {
                            $max_propostas = max(array_column($propostas_por_pa, 'total_propostas'));
                            foreach ($propostas_por_pa as $pa):
                                $largura_barra = ($max_propostas > 0) ? ($pa['total_propostas'] / $max_propostas) * 100 : 0;
                        ?>
                        <div class="bar-item">
                            <div class="bar-label"><?php echo htmlspecialchars($pa['pa']); ?></div>
                            <div class="bar-container">
                                <?php if ($pa['total_propostas'] > 0): ?>
                                <div class="bar-fill" style="width: <?php echo $largura_barra; ?>%;">
                                    <span class="bar-count"><?php echo number_format($pa['total_propostas'], 0, ',', '.'); ?></span>
                                </div>
                                <?php else: ?>
                                <div class="bar-fill" style="width: 100%; background: #e9ecef; border: 1px solid #dee2e6;">
                                    <span class="bar-count" style="color: var(--text-light);">0</span>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php if ($pa['total_propostas'] > 0): ?>
                                <div class="bar-percent <?php
                                    $taxa = $pa['taxa_devolucao'];
                                    echo $taxa >= 20 ? 'percent-high' : ($taxa >= 10 ? 'percent-medium' : 'percent-low');
                                ?>"><?php echo $pa['taxa_devolucao']; ?>% devol.</div>
                            <?php else: ?>
                                <div class="bar-percent" style="color: var(--text-light); font-size: 0.6rem;">Sem propostas</div>
                            <?php endif; ?>
                        </div>
                        <?php endforeach;
                        } else {
                        ?>
                        <div style="text-align: center; color: var(--text-light); padding: 2rem;">
                            <i class="fas fa-info-circle"></i> Nenhuma proposta encontrada para esta semana
                        </div>
                        <?php } ?>
                    </div>
                    <div class="bar-chart" id="barChartDevolucao" style="display:none;">
                        <?php
                        if (!empty($propostas_por_pa_devolucao)) {
                            $max_propostas = max(array_column($propostas_por_pa_devolucao, 'total_propostas'));
                            foreach ($propostas_por_pa_devolucao as $pa):
                                $largura_barra = ($max_propostas > 0) ? ($pa['total_propostas'] / $max_propostas) * 100 : 0;
                        ?>
                        <div class="bar-item">
                            <div class="bar-label"><?php echo htmlspecialchars($pa['pa']); ?></div>
                            <div class="bar-container">
                                <?php if ($pa['total_propostas'] > 0): ?>
                                <div class="bar-fill" style="width: <?php echo $largura_barra; ?>%;">
                                    <span class="bar-count"><?php echo number_format($pa['total_propostas'], 0, ',', '.'); ?></span>
                                </div>
                                <?php else: ?>
                                <div class="bar-fill" style="width: 100%; background: #e9ecef; border: 1px solid #dee2e6;">
                                    <span class="bar-count" style="color: var(--text-light);">0</span>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php if ($pa['total_propostas'] > 0): ?>
                                <div class="bar-percent <?php
                                    $taxa = $pa['taxa_devolucao'];
                                    echo $taxa >= 20 ? 'percent-high' : ($taxa >= 10 ? 'percent-medium' : 'percent-low');
                                ?>"><?php echo $pa['taxa_devolucao']; ?>% devol.</div>
                            <?php else: ?>
                                <div class="bar-percent" style="color: var(--text-light); font-size: 0.6rem;">Sem propostas</div>
                            <?php endif; ?>
                        </div>
                        <?php endforeach;
                        } else {
                        ?>
                        <div style="text-align: center; color: var(--text-light); padding: 2rem;">
                            <i class="fas fa-info-circle"></i> Nenhuma proposta encontrada para esta semana
                        </div>
                        <?php } ?>
                    </div>
                </div>
            </div>

            <!-- 2ª Coluna, 1ª Linha: Propostas por Usuário -->
            <div class="card card-usuario grid-item-2">
                <div class="card-header">
                    <div class="card-title">Propostas por Usuário</div>
                </div>
                <div class="card-content">
                    <div class="chart-list">
                        <?php foreach ($propostas_por_usuario as $usuario): ?>
                            <?php
                                // Buscar dados da API para o usuário
                                $dados_usuario = getUsuarioAPI($usuario['usuario_pa']);
                                $nome_exibir = $dados_usuario['nome'] ?? $usuario['usuario_pa'];
                            ?>
                            <div class="chart-item">
                                <div class="chart-name"><?php echo htmlspecialchars($nome_exibir); ?></div>
                                <div class="chart-value"><?php echo number_format($usuario['total_propostas'], 0, ',', '.'); ?></div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- 2ª Coluna, 2ª Linha: Propostas por Período -->
            <div class="card card-periodo grid-item-3">
                <div class="card-header">
                    <div class="card-title">Propostas por Período</div>
                </div>
                <div class="card-content">
                    <?php if ($filtro_periodo === 'personalizado' && !empty($filtro_data_inicio) && !empty($filtro_data_fim)): ?>
                        <div class="period-stats">
                            <div class="period-item" style="flex:1;">
                                <div class="period-label">Personalizado</div>
                                <div class="period-value"><?php echo number_format($dados_personalizado['total_propostas'] ?? 0, 0, ',', '.'); ?></div>
                                <div class="period-percent <?php
                                    $taxa = $dados_personalizado['taxa_devolucao'] ?? 0;
                                    echo $taxa >= 20 ? 'percent-high' : ($taxa >= 10 ? 'percent-medium' : 'percent-low');
                                ?>"><?php echo $dados_personalizado['taxa_devolucao'] ?? 0; ?>% devol.</div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="period-stats">
                            <div class="period-item">
                                <div class="period-label">Semana</div>
                                <div class="period-value"><?php echo number_format($dados_semana['total_propostas'], 0, ',', '.'); ?></div>
                                <div class="period-percent <?php
                                    $taxa = $dados_semana['taxa_devolucao'];
                                    echo $taxa >= 20 ? 'percent-high' : ($taxa >= 10 ? 'percent-medium' : 'percent-low');
                                ?>"><?php echo $dados_semana['taxa_devolucao']; ?>% devol.</div>
                            </div>
                            <div class="period-item">
                                <div class="period-label">Mês</div>
                                <div class="period-value"><?php echo number_format($dados_mes['total_propostas'], 0, ',', '.'); ?></div>
                                <div class="period-percent <?php
                                    $taxa = $dados_mes['taxa_devolucao'];
                                    echo $taxa >= 20 ? 'percent-high' : ($taxa >= 10 ? 'percent-medium' : 'percent-low');
                                ?>"><?php echo $dados_mes['taxa_devolucao']; ?>% devol.</div>
                            </div>
                            <div class="period-item">
                                <div class="period-label">Ano</div>
                                <div class="period-value"><?php echo number_format($dados_ano['total_propostas'], 0, ',', '.'); ?></div>
                                <div class="period-percent <?php
                                    $taxa = $dados_ano['taxa_devolucao'];
                                    echo $taxa >= 20 ? 'percent-high' : ($taxa >= 10 ? 'percent-medium' : 'percent-low');
                                ?>"><?php echo $dados_ano['taxa_devolucao']; ?>% devol.</div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 2ª Coluna, 3ª Linha: Melhores Usuários (menor taxa devolução) -->
            <div class="card card-melhores grid-item-4">
                <div class="card-header">
                    <div class="card-title">Menor Taxa de Devolução</div>
                </div>
                <div class="card-content">
                    <div class="chart-list">
                        <?php foreach ($melhores_usuarios as $usuario): ?>
                            <?php
                                // Buscar dados da API para o usuário
                                $dados_usuario = getUsuarioAPI($usuario['usuario_pa']);
                                $nome_exibir = $dados_usuario['nome'] ?? $usuario['usuario_pa'];
                            ?>
                            <div class="chart-item">
                                <div class="chart-name"><?php echo htmlspecialchars($nome_exibir); ?></div>
                                <div class="chart-value"><?php echo $usuario['taxa_devolucao']; ?>% devol. | <?php echo $usuario['total_propostas']; ?> propostas</div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- 3ª Coluna, 1ª Linha: Usuários Destaque -->
            <div class="card card-destaque grid-item-5">
                <div class="card-header">
                    <div class="card-title">Usuários Destaque</div>
                    <div class="card-subtitle">Presentes em ambos os rankings</div>
                </div>
                <div class="card-content">
                    <?php if (!empty($top_usuarios_completos)): ?>
                        <div class="podium-simple">
                            <?php foreach ($top_usuarios_completos as $index => $usuario): ?>
                                <?php
                                    // Buscar dados da API para o usuário
                                    $dados_usuario = getUsuarioAPI($usuario['usuario_pa']);
                                    $nome_exibir = $dados_usuario['nome'] ?? $usuario['usuario_pa'];
                                    $foto_url = $dados_usuario['foto'] ?? null;
                                    $pa_usuario = $dados_usuario['nomeAgencia'] ?? 'N/A';
                                ?>
                                <div class="podium-user podium-position-<?php echo $index + 1; ?>">
                                    <div class="user-position"><?php echo $index + 1; ?>º</div>
                                    <div class="user-photo">
                                        <?php if ($foto_url): ?>
                                            <img src="<?php echo htmlspecialchars($foto_url); ?>" alt="<?php echo htmlspecialchars($nome_exibir); ?>" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                            <div class="user-initials" style="display: none;">
                                                <?php echo strtoupper(substr($nome_exibir, 0, 2)); ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="user-initials">
                                                <?php echo strtoupper(substr($nome_exibir, 0, 2)); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="user-info">
                                        <div class="user-name"><?php echo htmlspecialchars($nome_exibir); ?></div>
                                        <div class="user-pa"><?php echo htmlspecialchars($pa_usuario); ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p style="text-align: center; color: var(--text-light); padding: 2rem;">
                            <i class="fas fa-info-circle"></i> Nenhum usuário presente em ambos os rankings
                        </p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 3ª Coluna, 2ª Linha: Destaque da Semana - Foto, Nome e Métricas (20%) -->
            <div class="card card-destaque grid-item-6">
                <div class="card-header">
                    <div class="card-title">Analista Destaque</div>
                </div>
                <div class="card-content">
                    <?php if ($usuario_destaque): ?>
                        <div class="user-highlight-compact">
                            <?php if ($dados_usuario_destaque && !empty($dados_usuario_destaque['foto'])): ?>
                                <img src="<?php echo htmlspecialchars($dados_usuario_destaque['foto']); ?>"
                                     alt="Foto de <?php echo htmlspecialchars($dados_usuario_destaque['nome'] ?? $usuario_destaque['username']); ?>"
                                     class="user-avatar-compact"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div class="user-avatar-placeholder-compact" style="display: none;">
                                    <?php echo strtoupper(substr($usuario_destaque['username'], 0, 2)); ?>
                                </div>
                            <?php else: ?>
                                <div class="user-avatar-placeholder-compact">
                                    <?php echo strtoupper(substr($usuario_destaque['username'], 0, 2)); ?>
                                </div>
                            <?php endif; ?>
                            <div class="user-info-compact">
                                <div class="user-name-compact">
                                    <?php echo htmlspecialchars($dados_usuario_destaque['nome'] ?? $usuario_destaque['username']); ?>
                                </div>
                                <div class="user-metrics-compact">
                                    <span class="metric-item"><?php echo $usuario_destaque['total_analisadas']; ?> propostas</span>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <p style="text-align: center; color: var(--text-light); padding: 1rem; font-size: 0.8rem;">
                            <i class="fas fa-info-circle"></i> Sem dados suficientes
                        </p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- 3ª Coluna, 3ª Linha: Top 3 PAs do Período (40%) -->
            <div class="card card-destaque grid-item-7">
                <div class="card-header">
                    <div class="card-title">Top Score</div>
                </div>
                <div class="card-content">
                    <?php
                    // Buscar Top 3 PAs do período
                    $sql = "
                        SELECT
                            pa.nome as pa,
                            COUNT(f.id) as total_propostas,
                            SUM(CASE WHEN f.acao = 'submeter' THEN 1 ELSE 0 END) as aprovadas,
                            SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END) as devolvidas,
                            ROUND((SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(f.id)) * 100, 1) as taxa_devolucao,
                            -- Pontuação: 40% propostas + 60% baixa devolução
                            ROUND(
                                (COUNT(f.id) * 0.4) +
                                ((100 - (SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(f.id)) * 100) * 0.6),
                                1
                            ) as pontuacao
                        FROM pontos_atendimento pa
                        LEFT JOIN acd_formularios f ON pa.nome COLLATE utf8mb4_general_ci = f.pa COLLATE utf8mb4_general_ci
                            $condicao_periodo
                            AND f.pa != '0' AND f.pa IS NOT NULL
                        WHERE pa.nome != '0' AND pa.nome IS NOT NULL AND pa.nome != 'UAD'
                        GROUP BY pa.nome
                        HAVING COUNT(f.id) >= 3
                        ORDER BY pontuacao DESC, total_propostas DESC
                        LIMIT 3
                    ";

                    $stmt = $pdo->prepare($sql);
                    $stmt->execute($params);
                    $top_pas_semana = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    ?>

                    <?php if (!empty($top_pas_semana)): ?>
                        <div class="podium-pas">
                            <?php foreach ($top_pas_semana as $index => $pa): ?>
                                <div class="podium-pa podium-pa-<?php echo $index + 1; ?>">
                                    <div class="pa-position"><?php echo $index + 1; ?>º</div>
                                    <div class="pa-info">
                                        <div class="pa-name"><?php echo htmlspecialchars($pa['pa']); ?></div>
                                        <div class="pa-metrics">
                                            <?php echo $pa['total_propostas']; ?> propostas • <?php echo $pa['taxa_devolucao']; ?>% devolução
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p style="text-align: center; color: var(--text-light); padding: 2rem;">
                            <i class="fas fa-info-circle"></i> Dados insuficientes para ranking
                        </p>
                    <?php endif; ?>
                </div>
            </div>
        </div>


    <script>
        // Controle dos filtros de período
        document.getElementById('periodoFilter').addEventListener('change', function() {
            const dataPersonalizada = document.getElementById('dataPersonalizada');

            if (this.value === 'personalizado') {
                dataPersonalizada.style.display = 'flex';
                // Focar no primeiro campo de data
                document.getElementById('dataInicio').focus();
            } else {
                dataPersonalizada.style.display = 'none';
                // Se não for personalizado, aplicar filtro imediatamente
                aplicarFiltro();
            }
        });

        // Eventos para os campos de data (apenas para validação visual)
        document.getElementById('dataInicio').addEventListener('change', function() {
            const dataFim = document.getElementById('dataFim').value;
            if (this.value && dataFim && new Date(this.value) > new Date(dataFim)) {
                alert('A data de início não pode ser maior que a data de fim!');
                this.value = '';
            }
        });

        document.getElementById('dataFim').addEventListener('change', function() {
            const dataInicio = document.getElementById('dataInicio').value;
            if (this.value && dataInicio && new Date(dataInicio) > new Date(this.value)) {
                alert('A data de fim não pode ser menor que a data de início!');
                this.value = '';
            }
        });

        function aplicarFiltro() {
            const periodo = document.getElementById('periodoFilter').value;
            const dataInicio = document.getElementById('dataInicio').value;
            const dataFim = document.getElementById('dataFim').value;

            let url = 'dashboard_filtros.php?periodo=' + periodo;

            if (periodo === 'personalizado') {
                if (dataInicio && dataFim) {
                    // Validar se data início não é maior que data fim
                    if (new Date(dataInicio) > new Date(dataFim)) {
                        alert('A data de início não pode ser maior que a data de fim!');
                        return;
                    }
                    url += '&data_inicio=' + dataInicio + '&data_fim=' + dataFim;
                } else {
                    alert('Por favor, selecione ambas as datas para o período personalizado.');
                    return;
                }
            }

            // Redirecionar para aplicar o filtro
            window.location.href = url;
        }

        // Auto-refresh da página a cada 5 minutos (apenas se não for período personalizado)
        <?php if ($filtro_periodo !== 'personalizado'): ?>
        setTimeout(function() {
            location.reload();
        }, 300000);
        <?php endif; ?>

        // Alternar ordenação do card Propostas por PA
        let paOrderByPropostas = true;
        const paCardTitle = document.getElementById('paCardTitle');
        const barChartPropostas = document.getElementById('barChartPropostas');
        const barChartDevolucao = document.getElementById('barChartDevolucao');
        paCardTitle.addEventListener('click', function() {
            paOrderByPropostas = !paOrderByPropostas;
            if (paOrderByPropostas) {
                paCardTitle.textContent = 'Propostas por PA';
                barChartPropostas.style.display = '';
                barChartDevolucao.style.display = 'none';
            } else {
                paCardTitle.textContent = 'Devolução por PA';
                barChartPropostas.style.display = 'none';
                barChartDevolucao.style.display = '';
            }
        });
    </script>
</body>
</html>
