<?php
// Buffer de saída para capturar qualquer saída indesejada
ob_start();

// Adicionar headers adequados
header('Content-Type: application/json');

// Habilitar exibição de erros para debug
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    require_once '../../auth_check.php';
    require_once '../../config/database.php';

    // Verificar se a constante está definida
    if (!defined('TIPO_ACESSO_COBRANCA')) {
        // Tentar definir o tipo de acesso
        $stmt = $pdo->prepare("
            SELECT tipo_acesso 
            FROM cbp_permissoes_usuarios 
            WHERE usuario_id = ? 
            AND ativo = 1
            AND tipo_acesso IN ('COMUM', 'GESTOR')
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $permissao = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$permissao) {
            throw new Exception('Usuário sem permissão de acesso');
        }
        
        define('TIPO_ACESSO_COBRANCA', $permissao['tipo_acesso']);
    }

    // Parâmetros do DataTables
    $draw = isset($_POST['draw']) ? intval($_POST['draw']) : 1;
    $start = isset($_POST['start']) ? intval($_POST['start']) : 0;
    $length = isset($_POST['length']) ? intval($_POST['length']) : 10;
    $search = isset($_POST['search']['value']) ? $_POST['search']['value'] : '';
    $order_column = isset($_POST['order'][0]['column']) ? intval($_POST['order'][0]['column']) : 0;
    $order_dir = isset($_POST['order'][0]['dir']) && strtoupper($_POST['order'][0]['dir']) === 'ASC' ? 'ASC' : 'DESC';

    // Mapeamento das colunas para ordenação
    $columns = array(
        0 => 'a.nome',
        1 => 'a.cpf_cnpj',
        2 => 'CAST(pa.numero AS UNSIGNED)',
        3 => 'total_contratos',
        4 => 'a.ativo',
        5 => 'a.created_at'
    );

    // Construir a query base
    $base_query = "
        FROM cbp_associados a
        LEFT JOIN pontos_atendimento pa ON a.pa_id = pa.id
        WHERE 1=1
    ";

    // Aplicar filtros
    $params = [];
    $where_conditions = [];

    // Filtro de busca global
    if (!empty($search) || !empty($_POST['searchGlobal'])) {
        $search_term = !empty($_POST['searchGlobal']) ? $_POST['searchGlobal'] : $search;
        $search_param = "%{$search_term}%";
        $where_conditions[] = "(
            a.nome LIKE ? OR 
            a.cpf_cnpj LIKE ? OR
            pa.nome LIKE ?
        )";
        $params = array_merge($params, [$search_param, $search_param, $search_param]);
    }

    // Filtros adicionais (PA e Status)
    if (!empty($_POST['pa'])) {
        $where_conditions[] = "a.pa_id = ?";
        $params[] = $_POST['pa'];
    }

    if (isset($_POST['ativo']) && $_POST['ativo'] !== '') {
        $where_conditions[] = "a.ativo = ?";
        $params[] = $_POST['ativo'];
    }

    if (!empty($where_conditions)) {
        $base_query .= " AND " . implode(" AND ", $where_conditions);
    }

    // Query para contar total de registros (sem filtros)
    $recordsTotal = $pdo->query("SELECT COUNT(*) FROM cbp_associados")->fetchColumn();

    // Query para contar registros filtrados
    $stmt = $pdo->prepare("SELECT COUNT(*) " . $base_query);
    $stmt->execute($params);
    $recordsFiltered = $stmt->fetchColumn();

    // Query principal com ordenação e limite
    $query = "
        SELECT 
            a.*,
            pa.nome as pa_nome,
            pa.numero as pa_numero,
            CAST(pa.numero AS UNSIGNED) as pa_numero_ordenacao,
            (SELECT COUNT(*) FROM cbp_contratos WHERE associado_id = a.id) as total_contratos
        " . $base_query . "
        ORDER BY " . $columns[$order_column] . " " . $order_dir . "
        LIMIT " . $length . "
        OFFSET " . $start;

    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $associados = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Formatar os dados para o DataTables
    $data = array();
    foreach ($associados as $associado) {
        // Formatar documento
        $doc = preg_replace('/[^0-9]/', '', $associado['cpf_cnpj']);
        if (strlen($doc) === 11) {
            $doc_formatado = preg_replace('/(\d{3})(\d{3})(\d{3})(\d{2})/', '$1.$2.$3-$4', $doc);
        } else if (strlen($doc) === 14) {
            $doc_formatado = preg_replace('/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/', '$1.$2.$3/$4-$5', $doc);
        } else {
            $doc_formatado = $doc;
        }

        // Formatar status
        $status = '<span class="status-badge ' . ($associado['ativo'] ? 'status-ativo' : 'status-inativo') . '">' .
                ($associado['ativo'] ? 'Ativo' : 'Inativo') . '</span>';

        // Formatar contratos
        if ($associado['total_contratos'] > 0) {
            $contratos = '<a href="contratos.php?associado=' . $associado['id'] . '" class="badge bg-primary text-decoration-none">' .
                        $associado['total_contratos'] . '</a>';
        } else {
            $contratos = '<span class="badge bg-secondary">0</span>';
        }

        // Formatar ações - sempre mostra o botão de editar
        $acoes = '<div class="btn-group">' .
                '<button type="button" class="btn btn-sm btn-primary" onclick="editarAssociado(' . $associado['id'] . ')" title="Editar">' .
                '<i class="fas fa-edit"></i></button>';
        
        // Adiciona o botão de excluir apenas se for gestor e não tiver contratos
        if (TIPO_ACESSO_COBRANCA === 'GESTOR' && $associado['total_contratos'] == 0) {
            $acoes .= ' <button type="button" class="btn btn-sm btn-danger" ' .
                    'onclick="excluirAssociado(' . $associado['id'] . ')" title="Excluir">' .
                    '<i class="fas fa-trash"></i></button>';
        }
        $acoes .= '</div>';

        $data[] = array(
            htmlspecialchars($associado['nome']),
            $doc_formatado,
            htmlspecialchars($associado['pa_nome']) . ' (' . $associado['pa_numero'] . ')',
            $contratos,
            $status,
            date('d/m/Y', strtotime($associado['created_at'])),
            $acoes
        );
    }

    // Capturar qualquer saída indesejada
    $output = ob_get_clean();
    if (!empty($output)) {
        error_log("Saída indesejada detectada: " . $output);
    }

    // Retornar resultado no formato esperado pelo DataTables
    $response = array(
        "draw" => $draw,
        "recordsTotal" => $recordsTotal,
        "recordsFiltered" => $recordsFiltered,
        "data" => $data
    );

    // Limpar qualquer saída anterior
    if (ob_get_length()) ob_clean();
    
    // Garantir que os headers estejam corretos
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;

} catch (Exception $e) {
    // Limpar qualquer saída anterior
    if (ob_get_length()) ob_clean();
    
    // Log do erro
    error_log("Erro na listagem de associados: " . $e->getMessage());
    
    // Garantir que os headers estejam corretos
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    
    // Retornar erro em formato JSON
    echo json_encode(array(
        "draw" => isset($draw) ? $draw : 0,
        "recordsTotal" => 0,
        "recordsFiltered" => 0,
        "data" => array(),
        "error" => "Erro ao processar a requisição: " . $e->getMessage()
    ), JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
} 