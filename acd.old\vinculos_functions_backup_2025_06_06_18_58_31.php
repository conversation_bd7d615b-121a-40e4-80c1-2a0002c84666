<?php
/**
 * Funções para estrutura nova da tabela acd_usuario_pa
 * Onde usuario_api_id referencia usuários da API
 * Gerado automaticamente em 2025-06-06 18:47:37
 */

// Função para buscar usuários da API
function buscarTodosUsuarios() {
    $apiFields = [
        "api_user" => "UFL7GXZ14LU9NOR",
        "api_token" => "20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG",
        "api_module" => "Usuarios",
        "api_action" => "listarUsuarios"
    ];

    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => "https://intranet.sicoobcredilivre.com.br/api",
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_POSTFIELDS => http_build_query($apiFields),
    ));
    
    $response = curl_exec($curl);
    curl_close($curl);
    
    if (!$response) return [];
    
    $usuarios = json_decode($response, true);
    return is_array($usuarios) ? $usuarios : [];
}

function buscarVinculosAtivos() {
    global $pdo;
    
    try {
        // Buscar vínculos da tabela
        $sql = "
            SELECT 
                v.id,
                v.usuario_api_id,
                v.pa_id,
                v.data_inicio,
                p.nome as pa_nome,
                p.numero as pa_numero
            FROM acd_usuario_pa v
            INNER JOIN pontos_atendimento p ON v.pa_id = p.id
            WHERE v.status = \"ativo\"
            AND (v.data_fim IS NULL OR v.data_fim > CURDATE())
            ORDER BY p.nome
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $vinculos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Buscar dados dos usuários da API
        $usuarios_api = buscarTodosUsuarios();
        $usuarios_map = [];
        foreach ($usuarios_api as $usuario) {
            $usuarios_map[$usuario["id"]] = $usuario;
        }
        
        // Enriquecer vínculos com dados da API
        foreach ($vinculos as &$vinculo) {
            $usuario_api = $usuarios_map[$vinculo["usuario_api_id"]] ?? null;
            if ($usuario_api) {
                $vinculo["usuario_nome"] = $usuario_api["nome"] ?? "Nome não disponível";
                $vinculo["usuario_email"] = $usuario_api["email"] ?? "";
            } else {
                $vinculo["usuario_nome"] = "Usuário não encontrado (ID: " . $vinculo["usuario_api_id"] . ")";
                $vinculo["usuario_email"] = "";
            }
        }
        
        return $vinculos;
        
    } catch (Exception $e) {
        error_log("Erro em buscarVinculosAtivos: " . $e->getMessage());
        return [];
    }
}

function buscarPAsSemResponsavel() {
    global $pdo;
    
    try {
        $sql = "
            SELECT
                pa.id,
                pa.numero,
                pa.nome
            FROM pontos_atendimento pa
            LEFT JOIN acd_usuario_pa v ON (
                pa.id = v.pa_id
                AND v.status = \"ativo\"
                AND (v.data_fim IS NULL OR v.data_fim > CURDATE())
            )
            WHERE v.id IS NULL
            ORDER BY pa.numero
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Erro em buscarPAsSemResponsavel: " . $e->getMessage());
        return [];
    }
}

function criarVinculo($usuario_api_id, $pa_id, $data_inicio = null, $criado_por = null, $observacoes = null) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Verificar se o PA existe
        $stmt = $pdo->prepare("SELECT id, nome FROM pontos_atendimento WHERE id = ?");
        $stmt->execute([$pa_id]);
        $pa = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$pa) {
            $pdo->rollBack();
            return [
                "sucesso" => false,
                "erro" => "Ponto de Atendimento não encontrado (ID: " . $pa_id . ")"
            ];
        }
        
        // Verificar se o usuário existe na API
        $usuarios_api = buscarTodosUsuarios();
        $usuario_encontrado = null;
        foreach ($usuarios_api as $usuario) {
            if ($usuario["id"] == $usuario_api_id) {
                $usuario_encontrado = $usuario;
                break;
            }
        }
        
        if (!$usuario_encontrado) {
            $pdo->rollBack();
            return [
                "sucesso" => false,
                "erro" => "Usuário não encontrado na API (ID: " . $usuario_api_id . ")"
            ];
        }
        
        // Desativar vínculos ativos para este PA
        $stmt = $pdo->prepare("
            UPDATE acd_usuario_pa 
            SET status = \"inativo\",
                data_fim = ?,
                desativado_por = ?,
                desativado_em = NOW(),
                observacoes = CONCAT(IFNULL(observacoes, \"\"), \" | Substituído por novo responsável\")
            WHERE pa_id = ? 
            AND status = \"ativo\"
        ");
        $stmt->execute([$data_inicio ?? date("Y-m-d"), $criado_por ?? $_SESSION["user_id"] ?? 1, $pa_id]);
        
        // Criar novo vínculo
        $stmt = $pdo->prepare("
            INSERT INTO acd_usuario_pa (
                usuario_api_id, pa_id, data_inicio, status, 
                criado_por, criado_em, observacoes
            ) VALUES (?, ?, ?, \"ativo\", ?, NOW(), ?)
        ");
        
        $stmt->execute([
            $usuario_api_id, 
            $pa_id, 
            $data_inicio ?? date("Y-m-d"), 
            $criado_por ?? $_SESSION["user_id"] ?? 1, 
            $observacoes
        ]);
        
        $novo_vinculo_id = $pdo->lastInsertId();
        
        $pdo->commit();
        
        return [
            "sucesso" => true,
            "vinculo_id" => $novo_vinculo_id,
            "mensagem" => "Vínculo criado com sucesso: " . $usuario_encontrado["nome"] . " → " . $pa["nome"]
        ];
        
    } catch (Exception $e) {
        $pdo->rollBack();
        return [
            "sucesso" => false,
            "erro" => $e->getMessage()
        ];
    }
}

function desativarVinculo($vinculo_id, $data_fim = null, $desativado_por = null, $observacoes = null) {
    global $pdo;
    
    try {
        $sql = "
            UPDATE acd_usuario_pa 
            SET status = \"inativo\",
                data_fim = ?,
                desativado_por = ?,
                desativado_em = NOW(),
                observacoes = CONCAT(IFNULL(observacoes, \"\"), \" | \", ?)
            WHERE id = ? 
            AND status = \"ativo\"
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $data_fim ?? date("Y-m-d"), 
            $desativado_por ?? $_SESSION["user_id"] ?? 1, 
            $observacoes ?? "Desativado", 
            $vinculo_id
        ]);
        
        if ($stmt->rowCount() > 0) {
            return [
                "sucesso" => true,
                "mensagem" => "Vínculo desativado com sucesso"
            ];
        } else {
            return [
                "sucesso" => false,
                "erro" => "Vínculo não encontrado ou já inativo"
            ];
        }
        
    } catch (Exception $e) {
        return [
            "sucesso" => false,
            "erro" => $e->getMessage()
        ];
    }
}

function buscarHistoricoUsuario($usuario_api_id) {
    global $pdo;
    
    try {
        $sql = "
            SELECT 
                v.*,
                p.nome as pa_nome,
                p.numero as pa_numero
            FROM acd_usuario_pa v
            INNER JOIN pontos_atendimento p ON v.pa_id = p.id
            WHERE v.usuario_api_id = ?
            ORDER BY v.data_inicio DESC, v.criado_em DESC
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$usuario_api_id]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Erro em buscarHistoricoUsuario: " . $e->getMessage());
        return [];
    }
}

function buscarHistoricoPA($pa_id) {
    global $pdo;
    
    try {
        $sql = "
            SELECT 
                v.*,
                uc.nome_completo as criado_por_nome,
                ud.nome_completo as desativado_por_nome
            FROM acd_usuario_pa v
            LEFT JOIN usuarios uc ON v.criado_por = uc.id
            LEFT JOIN usuarios ud ON v.desativado_por = ud.id
            WHERE v.pa_id = ?
            ORDER BY v.data_inicio DESC, v.criado_em DESC
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$pa_id]);
        $historico = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Enriquecer com dados da API
        $usuarios_api = buscarTodosUsuarios();
        $usuarios_map = [];
        foreach ($usuarios_api as $usuario) {
            $usuarios_map[$usuario["id"]] = $usuario;
        }
        
        foreach ($historico as &$item) {
            $usuario_api = $usuarios_map[$item["usuario_api_id"]] ?? null;
            if ($usuario_api) {
                $item["usuario_nome"] = $usuario_api["nome"] ?? "Nome não disponível";
                $item["usuario_email"] = $usuario_api["email"] ?? "";
            } else {
                $item["usuario_nome"] = "Usuário não encontrado (ID: " . $item["usuario_api_id"] . ")";
                $item["usuario_email"] = "";
            }
        }
        
        return $historico;
    } catch (Exception $e) {
        error_log("Erro em buscarHistoricoPA: " . $e->getMessage());
        return [];
    }
}
?>