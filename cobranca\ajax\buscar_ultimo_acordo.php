<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

if (!isset($_POST['processo_id']) || empty($_POST['processo_id'])) {
    echo json_encode(['success' => false, 'message' => 'ID do processo não informado']);
    exit;
}

try {
    $processo_id = intval($_POST['processo_id']);

    // Buscar o último acordo do processo
    $stmt = $pdo->prepare("
        SELECT a.id, a.numero, a.valor_acordo, a.data_acordo
        FROM cbp_acordos a
        WHERE a.processo_id = :processo_id
        ORDER BY a.created_at DESC
        LIMIT 1
    ");
    
    $stmt->execute(['processo_id' => $processo_id]);
    $acordo = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($acordo) {
        // Formatar os dados do acordo
        $acordo['valor_acordo_formatado'] = 'R$ ' . number_format($acordo['valor_acordo'], 2, ',', '.');
        $acordo['data_acordo_formatada'] = date('d/m/Y', strtotime($acordo['data_acordo']));
        
        echo json_encode([
            'success' => true,
            'acordo' => $acordo
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'acordo' => null,
            'message' => 'Nenhum acordo encontrado para este processo'
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao buscar acordo: ' . $e->getMessage()
    ]);
} 