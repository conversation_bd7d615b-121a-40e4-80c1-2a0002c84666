<?php
/**
 * <PERSON>ript para reestruturar o sistema de vínculos para trabalhar corretamente com a API
 */

require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

// Verificar permissões
session_start();
if (!isset($_SESSION['user_id'])) {
    die("Acesso negado. Faça login para continuar.");
}

if (!checkACDPermission($_SESSION['user_id'])) {
    die("Acesso negado. Você não tem permissão para acessar o sistema ACD.");
}

if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    die("Acesso negado. Apenas administradores e gestores podem executar esta reestruturação.");
}

// Função para buscar usuários da API
function buscarTodosUsuarios() {
    $apiFields = [
        'api_user' => 'UFL7GXZ14LU9NOR',
        'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
        'api_module' => 'Usuarios',
        'api_action' => 'listarUsuarios'
    ];

    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => http_build_query($apiFields),
    ));
    
    $response = curl_exec($curl);
    curl_close($curl);
    
    if (!$response) return [];
    
    $usuarios = json_decode($response, true);
    return is_array($usuarios) ? $usuarios : [];
}

echo "<h2>🏗️ Reestruturação do Sistema de Vínculos</h2>";
echo "<p>Este script criará uma nova estrutura otimizada para trabalhar com a API.</p>";

try {
    $pdo->beginTransaction();
    
    echo "<h3>📋 Fase 1: Backup e Preparação</h3>";
    
    // 1. Verificar se tabela atual existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'acd_usuario_pa'");
    $tabela_existe = $stmt->rowCount() > 0;
    
    if ($tabela_existe) {
        // Fazer backup
        echo "<p>💾 Criando backup da tabela atual...</p>";
        $pdo->exec("DROP TABLE IF EXISTS acd_usuario_pa_backup_" . date('Y_m_d_H_i_s'));
        $pdo->exec("CREATE TABLE acd_usuario_pa_backup_" . date('Y_m_d_H_i_s') . " AS SELECT * FROM acd_usuario_pa");
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM acd_usuario_pa");
        $registros_backup = $stmt->fetchColumn();
        echo "<p>✅ Backup criado: $registros_backup registros</p>";
    }
    
    echo "<h3>🌐 Fase 2: Integração com API</h3>";
    
    // 2. Buscar usuários da API
    echo "<p>🔍 Buscando usuários da API...</p>";
    $usuarios_api = buscarTodosUsuarios();
    echo "<p>✅ Encontrados " . count($usuarios_api) . " usuários na API</p>";
    
    // 3. Criar nova estrutura
    echo "<h3>🏗️ Fase 3: Criando Nova Estrutura</h3>";
    
    // Remover tabela antiga se existir
    if ($tabela_existe) {
        echo "<p>🗑️ Removendo estrutura antiga...</p>";
        $pdo->exec("DROP TABLE acd_usuario_pa");
    }
    
    // Criar nova estrutura otimizada
    echo "<p>🔨 Criando nova estrutura...</p>";
    $sql_nova_estrutura = "
    CREATE TABLE acd_usuario_pa (
        id INT AUTO_INCREMENT PRIMARY KEY,
        usuario_api_id VARCHAR(50) NOT NULL COMMENT 'ID do usuário da API',
        pa_id INT NOT NULL COMMENT 'ID do Ponto de Atendimento',
        data_inicio DATE NOT NULL COMMENT 'Data de início da responsabilidade',
        data_fim DATE NULL COMMENT 'Data de fim (NULL = ativo)',
        status ENUM('ativo', 'inativo') DEFAULT 'ativo',
        criado_por INT NOT NULL COMMENT 'Usuário local que criou o vínculo',
        criado_em TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        desativado_por INT NULL COMMENT 'Usuário local que desativou',
        desativado_em TIMESTAMP NULL,
        observacoes TEXT NULL,
        
        -- Índices para performance
        INDEX idx_usuario_api_id (usuario_api_id),
        INDEX idx_pa_id (pa_id),
        INDEX idx_data_inicio (data_inicio),
        INDEX idx_status (status),
        INDEX idx_criado_por (criado_por),
        INDEX idx_desativado_por (desativado_por),
        
        -- Chaves estrangeiras (apenas para dados locais)
        FOREIGN KEY (pa_id) REFERENCES pontos_atendimento(id) ON DELETE CASCADE,
        FOREIGN KEY (criado_por) REFERENCES usuarios(id),
        FOREIGN KEY (desativado_por) REFERENCES usuarios(id),
        
        -- Constraint única para evitar duplicatas
        UNIQUE KEY unique_usuario_pa_ativo (usuario_api_id, pa_id, status)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
    COMMENT='Vínculos entre usuários da API e PAs locais'";
    
    $pdo->exec($sql_nova_estrutura);
    echo "<p>✅ Nova estrutura criada com sucesso!</p>";
    
    echo "<h3>📦 Fase 4: Migração de Dados (se houver)</h3>";
    
    // 4. Migrar dados do backup se existir
    if ($tabela_existe && $registros_backup > 0) {
        echo "<p>🔄 Tentando migrar dados existentes...</p>";
        
        // Criar mapeamento de usuários locais para API
        $mapeamento = [];
        $stmt = $pdo->query("SELECT id, username FROM usuarios WHERE username IS NOT NULL");
        $usuarios_locais = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($usuarios_locais as $usuario_local) {
            foreach ($usuarios_api as $usuario_api) {
                if (isset($usuario_api['loginAD']) && 
                    strcasecmp($usuario_local['username'], $usuario_api['loginAD']) === 0) {
                    $mapeamento[$usuario_local['id']] = $usuario_api['id'];
                    break;
                }
            }
        }
        
        echo "<p>📋 Mapeamento criado: " . count($mapeamento) . " usuários mapeados</p>";
        
        // Migrar dados mapeáveis
        $backup_table = "acd_usuario_pa_backup_" . date('Y_m_d_H_i_s');
        $migrados = 0;
        
        foreach ($mapeamento as $usuario_local_id => $usuario_api_id) {
            $sql_migrar = "
            INSERT INTO acd_usuario_pa (
                usuario_api_id, pa_id, data_inicio, status, 
                criado_por, criado_em, observacoes
            )
            SELECT 
                ? as usuario_api_id,
                pa_id,
                COALESCE(DATE(data_vinculo), CURDATE()) as data_inicio,
                CASE WHEN status = 1 THEN 'ativo' ELSE 'inativo' END as status,
                ? as criado_por,
                COALESCE(data_vinculo, NOW()) as criado_em,
                'Migrado da estrutura anterior' as observacoes
            FROM $backup_table
            WHERE usuario_id = ?
            AND EXISTS (SELECT 1 FROM pontos_atendimento WHERE id = pa_id)";
            
            $stmt = $pdo->prepare($sql_migrar);
            $stmt->execute([$usuario_api_id, $_SESSION['user_id'], $usuario_local_id]);
            $migrados += $stmt->rowCount();
        }
        
        echo "<p>✅ Migrados $migrados registros</p>";
    }
    
    echo "<h3>🧪 Fase 5: Teste da Nova Estrutura</h3>";
    
    // 5. Testar inserção
    if (!empty($usuarios_api)) {
        $usuario_teste = $usuarios_api[0];
        $stmt = $pdo->query("SELECT id FROM pontos_atendimento LIMIT 1");
        $pa_teste = $stmt->fetchColumn();
        
        if ($pa_teste) {
            echo "<p>🧪 Testando inserção...</p>";
            
            $stmt = $pdo->prepare("
                INSERT INTO acd_usuario_pa (
                    usuario_api_id, pa_id, data_inicio, 
                    criado_por, observacoes
                ) VALUES (?, ?, CURDATE(), ?, 'Teste de estrutura')
            ");
            
            $stmt->execute([
                $usuario_teste['id'], 
                $pa_teste, 
                $_SESSION['user_id']
            ]);
            
            $teste_id = $pdo->lastInsertId();
            echo "<p>✅ Teste de inserção bem-sucedido (ID: $teste_id)</p>";
            
            // Remover teste
            $pdo->prepare("DELETE FROM acd_usuario_pa WHERE id = ?")->execute([$teste_id]);
            echo "<p>🗑️ Registro de teste removido</p>";
        }
    }
    
    $pdo->commit();
    
    echo "<h3>✅ Reestruturação Concluída!</h3>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h4>🎉 Sucesso!</h4>";
    echo "<p>O sistema de vínculos foi reestruturado com sucesso:</p>";
    echo "<ul>";
    echo "<li>✅ Nova estrutura criada</li>";
    echo "<li>✅ Integração com API configurada</li>";
    echo "<li>✅ Dados migrados (quando possível)</li>";
    echo "<li>✅ Testes realizados</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h4>📋 Próximos Passos</h4>";
    echo "<ol>";
    echo "<li>Teste a criação de vínculos na página de gerenciamento</li>";
    echo "<li>Verifique se os dados estão sendo exibidos corretamente</li>";
    echo "<li>Se tudo estiver funcionando, os backups podem ser removidos</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    $pdo->rollBack();
    echo "<h3>❌ Erro durante a reestruturação:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<p>A transação foi revertida. Nenhuma alteração foi feita.</p>";
}

echo "<br><a href='gerenciar_vinculos.php'>← Voltar para Gerenciar Vínculos</a>";
?>
