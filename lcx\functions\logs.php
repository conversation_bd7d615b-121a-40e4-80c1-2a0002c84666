<?php
/**
 * Funções de Log para o Sistema LCX
 * Baseado no padrão de logs do sistema principal
 */

/**
 * Registra uma ação no log do sistema LCX
 * 
 * @param PDO $pdo Conexão com o banco de dados
 * @param string $acao Ação realizada (ex: 'criar_livro', 'nova_movimentacao', etc.)
 * @param string $detalhes Detalhes da ação realizada
 * @param int|null $livro_id ID do livro caixa relacionado (opcional)
 * @param int|null $movimentacao_id ID da movimentação relacionada (opcional)
 * @return bool True se o log foi registrado com sucesso, false caso contrário
 */
function registrar_log_lcx($pdo, $acao, $detalhes, $livro_id = null, $movimentacao_id = null) {
    try {
        $usuario_id = $_SESSION['user_id'] ?? null;
        if (!$usuario_id) {
            error_log("Tentativa de registrar log LCX sem usuário logado");
            return false;
        }

        // Adicionar prefixo LCX para identificar logs do sistema
        $acao_completa = "LCX: " . $acao;
        
        // Adicionar informações contextuais aos detalhes
        $detalhes_completos = $detalhes;
        if ($livro_id) {
            $detalhes_completos .= " | Livro ID: " . $livro_id;
        }
        if ($movimentacao_id) {
            $detalhes_completos .= " | Movimentação ID: " . $movimentacao_id;
        }
        
        // Adicionar IP do usuário
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'N/A';
        $detalhes_completos .= " | IP: " . $ip;

        $stmt = $pdo->prepare("
            INSERT INTO logs (usuario_id, acao, detalhes, data_hora) 
            VALUES (?, ?, ?, NOW())
        ");
        
        $resultado = $stmt->execute([$usuario_id, $acao_completa, $detalhes_completos]);
        
        if (!$resultado) {
            error_log("Falha ao registrar log LCX: " . implode(', ', $stmt->errorInfo()));
            return false;
        }
        
        return true;
        
    } catch (Exception $e) {
        error_log("Erro ao registrar log LCX: " . $e->getMessage());
        return false;
    }
}

/**
 * Registra log de criação de livro caixa
 */
function log_criar_livro($pdo, $livro_id, $nome_livro, $pa_nome, $tipo, $saldo_inicial) {
    $detalhes = "Novo livro caixa criado: '{$nome_livro}' | PA: {$pa_nome} | Tipo: {$tipo} | Saldo inicial: R$ " . number_format($saldo_inicial, 2, ',', '.');
    return registrar_log_lcx($pdo, 'criar_livro', $detalhes, $livro_id);
}

/**
 * Registra log de fechamento de livro caixa
 */
function log_fechar_livro($pdo, $livro_id, $nome_livro, $saldo_final, $total_paginas) {
    $detalhes = "Livro caixa fechado: '{$nome_livro}' | Saldo final: R$ " . number_format($saldo_final, 2, ',', '.') . " | Total de páginas: {$total_paginas}";
    return registrar_log_lcx($pdo, 'fechar_livro', $detalhes, $livro_id);
}

/**
 * Registra log de nova movimentação
 */
function log_nova_movimentacao($pdo, $livro_id, $movimentacao_id, $tipo, $valor, $categoria, $descricao = '') {
    $tipo_texto = $tipo === 'entrada' ? 'Entrada' : 'Saída';
    $valor_formatado = number_format($valor, 2, ',', '.');
    $detalhes = "Nova movimentação: {$tipo_texto} de R$ {$valor_formatado} | Categoria: {$categoria}";
    if ($descricao) {
        $detalhes .= " | Descrição: {$descricao}";
    }
    return registrar_log_lcx($pdo, 'nova_movimentacao', $detalhes, $livro_id, $movimentacao_id);
}

/**
 * Registra log de edição de movimentação
 */
function log_editar_movimentacao($pdo, $livro_id, $movimentacao_id, $dados_anteriores, $dados_novos) {
    $detalhes = "Movimentação editada | Dados anteriores: " . json_encode($dados_anteriores) . " | Dados novos: " . json_encode($dados_novos);
    return registrar_log_lcx($pdo, 'editar_movimentacao', $detalhes, $livro_id, $movimentacao_id);
}

/**
 * Registra log de exclusão de movimentação
 */
function log_excluir_movimentacao($pdo, $livro_id, $movimentacao_id, $dados_movimentacao, $motivo = '') {
    $detalhes = "Movimentação excluída | Dados: " . json_encode($dados_movimentacao);
    if ($motivo) {
        $detalhes .= " | Motivo: {$motivo}";
    }
    return registrar_log_lcx($pdo, 'excluir_movimentacao', $detalhes, $livro_id, $movimentacao_id);
}

/**
 * Registra log de alteração de permissões LCX
 */
function log_alterar_permissao($pdo, $usuario_alterado_id, $nome_usuario, $nivel_anterior, $nivel_novo, $motivo = '') {
    $detalhes = "Permissão LCX alterada para '{$nome_usuario}' | De: {$nivel_anterior} | Para: {$nivel_novo}";
    if ($motivo) {
        $detalhes .= " | Motivo: {$motivo}";
    }
    return registrar_log_lcx($pdo, 'alterar_permissao', $detalhes);
}

/**
 * Registra log de designação de tesoureiro
 */
function log_designar_tesoureiro($pdo, $usuario_id, $nome_usuario, $pa_nome, $motivo = '') {
    $detalhes = "Tesoureiro designado: '{$nome_usuario}' para PA: {$pa_nome}";
    if ($motivo) {
        $detalhes .= " | Motivo: {$motivo}";
    }
    return registrar_log_lcx($pdo, 'designar_tesoureiro', $detalhes);
}

/**
 * Registra log de remoção de tesoureiro
 */
function log_remover_tesoureiro($pdo, $usuario_id, $nome_usuario, $pa_nome, $motivo = '') {
    $detalhes = "Tesoureiro removido: '{$nome_usuario}' do PA: {$pa_nome}";
    if ($motivo) {
        $detalhes .= " | Motivo: {$motivo}";
    }
    return registrar_log_lcx($pdo, 'remover_tesoureiro', $detalhes);
}

/**
 * Registra log de impressão de livro
 */
function log_imprimir_livro($pdo, $livro_id, $nome_livro, $tipo_impressao = 'completa') {
    $detalhes = "Livro caixa impresso: '{$nome_livro}' | Tipo: {$tipo_impressao}";
    return registrar_log_lcx($pdo, 'imprimir_livro', $detalhes, $livro_id);
}

/**
 * Registra log de importação de dados
 */
function log_importar_dados($pdo, $livro_id, $nome_livro, $total_registros, $arquivo_origem = '') {
    $detalhes = "Dados importados para '{$nome_livro}' | Total de registros: {$total_registros}";
    if ($arquivo_origem) {
        $detalhes .= " | Arquivo: {$arquivo_origem}";
    }
    return registrar_log_lcx($pdo, 'importar_dados', $detalhes, $livro_id);
}

/**
 * Registra log de acesso negado
 */
function log_acesso_negado($pdo, $pagina, $motivo = '') {
    $detalhes = "Acesso negado à página: {$pagina}";
    if ($motivo) {
        $detalhes .= " | Motivo: {$motivo}";
    }
    return registrar_log_lcx($pdo, 'acesso_negado', $detalhes);
}

/**
 * Registra log de login no sistema LCX
 */
function log_acesso_lcx($pdo, $pagina) {
    $detalhes = "Acesso ao sistema LCX | Página: {$pagina}";
    return registrar_log_lcx($pdo, 'acesso_sistema', $detalhes);
}

/**
 * Registra log de habilitação de edição retroativa geral
 */
function log_habilitar_edicao_geral($pdo, $livro_id, $nome_livro, $pa_nome) {
    $detalhes = "Edição retroativa GERAL habilitada para livro '{$nome_livro}' | PA: {$pa_nome} | Permite editar movimentações de qualquer data";
    return registrar_log_lcx($pdo, 'habilitar_edicao_geral', $detalhes, $livro_id);
}

/**
 * Registra log de habilitação de edição retroativa específica
 */
function log_habilitar_edicao_especifica($pdo, $livro_id, $nome_livro, $pa_nome, $data_especifica) {
    $data_formatada = date('d/m/Y', strtotime($data_especifica));
    $detalhes = "Edição retroativa ESPECÍFICA habilitada para livro '{$nome_livro}' | PA: {$pa_nome} | Data habilitada: {$data_formatada} | Permite editar apenas movimentações desta data";
    return registrar_log_lcx($pdo, 'habilitar_edicao_especifica', $detalhes, $livro_id);
}

/**
 * Registra log de desabilitação de edição retroativa
 */
function log_desabilitar_edicao($pdo, $livro_id, $nome_livro, $pa_nome, $tipo_anterior = '') {
    $detalhes = "Edição retroativa DESABILITADA para livro '{$nome_livro}' | PA: {$pa_nome}";
    if ($tipo_anterior) {
        $detalhes .= " | Tipo anterior: {$tipo_anterior}";
    }
    return registrar_log_lcx($pdo, 'desabilitar_edicao', $detalhes, $livro_id);
}

/**
 * Registra log de edição de movimentação retroativa
 */
function log_edicao_retroativa($pdo, $livro_id, $movimentacao_id, $data_movimentacao, $dados_anteriores, $dados_novos, $tipo_edicao = '') {
    $data_formatada = date('d/m/Y', strtotime($data_movimentacao));
    $detalhes = "Movimentação RETROATIVA editada | Data: {$data_formatada}";
    if ($tipo_edicao) {
        $detalhes .= " | Tipo de edição: {$tipo_edicao}";
    }
    $detalhes .= " | Dados anteriores: " . json_encode($dados_anteriores) . " | Dados novos: " . json_encode($dados_novos);
    return registrar_log_lcx($pdo, 'edicao_retroativa', $detalhes, $livro_id, $movimentacao_id);
}

/**
 * Busca logs do sistema LCX com filtros
 * 
 * @param PDO $pdo Conexão com o banco
 * @param array $filtros Array com filtros (data_inicio, data_fim, usuario_id, acao, livro_id)
 * @param int $limite Limite de registros
 * @param int $offset Offset para paginação
 * @return array Array com os logs encontrados
 */
function buscar_logs_lcx($pdo, $filtros = [], $limite = 50, $offset = 0) {
    try {
        $where_conditions = ["l.acao LIKE 'LCX:%'"];
        $params = [];
        
        if (!empty($filtros['data_inicio'])) {
            $where_conditions[] = "l.data_hora >= ?";
            $params[] = $filtros['data_inicio'] . ' 00:00:00';
        }
        
        if (!empty($filtros['data_fim'])) {
            $where_conditions[] = "l.data_hora <= ?";
            $params[] = $filtros['data_fim'] . ' 23:59:59';
        }
        
        if (!empty($filtros['usuario_id'])) {
            $where_conditions[] = "l.usuario_id = ?";
            $params[] = $filtros['usuario_id'];
        }
        
        if (!empty($filtros['acao'])) {
            $where_conditions[] = "l.acao LIKE ?";
            $params[] = 'LCX: ' . $filtros['acao'] . '%';
        }
        
        if (!empty($filtros['livro_id'])) {
            $where_conditions[] = "l.detalhes LIKE ?";
            $params[] = '%Livro ID: ' . $filtros['livro_id'] . '%';
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $sql = "
            SELECT l.*, u.nome_completo as usuario_nome
            FROM logs l
            JOIN usuarios u ON l.usuario_id = u.id
            WHERE {$where_clause}
            ORDER BY l.data_hora DESC
            LIMIT {$limite} OFFSET {$offset}
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Erro ao buscar logs LCX: " . $e->getMessage());
        return [];
    }
}

/**
 * Conta total de logs LCX com filtros
 */
function contar_logs_lcx($pdo, $filtros = []) {
    try {
        $where_conditions = ["acao LIKE 'LCX:%'"];
        $params = [];
        
        if (!empty($filtros['data_inicio'])) {
            $where_conditions[] = "data_hora >= ?";
            $params[] = $filtros['data_inicio'] . ' 00:00:00';
        }
        
        if (!empty($filtros['data_fim'])) {
            $where_conditions[] = "data_hora <= ?";
            $params[] = $filtros['data_fim'] . ' 23:59:59';
        }
        
        if (!empty($filtros['usuario_id'])) {
            $where_conditions[] = "usuario_id = ?";
            $params[] = $filtros['usuario_id'];
        }
        
        if (!empty($filtros['acao'])) {
            $where_conditions[] = "acao LIKE ?";
            $params[] = 'LCX: ' . $filtros['acao'] . '%';
        }
        
        if (!empty($filtros['livro_id'])) {
            $where_conditions[] = "detalhes LIKE ?";
            $params[] = '%Livro ID: ' . $filtros['livro_id'] . '%';
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $sql = "SELECT COUNT(*) FROM logs WHERE {$where_clause}";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchColumn();
        
    } catch (Exception $e) {
        error_log("Erro ao contar logs LCX: " . $e->getMessage());
        return 0;
    }
}
?>
