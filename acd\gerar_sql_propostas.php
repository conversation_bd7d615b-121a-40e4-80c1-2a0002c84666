<?php
require_once '../config/database.php';
require_once '../config/funcoes.php';
session_start();

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

$mensagem = '';
$tipo_mensagem = '';

// Processar upload do arquivo
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['arquivo'])) {
    $arquivo = $_FILES['arquivo'];
    
    // Validar tipo de arquivo
    $tipos_permitidos = ['csv'];
    $extensao = strtolower(pathinfo($arquivo['name'], PATHINFO_EXTENSION));
    
    if (!in_array($extensao, $tipos_permitidos)) {
        $mensagem = "Tipo de arquivo não permitido. Use apenas CSV.";
        $tipo_mensagem = "danger";
    } elseif ($arquivo['error'] !== UPLOAD_ERR_OK) {
        $mensagem = "Erro no upload do arquivo. Código do erro: " . $arquivo['error'];
        $tipo_mensagem = "danger";
    } else {
        try {
            // Função para detectar o delimitador
            function detectDelimiter($file) {
                $delimiters = [',', ';', '\t', '|'];
                $firstLine = fgets($file);
                rewind($file);
                
                $counts = [];
                foreach ($delimiters as $delimiter) {
                    $counts[$delimiter] = substr_count($firstLine, $delimiter);
                }
                
                $maxCount = max($counts);
                $detected = $maxCount > 0 ? array_search($maxCount, $counts) : ',';
                
                // Log da detecção do delimitador
                error_log("Primeira linha do arquivo: " . $firstLine);
                error_log("Contagem de delimitadores: " . print_r($counts, true));
                error_log("Delimitador detectado: " . $detected);
                
                return $detected;
            }

            // Abrir o arquivo CSV
            $handle = fopen($arquivo['tmp_name'], "r");
            if ($handle !== FALSE) {
                // Detectar o delimitador
                $delimiter = detectDelimiter($handle);
                
                // Ler o cabeçalho
                $cabecalho = fgetcsv($handle, 0, $delimiter);
                if (!$cabecalho) {
                    throw new Exception("Não foi possível ler o cabeçalho do arquivo CSV");
                }
                
                // Log do cabeçalho
                error_log("Cabeçalho detectado: " . print_r($cabecalho, true));

                // Preparar o arquivo SQL
                $sql_filename = 'propostas_' . date('Y-m-d_H-i-s') . '.sql';
                $sql_content = "-- Arquivo SQL gerado em " . date('Y-m-d H:i:s') . "\n";
                $sql_content .= "-- Total de registros: 0\n\n";
                $sql_content .= "SET NAMES utf8mb4;\n";
                $sql_content .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";
                $sql_content .= "TRUNCATE TABLE acd_formularios;\n\n";

                $linhas_processadas = 0;
                $erros = [];
                $linha_atual = 0;
                
                // Processar cada linha
                while (($data = fgetcsv($handle, 0, $delimiter)) !== FALSE) {
                    $linha_atual++;
                    
                    // Verificar se a linha está vazia
                    if (empty(array_filter($data, 'trim'))) {
                        continue;
                    }
                    
                    // Verificar número de colunas
                    if (count($data) !== 9) {
                        $erros[] = "Linha $linha_atual: Número incorreto de colunas (" . count($data) . " encontradas, 9 esperadas)";
                        continue;
                    }

                    try {
                        // Mapear os campos
                        $usuario_id = trim($data[0]);
                        $documento = trim($data[1]);
                        $nome_cliente = trim($data[2]);
                        $mesa = trim($data[3]);
                        $usuario_pa = trim($data[4]);
                        $pa = trim($data[5]);
                        $acao = trim($data[6]);
                        $motivo_devolucao = !empty($data[7]) ? trim($data[7]) : null;
                        $data_criacao = !empty($data[8]) ? trim($data[8]) : null;

                        // Log dos primeiros registros para debug
                        if ($linha_atual <= 5) {
                            error_log("Linha $linha_atual - Dados brutos: " . print_r($data, true));
                            error_log("Linha $linha_atual - PA original: '" . $data[5] . "'");
                            error_log("Linha $linha_atual - PA após trim: '" . $pa . "'");
                        }

                        // Validar campos obrigatórios
                        $campos_obrigatorios = [
                            'ID Usuário' => $usuario_id,
                            'Documento' => $documento,
                            'Nome Cliente' => $nome_cliente,
                            'Tipo Operação' => $mesa,
                            'Usuário PA' => $usuario_pa,
                            'PA' => ($pa === '0' || !empty($pa)) ? $pa : null
                        ];
                        
                        $campos_ausentes = [];
                        foreach ($campos_obrigatorios as $nome => $valor) {
                            if ($valor === null) {
                                $campos_ausentes[] = $nome;
                            }
                        }
                        
                        if (!empty($campos_ausentes)) {
                            $erros[] = sprintf(
                                "Linha %d: Campos obrigatórios ausentes: %s\nConteúdo da linha: [%s]",
                                $linha_atual,
                                implode(', ', $campos_ausentes),
                                implode(' | ', array_map(function($valor) {
                                    return '"' . addslashes($valor) . '"';
                                }, $data))
                            );
                            continue;
                        }

                        // Preparar valores para o SQL
                        $motivo_devolucao_sql = $motivo_devolucao ? "'" . addslashes($motivo_devolucao) . "'" : 'NULL';
                        $data_criacao_sql = $data_criacao ? "'" . addslashes($data_criacao) . "'" : 'NULL';

                        // Gerar comando INSERT
                        $sql_content .= "INSERT INTO acd_formularios (usuario_id, documento, nome, mesa, usuario_pa, pa, acao, motivo_devolucao, data_criacao) VALUES (\n";
                        $sql_content .= "    '" . addslashes($usuario_id) . "',\n";
                        $sql_content .= "    '" . addslashes($documento) . "',\n";
                        $sql_content .= "    '" . addslashes($nome_cliente) . "',\n";
                        $sql_content .= "    '" . addslashes($mesa) . "',\n";
                        $sql_content .= "    '" . addslashes($usuario_pa) . "',\n";
                        $sql_content .= "    '" . addslashes($pa) . "',\n";
                        $sql_content .= "    '" . addslashes($acao) . "',\n";
                        $sql_content .= "    " . $motivo_devolucao_sql . ",\n";
                        $sql_content .= "    " . $data_criacao_sql . "\n";
                        $sql_content .= ");\n\n";

                        $linhas_processadas++;

                    } catch (Exception $e) {
                        $erros[] = "Linha $linha_atual: " . $e->getMessage();
                        continue;
                    }
                }
                fclose($handle);

                // Atualizar o total de registros no cabeçalho do SQL
                $sql_content = str_replace(
                    "-- Total de registros: 0",
                    "-- Total de registros: $linhas_processadas",
                    $sql_content
                );

                // Adicionar finalização do arquivo SQL
                $sql_content .= "\nSET FOREIGN_KEY_CHECKS = 1;\n";

                // Salvar o arquivo SQL
                $sql_path = '../temp/' . $sql_filename;
                if (!is_dir('../temp')) {
                    mkdir('../temp', 0777, true);
                }
                
                if (file_put_contents($sql_path, $sql_content)) {
                    $mensagem = "Arquivo SQL gerado com sucesso! $linhas_processadas registros processados.";
                    if (count($erros) > 0) {
                        $mensagem .= "<br>Alguns erros foram encontrados:<br>" . implode("<br>", $erros);
                        $tipo_mensagem = "warning";
                    } else {
                        $tipo_mensagem = "success";
                    }
                    
                    // Adicionar link para download
                    $mensagem .= "<br><br><a href='download_sql.php?file=" . urlencode($sql_filename) . "' class='btn btn-primary'>Download do arquivo SQL</a>";
                } else {
                    throw new Exception("Erro ao salvar o arquivo SQL");
                }

            } else {
                throw new Exception("Não foi possível abrir o arquivo para leitura");
            }
        } catch (Exception $e) {
            $mensagem = "Erro ao processar arquivo: " . $e->getMessage();
            $tipo_mensagem = "danger";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="../assets/images/Sicoob.ico">
    <title>Gerar SQL de Propostas - Análise de Crédito</title>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <style>
        :root {
            --sicoob-turquesa: #00AE9D;
            --sicoob-verde-escuro: #003641;
            --sicoob-branco: #FFFFFF;
            --sicoob-verde-claro: #C9D200;
            --sicoob-verde-medio: #7DB61C;
            --sicoob-roxo: #49479D;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .wrapper {
            display: flex;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .content-container {
            background: var(--sicoob-branco);
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 8px rgba(0, 54, 65, 0.1);
            border-top: 4px solid var(--sicoob-turquesa);
        }

        .content-title {
            color: var(--sicoob-verde-escuro);
            margin-bottom: 2rem;
            font-weight: 600;
            position: relative;
            padding-bottom: 1rem;
        }

        .content-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--sicoob-turquesa);
        }

        .upload-area {
            border: 2px dashed var(--sicoob-turquesa);
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            background: rgba(0, 174, 157, 0.05);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            background: rgba(0, 174, 157, 0.1);
        }

        .upload-area i {
            font-size: 3rem;
            color: var(--sicoob-turquesa);
            margin-bottom: 1rem;
        }

        .btn-primary {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
        }

        .btn-primary:hover {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
        }

        .alert {
            border-radius: 4px;
            border: none;
        }

        .alert-success {
            background-color: rgba(125, 182, 28, 0.1);
            color: var(--sicoob-verde-medio);
        }

        .alert-danger {
            background-color: rgba(73, 71, 157, 0.1);
            color: var(--sicoob-roxo);
        }

        .alert-warning {
            background-color: rgba(201, 210, 0, 0.1);
            color: var(--sicoob-verde-claro);
        }

        .file-info {
            margin-top: 1rem;
            font-size: 0.9rem;
            color: var(--sicoob-verde-escuro);
            text-align: left;
            padding: 1rem;
            background: rgba(0, 54, 65, 0.05);
            border-radius: 4px;
        }

        .file-info strong {
            color: var(--sicoob-verde-escuro);
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'components/sidebar.php'; ?>

        <div class="main-content">
            <div class="content-container">
                <h2 class="content-title">Gerar SQL de Propostas</h2>

                <?php if ($mensagem): ?>
                    <div class="alert alert-<?php echo $tipo_mensagem; ?> alert-dismissible fade show" role="alert">
                        <i class="fas <?php 
                            echo $tipo_mensagem === 'success' ? 'fa-check-circle' : 
                                ($tipo_mensagem === 'warning' ? 'fa-exclamation-triangle' : 'fa-exclamation-circle'); 
                        ?> me-2"></i>
                        <?php echo $mensagem; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    <div class="upload-area" id="dropZone">
                        <i class="fas fa-file-code"></i>
                        <h4>Arraste seu arquivo CSV aqui ou clique para selecionar</h4>
                        <p class="text-muted">Formato aceito: CSV</p>
                        <input type="file" name="arquivo" id="arquivo" class="d-none" accept=".csv">
                        <div class="file-info" id="fileInfo"></div>
                    </div>

                    <div class="mt-4">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-file-code me-2"></i>Gerar Arquivo SQL
                        </button>
                    </div>
                </form>

                <div class="mt-4">
                    <h5 class="mb-3">Formato do Arquivo CSV</h5>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Campo</th>
                                    <th>Descrição</th>
                                    <th>Exemplo</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>ID Usuário</td>
                                    <td>ID do usuário no sistema</td>
                                    <td>28</td>
                                </tr>
                                <tr>
                                    <td>Documento</td>
                                    <td>CPF do cliente</td>
                                    <td>615.899.186-49</td>
                                </tr>
                                <tr>
                                    <td>Nome Cliente</td>
                                    <td>Nome do cliente</td>
                                    <td>CONCEICAO DE FATIMA BENTO</td>
                                </tr>
                                <tr>
                                    <td>Tipo Operação</td>
                                    <td>Tipo da operação</td>
                                    <td>Emprestimo</td>
                                </tr>
                                <tr>
                                    <td>Código Usuário</td>
                                    <td>Código do usuário na API</td>
                                    <td>WILLIANJ3049_11</td>
                                </tr>
                                <tr>
                                    <td>PA</td>
                                    <td>Nome do PA</td>
                                    <td>11</td>
                                </tr>
                                <tr>
                                    <td>Ação</td>
                                    <td>Ação realizada</td>
                                    <td>Submeter</td>
                                </tr>
                                <tr>
                                    <td>Motivo Devolução</td>
                                    <td>Motivo da devolução (opcional)</td>
                                    <td>-</td>
                                </tr>
                                <tr>
                                    <td>Data Criação</td>
                                    <td>Data e hora de criação</td>
                                    <td>01/02/2025 09:15</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#dropZone').on('click', function(e) {
                if (e.target === this) {
                    $('#arquivo').trigger('click');
                }
            });

            $('#arquivo').on('change', function() {
                if (this.files.length) {
                    updateFileInfo(this.files[0]);
                }
            });

            $('#dropZone')
                .on('dragover', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    $(this).addClass('bg-light');
                })
                .on('dragleave', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    $(this).removeClass('bg-light');
                })
                .on('drop', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    $(this).removeClass('bg-light');
                    
                    const files = e.originalEvent.dataTransfer.files;
                    if (files.length) {
                        $('#arquivo').prop('files', files);
                        updateFileInfo(files[0]);
                    }
                });

            function updateFileInfo(file) {
                const fileInfo = $('#fileInfo');
                const size = (file.size / 1024).toFixed(2);
                fileInfo.html(`
                    <strong>Arquivo selecionado:</strong><br>
                    Nome: ${file.name}<br>
                    Tamanho: ${size} KB
                `);
            }
        });
    </script>
</body>
</html> 