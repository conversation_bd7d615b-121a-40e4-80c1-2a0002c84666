<?php
/**
 * Script para aplicar correção para estrutura antiga
 * Substitui as funções problemáticas por versões compatíveis
 */

require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

session_start();
if (!isset($_SESSION['user_id'])) {
    die("Acesso negado. Faça login para continuar.");
}

if (!checkACDPermission($_SESSION['user_id'])) {
    die("Acesso negado. Você não tem permissão para acessar o sistema ACD.");
}

if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    die("Acesso negado. Apenas administradores e gestores podem executar esta correção.");
}

echo "<h2>🔧 Correção para Estrutura Antiga</h2>";
echo "<p>Este script corrige as funções para trabalhar com a estrutura antiga onde usuario_id e pa_id são do banco local.</p>";

try {
    echo "<h3>📋 Verificando Estrutura</h3>";
    
    // Verificar estrutura da tabela
    $stmt = $pdo->query("DESCRIBE acd_usuario_pa");
    $colunas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $campos = array_column($colunas, 'Field');
    
    echo "<p>Campos encontrados: " . implode(', ', $campos) . "</p>";
    
    $tem_usuario_id = in_array('usuario_id', $campos);
    $tem_pa_id = in_array('pa_id', $campos);
    $tem_usuario_vinculo = in_array('usuario_vinculo', $campos);
    
    if (!$tem_usuario_id || !$tem_pa_id) {
        echo "<p>❌ Estrutura não compatível. Campos usuario_id e pa_id são obrigatórios.</p>";
        exit;
    }
    
    echo "<p>✅ Estrutura antiga confirmada</p>";
    
    // Verificar dados existentes
    echo "<h3>📊 Verificando Dados</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM acd_usuario_pa");
    $total_vinculos = $stmt->fetchColumn();
    echo "<p>Total de vínculos: $total_vinculos</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM usuarios");
    $total_usuarios = $stmt->fetchColumn();
    echo "<p>Total de usuários locais: $total_usuarios</p>";
    
    $stmt = $pdo->query("SELECT COUNT(*) FROM pontos_atendimento");
    $total_pas = $stmt->fetchColumn();
    echo "<p>Total de PAs: $total_pas</p>";
    
    // Verificar constraint problemática
    if ($tem_usuario_vinculo) {
        echo "<h3>🔍 Verificando Constraint Problemática</h3>";
        
        $stmt = $pdo->query("
            SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'acd_usuario_pa' 
            AND CONSTRAINT_NAME = 'acd_usuario_pa_ibfk_3'
        ");
        
        if ($stmt->rowCount() > 0) {
            echo "<p>⚠️ Constraint problemática encontrada</p>";
            
            if (isset($_POST['remover_constraint'])) {
                try {
                    $pdo->exec("ALTER TABLE acd_usuario_pa DROP FOREIGN KEY acd_usuario_pa_ibfk_3");
                    echo "<p>✅ Constraint removida</p>";
                } catch (Exception $e) {
                    echo "<p>⚠️ Erro ao remover constraint: " . $e->getMessage() . "</p>";
                }
            } else {
                echo "<form method='POST' style='display: inline;'>";
                echo "<button type='submit' name='remover_constraint' class='btn btn-warning'>🗑️ Remover Constraint</button>";
                echo "</form>";
            }
        } else {
            echo "<p>✅ Nenhuma constraint problemática encontrada</p>";
        }
    }
    
    echo "<h3>🔄 Aplicando Correção</h3>";
    
    if (isset($_POST['aplicar_correcao'])) {
        // Fazer backup do arquivo original
        if (file_exists('vinculos_functions.php')) {
            $backup_name = 'vinculos_functions_backup_' . date('Y_m_d_H_i_s') . '.php';
            copy('vinculos_functions.php', $backup_name);
            echo "<p>💾 Backup criado: $backup_name</p>";
        }
        
        // Copiar funções compatíveis
        if (file_exists('vinculos_functions_estrutura_antiga.php')) {
            copy('vinculos_functions_estrutura_antiga.php', 'vinculos_functions.php');
            echo "<p>✅ Funções atualizadas com sucesso!</p>";
            
            // Testar as novas funções
            echo "<h3>🧪 Testando Funções</h3>";
            
            include 'vinculos_functions.php';
            
            // Teste 1: buscarVinculosAtivos
            echo "<p>🔍 Testando buscarVinculosAtivos()...</p>";
            try {
                $vinculos = buscarVinculosAtivos();
                echo "<p>✅ Sucesso: " . count($vinculos) . " vínculos encontrados</p>";
            } catch (Exception $e) {
                echo "<p>❌ Erro: " . $e->getMessage() . "</p>";
            }
            
            // Teste 2: buscarPAsSemResponsavel
            echo "<p>🔍 Testando buscarPAsSemResponsavel()...</p>";
            try {
                $pas_sem_responsavel = buscarPAsSemResponsavel();
                echo "<p>✅ Sucesso: " . count($pas_sem_responsavel) . " PAs sem responsável</p>";
            } catch (Exception $e) {
                echo "<p>❌ Erro: " . $e->getMessage() . "</p>";
            }
            
            // Teste 3: Criar vínculo (se houver dados)
            if ($total_usuarios > 0 && $total_pas > 0) {
                echo "<p>🔍 Testando criarVinculo()...</p>";
                
                $stmt = $pdo->query("SELECT id FROM usuarios LIMIT 1");
                $usuario_teste = $stmt->fetchColumn();
                
                $stmt = $pdo->query("SELECT id FROM pontos_atendimento LIMIT 1");
                $pa_teste = $stmt->fetchColumn();
                
                try {
                    $resultado = criarVinculo($usuario_teste, $pa_teste, date('Y-m-d'), $_SESSION['user_id'], 'Teste de função');
                    
                    if ($resultado['sucesso']) {
                        echo "<p>✅ Teste de criação bem-sucedido</p>";
                        
                        // Remover teste
                        $pdo->prepare("DELETE FROM acd_usuario_pa WHERE id = ?")->execute([$resultado['vinculo_id']]);
                        echo "<p>🗑️ Registro de teste removido</p>";
                    } else {
                        echo "<p>⚠️ Teste falhou: " . $resultado['erro'] . "</p>";
                    }
                } catch (Exception $e) {
                    echo "<p>❌ Erro no teste: " . $e->getMessage() . "</p>";
                }
            }
            
            echo "<h3>✅ Correção Concluída!</h3>";
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h4>🎉 Sucesso!</h4>";
            echo "<p>As funções foram corrigidas para trabalhar com a estrutura antiga:</p>";
            echo "<ul>";
            echo "<li>✅ Funções compatíveis aplicadas</li>";
            echo "<li>✅ Backup do arquivo original criado</li>";
            echo "<li>✅ Testes realizados</li>";
            echo "</ul>";
            echo "<p><strong>Agora você pode usar o sistema normalmente!</strong></p>";
            echo "</div>";
            
            echo "<p><a href='gerenciar_vinculos.php' class='btn btn-success'>🔗 Testar Gerenciar Vínculos</a></p>";
            
        } else {
            echo "<p>❌ Arquivo de funções compatíveis não encontrado!</p>";
        }
        
    } else {
        echo "<p>As funções serão atualizadas para trabalhar com a estrutura antiga onde:</p>";
        echo "<ul>";
        echo "<li>✅ usuario_id referencia a tabela usuarios local</li>";
        echo "<li>✅ pa_id referencia a tabela pontos_atendimento local</li>";
        echo "<li>✅ Mantém compatibilidade com campos existentes</li>";
        echo "</ul>";
        
        echo "<form method='POST'>";
        echo "<button type='submit' name='aplicar_correcao' class='btn btn-primary'>🔄 Aplicar Correção</button>";
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Erro:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "<br><a href='gerenciar_vinculos.php'>← Voltar para Gerenciar Vínculos</a>";
?>

<style>
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    color: white; 
    text-decoration: none; 
    border-radius: 4px; 
    border: none;
    cursor: pointer;
    margin: 5px;
}
.btn-primary { background: #007bff; }
.btn-success { background: #28a745; }
.btn-warning { background: #ffc107; color: #212529; }
.btn:hover { opacity: 0.8; }
</style>
