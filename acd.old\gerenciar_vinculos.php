<?php
require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';
require_once 'vinculos_functions.php';
session_start();

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se o usuário tem permissão para acessar a pasta ACD
if (!checkACDPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Verificar se o usuário é administrador ou gestor
if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Processar formulário de vínculo
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $user_id = $_SESSION['user_id'];

        try {
            if ($_POST['action'] === 'vincular') {
                // Verificar se os campos obrigatórios existem
                if (!isset($_POST['usuario_id']) || !isset($_POST['pa_id'])) {
                    $mensagem = "Erro: Campos obrigatórios não informados (usuário e PA)";
                    $tipo_mensagem = "danger";
                } else {
                    $usuario_id = $_POST['usuario_id'];
                    $pa_id = $_POST['pa_id'];
                    $data_inicio = $_POST['data_inicio'] ?? date('Y-m-d');
                    $observacoes = $_POST['observacoes'] ?? null;

                    $resultado = criarVinculo($usuario_id, $pa_id, $data_inicio, $user_id, $observacoes);

                    if ($resultado['sucesso']) {
                        $mensagem = $resultado['mensagem'];
                        $tipo_mensagem = "success";
                    } else {
                        $mensagem = "Erro ao criar vínculo: " . $resultado['erro'];
                        $tipo_mensagem = "danger";
                    }
                }
            } elseif ($_POST['action'] === 'desvincular') {
                // Verificar se o campo obrigatório existe
                if (!isset($_POST['vinculo_id'])) {
                    $mensagem = "Erro: ID do vínculo não informado";
                    $tipo_mensagem = "danger";
                } else {
                    $vinculo_id = $_POST['vinculo_id'];
                    $data_fim = $_POST['data_fim'] ?? date('Y-m-d');
                    $observacoes = $_POST['observacoes_desativacao'] ?? 'Vínculo desativado pelo administrador';

                    $resultado = desativarVinculo($vinculo_id, $data_fim, $user_id, $observacoes);

                    if ($resultado['sucesso']) {
                        $mensagem = $resultado['mensagem'];
                        $tipo_mensagem = "success";
                    } else {
                        $mensagem = "Erro ao desativar vínculo: " . $resultado['erro'];
                        $tipo_mensagem = "danger";
                    }
                }
            }
        } catch (Exception $e) {
            $mensagem = "Erro ao processar vínculo: " . $e->getMessage();
            $tipo_mensagem = "danger";
        }
    }
}

// Buscar vínculos ativos usando a nova função
try {
    if (function_exists('buscarVinculosAtivos')) {
        $vinculos = buscarVinculosAtivos();
    } else {
        $vinculos = [];
        $mensagem = "Funções de vínculos não encontradas. Execute a correção de estrutura.";
        $tipo_mensagem = "warning";
    }
} catch (Exception $e) {
    $vinculos = [];
    $mensagem = "Erro ao buscar vínculos: " . $e->getMessage();
    $tipo_mensagem = "danger";
}

// Buscar PAs sem responsável
try {
    if (function_exists('buscarPAsSemResponsavel')) {
        $pas_sem_responsavel = buscarPAsSemResponsavel();
    } else {
        $pas_sem_responsavel = [];
    }
} catch (Exception $e) {
    $pas_sem_responsavel = [];
}

// Buscar usuários
$stmt = $pdo->query("SELECT id, nome_completo, email, username FROM usuarios ORDER BY nome_completo");
$usuarios = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Buscar pontos de atendimento
$stmt = $pdo->query("SELECT id, numero, nome FROM pontos_atendimento ORDER BY numero");
$pontos_atendimento = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Separar PAs com e sem responsável para o select
$pas_com_responsavel = [];
$pas_sem_responsavel_select = [];

foreach ($pontos_atendimento as $pa) {
    $tem_responsavel = false;
    foreach ($vinculos as $vinculo) {
        if ($vinculo['pa_id'] == $pa['id']) {
            $tem_responsavel = true;
            $pas_com_responsavel[] = [
                'pa' => $pa,
                'responsavel' => $vinculo['usuario_nome']
            ];
            break;
        }
    }

    if (!$tem_responsavel) {
        $pas_sem_responsavel_select[] = $pa;
    }
}

// Buscar estatísticas
$total_pas = $pdo->query("SELECT COUNT(*) FROM pontos_atendimento")->fetchColumn();
$total_com_responsavel = count($vinculos);
$total_sem_responsavel = count($pas_sem_responsavel);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="../assets/images/Sicoob.ico">
    <title>Gerenciar Vínculos - Análise de Crédito</title>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <style>
        :root {
            /* Cores do Sicoob */
            --sicoob-turquesa: #00AE9D;
            --sicoob-verde-escuro: #003641;
            --sicoob-branco: #FFFFFF;
            --sicoob-verde-claro: #C9D200;
            --sicoob-verde-medio: #7DB61C;
            --sicoob-roxo: #49479D;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .wrapper {
            display: flex;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 2rem;
            transition: all 0.3s ease;
        }

        .content-container {
            background: var(--sicoob-branco);
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 8px rgba(0, 54, 65, 0.1);
            border-top: 4px solid var(--sicoob-turquesa);
        }

        .content-title {
            color: var(--sicoob-verde-escuro);
            margin-bottom: 2rem;
            font-weight: 600;
            position: relative;
            padding-bottom: 1rem;
        }

        .content-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--sicoob-turquesa);
        }

        .btn-primary {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
        }

        .btn-primary:hover {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
        }

        .btn-danger {
            background-color: var(--sicoob-verde-claro);
            border-color: var(--sicoob-verde-claro);
        }

        .btn-danger:hover {
            background-color: #b3bc00;
            border-color: #b3bc00;
        }

        .table {
            margin-top: 1rem;
        }

        .table thead th {
            background-color: var(--sicoob-verde-escuro);
            color: var(--sicoob-branco);
            border: none;
        }

        .table tbody tr:hover {
            background-color: rgba(0, 174, 157, 0.05);
        }

        .select2-container--default .select2-selection--single {
            height: 38px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }

        .select2-container--default .select2-selection--single:focus {
            border-color: var(--sicoob-turquesa);
            box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.25);
        }

        .alert {
            border-radius: 4px;
            border: none;
        }

        .alert-success {
            background-color: rgba(125, 182, 28, 0.1);
            color: var(--sicoob-verde-medio);
        }

        .alert-warning {
            background-color: rgba(201, 210, 0, 0.1);
            color: var(--sicoob-verde-claro);
        }

        .alert-danger {
            background-color: rgba(73, 71, 157, 0.1);
            color: var(--sicoob-roxo);
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'components/sidebar.php'; ?>

        <div class="main-content">
            <div class="content-container">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="content-title mb-0">Gerenciar Vínculos de Usuários</h2>
                    <div class="btn-group">
                        <a href="historico_vinculos.php" class="btn btn-outline-info">
                            <i class="fas fa-history me-2"></i>Ver Histórico Completo
                        </a>
                        <!-- Botões de diagnóstico ocultos -->
                        <!--
                        <a href="diagnostico_rapido.php" class="btn btn-outline-warning">
                            <i class="fas fa-stethoscope me-2"></i>Diagnóstico
                        </a>
                        <a href="adicionar_historico_estrutura_antiga.php" class="btn btn-outline-success">
                            <i class="fas fa-history me-2"></i>Adicionar Histórico
                        </a>
                        -->
                    </div>
                </div>

                <?php if (isset($mensagem)): ?>
                    <div class="alert alert-<?php echo $tipo_mensagem; ?> alert-dismissible fade show" role="alert">
                        <i class="fas <?php echo $tipo_mensagem === 'success' ? 'fa-check-circle' :
                                            ($tipo_mensagem === 'warning' ? 'fa-exclamation-triangle' : 'fa-exclamation-circle'); ?> me-2"></i>
                        <?php echo $mensagem; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Alerta para problemas de constraint ou coluna -->
                <?php if (strpos($mensagem ?? '', 'SQLSTATE[23000]') !== false ||
                         strpos($mensagem ?? '', 'constraint') !== false ||
                         strpos($mensagem ?? '', 'Column not found') !== false ||
                         strpos($mensagem ?? '', 'usuario_id') !== false): ?>
                    <div class="alert alert-danger">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>Problema de Estrutura Detectado
                        </h6>
                        <p>Há um problema com a estrutura da tabela de vínculos ou funções incompatíveis.</p>
                        <p>
                            <a href="adicionar_historico_estrutura_antiga.php" class="btn btn-success btn-sm">
                                <i class="fas fa-history me-2"></i>Adicionar Sistema de Histórico
                            </a>
                            <a href="aplicar_correcao_estrutura_antiga.php" class="btn btn-warning btn-sm">
                                <i class="fas fa-tools me-2"></i>Corrigir Funções
                            </a>
                            <a href="verificar_estrutura_atual.php" class="btn btn-info btn-sm">
                                <i class="fas fa-search me-2"></i>Verificar Estrutura
                            </a>
                        </p>
                    </div>
                <?php endif; ?>

                <!-- Estatísticas de Responsabilidade -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h5 class="card-title text-primary">
                                    <i class="fas fa-building me-2"></i><?php echo $total_pas; ?>
                                </h5>
                                <p class="card-text">Total de PAs</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h5 class="card-title text-success">
                                    <i class="fas fa-user-check me-2"></i><?php echo $total_com_responsavel; ?>
                                </h5>
                                <p class="card-text">Com Responsável</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <h5 class="card-title text-warning">
                                    <i class="fas fa-user-times me-2"></i><?php echo $total_sem_responsavel; ?>
                                </h5>
                                <p class="card-text">Sem Responsável</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h5 class="card-title text-info">
                                    <i class="fas fa-percentage me-2"></i><?php echo $total_pas > 0 ? round(($total_com_responsavel / $total_pas) * 100, 1) : 0; ?>%
                                </h5>
                                <p class="card-text">Cobertura</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Aviso sobre Responsabilidade Única -->
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="fas fa-info-circle me-2"></i>Regra de Responsabilidade Única
                    </h6>
                    <p class="mb-0">
                        <strong>Importante:</strong> Cada Ponto de Atendimento pode ter apenas <strong>UM responsável ativo</strong> por vez.
                        Ao criar um novo vínculo, o responsável anterior será automaticamente desativado.
                    </p>
                </div>

                <?php if ($total_sem_responsavel > 0): ?>
                    <div class="alert alert-warning">
                        <h6 class="alert-heading">
                            <i class="fas fa-exclamation-triangle me-2"></i>PAs sem Responsável
                        </h6>
                        <p>Existem <strong><?php echo $total_sem_responsavel; ?> PAs sem responsável ativo</strong>:</p>
                        <div class="row">
                            <?php foreach ($pas_sem_responsavel as $pa): ?>
                                <div class="col-md-4 mb-1">
                                    <small class="text-muted">
                                        <i class="fas fa-building me-1"></i>
                                        <?php echo htmlspecialchars($pa['numero'] . ' - ' . $pa['nome']); ?>
                                    </small>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Formulário de Vínculo -->
                <div class="card mb-4">
                    <div class="card-body">
                        <h5 class="card-title mb-3">
                            <i class="fas fa-link me-2"></i>Novo Vínculo com Histórico
                        </h5>
                        <form method="POST" class="row g-3">
                            <div class="col-md-4">
                                <label for="usuario_id" class="form-label">Usuário</label>
                                <select class="form-select" id="usuario_id" name="usuario_id" required>
                                    <option value="">Selecione um usuário...</option>
                                    <?php foreach ($usuarios as $usuario): ?>
                                        <option value="<?php echo $usuario['id']; ?>">
                                            <?php echo htmlspecialchars($usuario['nome_completo']); ?>
                                            (<?php echo htmlspecialchars($usuario['username']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="pa_id" class="form-label">
                                    Ponto de Atendimento
                                    <small class="text-muted">(apenas PAs sem responsável)</small>
                                </label>
                                <select class="form-select" id="pa_id" name="pa_id" required>
                                    <option value="">Selecione um PA sem responsável...</option>
                                    <?php if (empty($pas_sem_responsavel_select)): ?>
                                        <option value="" disabled>Todos os PAs já têm responsável</option>
                                    <?php else: ?>
                                        <?php foreach ($pas_sem_responsavel_select as $pa): ?>
                                            <option value="<?php echo $pa['id']; ?>">
                                                <?php echo htmlspecialchars($pa['numero']); ?> -
                                                <?php echo htmlspecialchars($pa['nome']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </select>

                                <?php if (!empty($pas_com_responsavel)): ?>
                                    <div class="mt-2">
                                        <button type="button" class="btn btn-outline-info btn-sm"
                                                data-bs-toggle="collapse" data-bs-target="#pasComResponsavel">
                                            <i class="fas fa-eye me-1"></i>Ver PAs com Responsável (<?php echo count($pas_com_responsavel); ?>)
                                        </button>
                                    </div>

                                    <div class="collapse mt-2" id="pasComResponsavel">
                                        <div class="card card-body bg-light">
                                            <h6 class="card-title mb-2">
                                                <i class="fas fa-users me-1"></i>PAs que já têm responsável:
                                            </h6>
                                            <div class="row">
                                                <?php foreach ($pas_com_responsavel as $item): ?>
                                                    <div class="col-md-6 mb-1">
                                                        <small class="text-muted">
                                                            <strong><?php echo htmlspecialchars($item['pa']['numero']); ?></strong> -
                                                            <?php echo htmlspecialchars($item['pa']['nome']); ?>
                                                            <br>
                                                            <i class="fas fa-user me-1"></i>
                                                            <?php echo htmlspecialchars($item['responsavel']); ?>
                                                        </small>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                            <small class="text-info mt-2">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Para alterar o responsável de um PA, desative o vínculo atual primeiro.
                                            </small>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-2">
                                <label for="data_inicio" class="form-label">Data de Início</label>
                                <input type="date" class="form-control" id="data_inicio" name="data_inicio"
                                       value="<?php echo date('Y-m-d'); ?>" required>
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" name="action" value="vincular" class="btn btn-primary w-100">
                                    <i class="fas fa-link me-2"></i>Vincular
                                </button>
                            </div>

                            <!-- Opção Avançada para Substituir Responsável -->
                            <div class="col-md-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="modo_avancado" onchange="toggleModoAvancado()">
                                    <label class="form-check-label" for="modo_avancado">
                                        <small class="text-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            <strong>Modo Avançado:</strong> Permitir substituição de responsável (mostrar todos os PAs)
                                        </small>
                                    </label>
                                </div>

                                <!-- Aviso do Modo Avançado -->
                                <div id="aviso_modo_avancado" class="alert alert-warning mt-2" style="display: none;">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <div>
                                            <strong>Modo Avançado Ativo!</strong><br>
                                            <small>
                                                • PAs em <span style="color: #dc3545; font-weight: bold;">vermelho</span> já têm responsável<br>
                                                • PAs em <span style="color: #28a745; font-weight: bold;">verde</span> estão livres<br>
                                                • Ao selecionar um PA com responsável, o atual será substituído automaticamente
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <label for="observacoes" class="form-label">Observações (opcional)</label>
                                <textarea class="form-control" id="observacoes" name="observacoes" rows="2"
                                          placeholder="Motivo do vínculo, observações adicionais..."></textarea>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Lista de Vínculos -->
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title mb-3">
                            <i class="fas fa-users me-2"></i>Vínculos Ativos
                            <small class="text-muted">(<?php echo count($vinculos); ?> vínculos)</small>
                        </h5>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Usuário</th>
                                        <th>Ponto de Atendimento</th>
                                        <th>Data de Início</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (empty($vinculos)): ?>
                                        <tr>
                                            <td colspan="4" class="text-center text-muted py-4">
                                                <i class="fas fa-info-circle me-2"></i>
                                                Nenhum vínculo ativo encontrado
                                            </td>
                                        </tr>
                                    <?php else: ?>
                                        <?php foreach ($vinculos as $vinculo): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($vinculo['usuario_nome']); ?></strong><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($vinculo['usuario_email']); ?></small>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($vinculo['pa_numero']); ?></strong> -
                                                    <?php echo htmlspecialchars($vinculo['pa_nome']); ?>
                                                </td>
                                                <td>
                                                    <?php echo date('d/m/Y', strtotime($vinculo['data_inicio'])); ?>
                                                    <br><small class="text-muted">
                                                        <?php
                                                        $dias = (time() - strtotime($vinculo['data_inicio'])) / (60 * 60 * 24);
                                                        echo floor($dias) . ' dias';
                                                        ?>
                                                    </small>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <!-- Botão de Histórico -->
                                                        <button type="button" class="btn btn-outline-info btn-sm"
                                                                onclick="verHistorico(<?php echo $vinculo['usuario_id']; ?>, <?php echo $vinculo['pa_id']; ?>)"
                                                                title="Ver Histórico">
                                                            <i class="fas fa-history"></i>
                                                        </button>

                                                        <!-- Botão de Desativar -->
                                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                                onclick="desativarVinculo(<?php echo $vinculo['id']; ?>, '<?php echo htmlspecialchars($vinculo['usuario_nome']); ?>', '<?php echo htmlspecialchars($vinculo['pa_nome']); ?>')"
                                                                title="Desativar Vínculo">
                                                            <i class="fas fa-unlink"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Modal para Desativar Vínculo -->
                <div class="modal fade" id="modalDesativar" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-unlink me-2"></i>Desativar Vínculo
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <form method="POST">
                                <div class="modal-body">
                                    <input type="hidden" name="vinculo_id" id="vinculo_id_desativar">

                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                        <strong>Atenção:</strong> O vínculo será desativado mas mantido no histórico.
                                    </div>

                                    <p>Desativar vínculo de <strong id="usuario_nome_desativar"></strong>
                                       com <strong id="pa_nome_desativar"></strong>?</p>

                                    <div class="mb-3">
                                        <label for="data_fim" class="form-label">Data de Fim</label>
                                        <input type="date" class="form-control" name="data_fim"
                                               value="<?php echo date('Y-m-d'); ?>" required>
                                    </div>

                                    <div class="mb-3">
                                        <label for="observacoes_desativacao" class="form-label">Motivo da Desativação</label>
                                        <textarea class="form-control" name="observacoes_desativacao" rows="3"
                                                  placeholder="Descreva o motivo da desativação..." required></textarea>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                                    <button type="submit" name="action" value="desvincular" class="btn btn-danger">
                                        <i class="fas fa-unlink me-2"></i>Desativar Vínculo
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Modal de Histórico -->
                <div class="modal fade" id="modalHistorico" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-history me-2"></i>Histórico de Vínculos
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div id="historico-content">
                                    <div class="text-center">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Carregando...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // Inicializar Select2 para os selects
            $('#usuario_id, #pa_id').select2({
                theme: 'bootstrap-5',
                width: '100%',
                placeholder: 'Selecione...',
                allowClear: true
            });
        });

        // Função para desativar vínculo
        function desativarVinculo(vinculoId, usuarioNome, paNome) {
            $('#vinculo_id_desativar').val(vinculoId);
            $('#usuario_nome_desativar').text(usuarioNome);
            $('#pa_nome_desativar').text(paNome);

            var modal = new bootstrap.Modal(document.getElementById('modalDesativar'));
            modal.show();
        }

        // Função para ver histórico
        function verHistorico(usuarioId, paId) {
            // Mostrar modal de histórico
            const modal = new bootstrap.Modal(document.getElementById('modalHistorico'));
            modal.show();

            // Carregar histórico via AJAX
            const historicoContent = document.getElementById('historico-content');
            historicoContent.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Carregando histórico...</span>
                    </div>
                    <p class="mt-2">Carregando histórico de vínculos...</p>
                </div>
            `;

            fetch('ajax_historico_vinculos.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `usuario_id=${usuarioId}&pa_id=${paId}`
            })
            .then(response => response.text())
            .then(data => {
                historicoContent.innerHTML = data;
            })
            .catch(error => {
                console.error('Erro:', error);
                historicoContent.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erro ao carregar histórico. Tente novamente.
                    </div>
                `;
            });
        }

        // Validação do formulário
        document.querySelector('form').addEventListener('submit', function(e) {
            const usuarioId = document.getElementById('usuario_id').value;
            const paId = document.getElementById('pa_id').value;
            const dataInicio = document.getElementById('data_inicio').value;

            if (!usuarioId || !paId || !dataInicio) {
                e.preventDefault();
                alert('Por favor, preencha todos os campos obrigatórios.');
                return false;
            }

            // Verificar se a data não é futura
            const hoje = new Date().toISOString().split('T')[0];
            if (dataInicio > hoje) {
                e.preventDefault();
                alert('A data de início não pode ser futura.');
                return false;
            }

            // Aviso sobre responsabilidade única
            const paSelect = document.getElementById('pa_id');
            const paTexto = paSelect.options[paSelect.selectedIndex].text;

            const confirmacao = confirm(
                'ATENÇÃO: Responsabilidade Única\n\n' +
                'Cada PA pode ter apenas UM responsável ativo.\n' +
                'Se o PA "' + paTexto + '" já tiver um responsável, ' +
                'ele será automaticamente desativado.\n\n' +
                'Deseja continuar?'
            );

            if (!confirmacao) {
                e.preventDefault();
                return false;
            }
        });

        // Verificar responsável atual ao selecionar PA
        document.getElementById('pa_id').addEventListener('change', function() {
            const paId = this.value;
            if (paId) {
                verificarResponsavelAtual(paId);
            } else {
                limparAvisoResponsavel();
            }
        });

        // Função para alternar modo avançado
        function toggleModoAvancado() {
            const modoAvancado = document.getElementById('modo_avancado').checked;
            const paSelect = document.getElementById('pa_id');
            const paLabel = paSelect.previousElementSibling;
            const avisoModoAvancado = document.getElementById('aviso_modo_avancado');

            // Mostrar/esconder aviso
            avisoModoAvancado.style.display = modoAvancado ? 'block' : 'none';

            // Limpar select atual
            paSelect.innerHTML = '';

            if (modoAvancado) {
                // Modo avançado: mostrar todos os PAs
                paLabel.innerHTML = 'Ponto de Atendimento <small class="text-warning">(todos os PAs - substituição permitida)</small>';
                paSelect.innerHTML = '<option value="">Selecione um PA...</option>';

                <?php foreach ($pontos_atendimento as $pa): ?>
                    const option<?php echo $pa['id']; ?> = document.createElement('option');
                    option<?php echo $pa['id']; ?>.value = '<?php echo $pa['id']; ?>';
                    option<?php echo $pa['id']; ?>.textContent = '<?php echo htmlspecialchars($pa['numero'] . ' - ' . $pa['nome']); ?>';

                    // Verificar se tem responsável
                    <?php
                    $tem_responsavel = false;
                    $nome_responsavel = '';
                    foreach ($vinculos as $vinculo) {
                        if ($vinculo['pa_id'] == $pa['id']) {
                            $tem_responsavel = true;
                            $nome_responsavel = $vinculo['usuario_nome'];
                            break;
                        }
                    }
                    ?>

                    <?php if ($tem_responsavel): ?>
                        option<?php echo $pa['id']; ?>.textContent += ' (Atual: <?php echo htmlspecialchars($nome_responsavel); ?>)';
                        option<?php echo $pa['id']; ?>.style.color = '#dc3545';
                        option<?php echo $pa['id']; ?>.style.fontWeight = 'bold';
                    <?php else: ?>
                        option<?php echo $pa['id']; ?>.style.color = '#28a745';
                    <?php endif; ?>

                    paSelect.appendChild(option<?php echo $pa['id']; ?>);
                <?php endforeach; ?>

            } else {
                // Modo normal: apenas PAs sem responsável
                paLabel.innerHTML = 'Ponto de Atendimento <small class="text-muted">(apenas PAs sem responsável)</small>';

                <?php if (empty($pas_sem_responsavel_select)): ?>
                    paSelect.innerHTML = '<option value="" disabled>Todos os PAs já têm responsável</option>';
                <?php else: ?>
                    paSelect.innerHTML = '<option value="">Selecione um PA sem responsável...</option>';
                    <?php foreach ($pas_sem_responsavel_select as $pa): ?>
                        const optionSem<?php echo $pa['id']; ?> = document.createElement('option');
                        optionSem<?php echo $pa['id']; ?>.value = '<?php echo $pa['id']; ?>';
                        optionSem<?php echo $pa['id']; ?>.textContent = '<?php echo htmlspecialchars($pa['numero'] . ' - ' . $pa['nome']); ?>';
                        paSelect.appendChild(optionSem<?php echo $pa['id']; ?>);
                    <?php endforeach; ?>
                <?php endif; ?>
            }

            // Reinicializar Select2
            $('#pa_id').select2('destroy').select2({
                theme: 'bootstrap-5',
                width: '100%',
                placeholder: 'Selecione...',
                allowClear: true
            });
        }

        // Função para verificar responsável atual (via AJAX)
        function verificarResponsavelAtual(paId) {
            const dataInicio = document.getElementById('data_inicio').value || new Date().toISOString().split('T')[0];

            // Mostrar loading
            mostrarAvisoResponsavel('Verificando responsável atual...', 'info');

            fetch('ajax_verificar_responsavel.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `pa_id=${paId}&data=${dataInicio}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.erro) {
                    mostrarAvisoResponsavel('Erro: ' + data.erro, 'danger');
                } else if (data.tem_responsavel) {
                    const responsavel = data.responsavel;
                    const pa = data.pa;
                    const dataFormatada = new Date(responsavel.data_inicio).toLocaleDateString('pt-BR');

                    mostrarAvisoResponsavel(
                        `⚠️ PA ${pa.numero} - ${pa.nome} já tem responsável ativo: ` +
                        `<strong>${responsavel.nome}</strong> (desde ${dataFormatada}). ` +
                        `Este responsável será automaticamente desativado se você continuar.`,
                        'warning'
                    );
                } else {
                    const pa = data.pa;
                    mostrarAvisoResponsavel(
                        `✅ PA ${pa.numero} - ${pa.nome} está disponível (sem responsável ativo).`,
                        'success'
                    );
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                mostrarAvisoResponsavel('Erro ao verificar responsável atual.', 'danger');
            });
        }

        // Função para mostrar aviso sobre responsável
        function mostrarAvisoResponsavel(mensagem, tipo) {
            let avisoDiv = document.getElementById('aviso-responsavel');
            if (!avisoDiv) {
                avisoDiv = document.createElement('div');
                avisoDiv.id = 'aviso-responsavel';
                avisoDiv.className = 'mt-2';
                document.getElementById('pa_id').parentNode.appendChild(avisoDiv);
            }

            avisoDiv.innerHTML = `
                <div class="alert alert-${tipo} alert-sm mb-0">
                    <small>${mensagem}</small>
                </div>
            `;
        }

        // Função para limpar aviso sobre responsável
        function limparAvisoResponsavel() {
            const avisoDiv = document.getElementById('aviso-responsavel');
            if (avisoDiv) {
                avisoDiv.innerHTML = '';
            }
        }
    </script>
</body>
</html> 