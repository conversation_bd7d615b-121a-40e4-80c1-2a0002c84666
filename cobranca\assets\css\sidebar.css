@import url('https://use.fontawesome.com/releases/v6.5.1/css/all.css');

:root {
    --sicoob-turquesa: #00AE9D;
    --sicoob-verde-escuro: #003641;
    --sicoob-branco: #FFFFFF;
    --sicoob-verde-claro: #C9D200;
    --sicoob-verde-medio: #7DB61C;
    --sicoob-roxo: #49479D;
    --sicoob-cinza: #58595B;
    --sicoob-cinza-claro: #F5F5F5;
    --sidebar-width: 220px;
    --sidebar-collapsed-width: 60px;
    --sidebar-font: 'Se<PERSON><PERSON> UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
}

/* Garantir consistência de fontes */
.sidebar, 
.sidebar *,
.sidebar-title,
.sidebar-link,
.sidebar-link span,
.sidebar-link i {
    font-family: var(--sidebar-font) !important;
}

/* Ajustes específicos para ícones */
.sidebar i[class^="fas"] {
    display: inline-block !important;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
    -moz-osx-font-smoothing: grayscale !important;
    -webkit-font-smoothing: antialiased !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-rendering: auto !important;
    line-height: 1 !important;
}

/* Layout principal */
.wrapper {
    display: flex;
    min-height: 100vh;
    background-color: var(--sicoob-cinza-claro);
}

.content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: 20px;
    transition: margin-left 0.3s ease;
}

.content.sidebar-collapsed {
    margin-left: var(--sidebar-collapsed-width);
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background-color: var(--sicoob-verde-escuro);
    color: var(--sicoob-branco);
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    overflow-x: hidden;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

/* Header da Sidebar */
.sidebar-header {
    padding: 15px 10px 15px 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 65px;
    gap: 5px;
    position: relative;
}

.sidebar-logo-container {
    display: flex;
    align-items: center;
    min-width: 32px;
    max-width: 180px;
    overflow: hidden;
    flex: 1;
    transition: all 0.3s ease;
    padding-left: 8px;
    padding-right: 5px;
} 

.sidebar-logo {
    height: 28px;
    width: 28px;
    transition: all 0.3s ease;
    margin-right: 6px;
    flex-shrink: 0;
    object-fit: contain;
}

.sidebar-title {
    color: var(--sicoob-branco) !important;
    font-size: 0.7rem !important;
    font-weight: 600 !important;
    margin: 0 !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 165px !important;
    opacity: 1 !important;
    transition: all 0.3s ease !important;
    line-height: 1.1 !important;
    text-transform: uppercase !important;
    letter-spacing: -0.4px !important;
}

.sidebar.collapsed .sidebar-logo-container {
    max-width: var(--sidebar-collapsed-width);
    min-width: var(--sidebar-collapsed-width);
    justify-content: flex-start;
    padding-left: 8px;
    padding-right: 10px;
}

.sidebar.collapsed .sidebar-logo {
    margin: 0;
    width: 28px;
    height: 28px;
}

.sidebar.collapsed .sidebar-title {
    opacity: 0;
    width: 0;
    margin: 0;
    padding: 0;
}

.sidebar-toggle {
    background: transparent;
    border: none;
    color: var(--sicoob-branco);
    cursor: pointer;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    transition: all 0.3s ease;
    flex-shrink: 0;
    position: absolute;
    right: -10px;
    bottom: -12px;
    z-index: 1000;
    background-color: var(--sicoob-verde-escuro);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar.collapsed .sidebar-toggle {
    right: -12px;
    bottom: -12px;
}

.sidebar-toggle:hover {
    background-color: var(--sicoob-turquesa);
}

.sidebar-toggle i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
    display: inline-block !important;
}

.sidebar.collapsed .sidebar-toggle i {
    transform: rotate(180deg);
}

.sidebar.collapsed .sidebar-header {
    padding: 15px 12px;
    position: relative;
}

/* Navegação */
.sidebar-nav {
    flex: 1;
    padding: 20px 0 15px 0;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.sidebar-link {
    color: var(--sicoob-branco) !important;
    padding: 10px 15px !important;
    display: flex !important;
    align-items: center !important;
    text-decoration: none !important;
    border-radius: 0 !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    margin-bottom: 4px !important;
    font-weight: 500 !important;
}

.sidebar-link i {
    width: 25px !important;
    font-size: 1rem !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
}

.sidebar-link span {
    margin-left: 8px !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    opacity: 1 !important;
}

.sidebar.collapsed .sidebar-link span {
    opacity: 0 !important;
    width: 0 !important;
    margin-left: 0 !important;
}

.sidebar-link:hover, .sidebar-link.active {
    background-color: rgba(0, 174, 157, 0.2) !important;
    color: var(--sicoob-verde-claro) !important;
    text-decoration: none !important;
}

.sidebar-link.active::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 4px;
    background-color: var(--sicoob-verde-claro);
}

/* Footer da Sidebar */
.sidebar-footer {
    padding: 15px 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Scrollbar personalizada */
.sidebar-nav::-webkit-scrollbar {
    width: 5px;
}

.sidebar-nav::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Responsividade */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        z-index: 1030;
    }
    
    .sidebar.visible {
        transform: translateX(0);
    }
    
    .content {
        margin-left: 0 !important;
    }
    
    .mobile-toggle {
        display: block;
        position: fixed;
        top: 15px;
        left: 15px;
        z-index: 1020;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--sicoob-verde-escuro);
        color: var(--sicoob-branco);
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
    
    .mobile-toggle:hover {
        background-color: var(--sicoob-turquesa);
    }
    
    .sidebar-backdrop {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1025;
        display: none;
    }
    
    .sidebar-backdrop.visible {
        display: block;
    }
}

/* Cards e Elementos UI */
.card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.card-header {
    background-color: var(--sicoob-branco);
    border-bottom: 2px solid var(--sicoob-verde-escuro);
    padding: 15px 20px;
}

.card-header h5 {
    color: var(--sicoob-verde-escuro);
    margin: 0;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

/* Botões */
.btn-primary {
    background-color: var(--sicoob-verde-escuro);
    border-color: var(--sicoob-verde-escuro);
}

.btn-primary:hover {
    background-color: var(--sicoob-verde-medio);
    border-color: var(--sicoob-verde-medio);
}

.btn-outline-light {
    color: var(--sicoob-branco);
    border-color: var(--sicoob-branco);
}

.btn-outline-light:hover {
    background-color: var(--sicoob-branco);
    color: var(--sicoob-turquesa);
}

/* Formulários */
.form-control, .form-select {
    border-radius: 6px;
    border: 1px solid #ddd;
    padding: 10px 12px;
}

.form-control:focus, .form-select:focus {
    border-color: var(--sicoob-verde-escuro);
    box-shadow: 0 0 0 0.2rem rgba(0, 54, 65, 0.25);
}

.form-label {
    color: var(--sicoob-cinza);
    font-weight: 500;
    margin-bottom: 8px;
}

/* Tabelas */
.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: var(--sicoob-verde-escuro);
    color: var(--sicoob-branco);
    font-weight: 600;
    border-bottom: 2px solid var(--sicoob-verde-escuro);
}

.table tbody tr:hover {
    background-color: rgba(0, 174, 157, 0.05);
}

/* DataTables */
.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background: var(--sicoob-turquesa) !important;
    color: var(--sicoob-branco) !important;
    border: 1px solid var(--sicoob-turquesa) !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: var(--sicoob-verde-medio) !important;
    color: var(--sicoob-branco) !important;
    border: 1px solid var(--sicoob-verde-medio) !important;
}

/* Timeline */
.timeline::before {
    background: var(--sicoob-turquesa);
}

.timeline-marker {
    background: var(--sicoob-turquesa);
    border: 2px solid var(--sicoob-branco);
} 