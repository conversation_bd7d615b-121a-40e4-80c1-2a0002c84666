:root {
    --sicoob-verde: #003641;
    --sicoob-turquesa: #00AE9D;
    --sicoob-verde-claro: #e8f5e9;
    --sicoob-cinza: #58595B;
    --sicoob-branco: #FFFFFF;
    --sicoob-turquesa-claro: #e0f7fa;
    --sicoob-amarelo-claro: #fff8e1;
    --sicoob-amarelo: #ffc107;
    --sicoob-verde-escuro: #002B33;
}

body {
    font-family: 'Open Sans', Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    color: var(--sicoob-verde);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    background-color: var(--sicoob-branco);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 15px 0;
}

.header .logo {
    height: 40px;
}

.card {
    background: var(--sicoob-branco);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 20px;
}

.btn {
    background-color: var(--sicoob-turquesa);
    color: var(--sicoob-branco);
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: var(--sicoob-verde);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--sicoob-verde);
    font-weight: 600;
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-sizing: border-box;
}

.error {
    color: #dc3545;
    padding: 10px;
    margin-bottom: 20px;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #dc3545;
}

.success {
    color: #28a745;
    padding: 10px;
    margin-bottom: 20px;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #28a745;
}

.card-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    transition: all 0.3s ease;
    position: relative;
    text-decoration: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-btn i {
    font-size: 18px;
    transition: all 0.3s ease;
}

.card-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.card-btn:hover i {
    transform: scale(1.1);
}

/* Tooltip */
.card-btn[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    margin-bottom: 8px;
}

/* Badge de notificação */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: linear-gradient(135deg, #ff4b4b, #dc3545);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.btn-dashboard {
    background-color: var(--sicoob-verde-escuro);
}

.btn-form {
    background-color: var(--sicoob-turquesa);
}

.btn-metas {
    background-color: var(--sicoob-verde-claro);
}

.navbar-dashboard {
    background: linear-gradient(135deg, #00AE9D 0%, #003641 100%) !important;
    box-shadow: 0 4px 20px rgba(0, 54, 65, 0.2);
    border-bottom: 3px solid #7DB61C;
} 