-- Script para adicionar campo de data específica para edição retroativa
-- Este campo permitirá que gestores definam uma data específica para edição

USE sicoob_access_control;

-- Adicionar campo para data específica de edição retroativa
ALTER TABLE lcx_livros_caixa 
ADD COLUMN data_edicao_especifica DATE NULL 
COMMENT 'Data específica habilitada para edição retroativa de movimentações';

-- Adicionar campo para usuário que habilitou a data específica
ALTER TABLE lcx_livros_caixa 
ADD COLUMN habilitado_por INT NULL 
COMMENT 'Usuário que habilitou a data específica para edição';

-- Adicionar campo para data/hora da habilitação
ALTER TABLE lcx_livros_caixa 
ADD COLUMN data_habilitacao DATETIME NULL 
COMMENT 'Data e hora em que a edição específica foi habilitada';

-- Adicionar foreign key para o usuário que habilitou
ALTER TABLE lcx_livros_caixa 
ADD CONSTRAINT fk_livros_habilitado_por 
FOREIGN KEY (habilitado_por) REFERENCES usuarios(id);

-- Criar índice para melhor performance nas consultas
CREATE INDEX idx_lcx_livros_data_edicao ON lcx_livros_caixa(data_edicao_especifica);

SELECT 'Campos adicionados com sucesso!' as resultado;
