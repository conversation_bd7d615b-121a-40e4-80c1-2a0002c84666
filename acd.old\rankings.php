<?php
session_start();
require_once '../config/database.php';
require_once 'check_acd_permission.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se o usuário tem permissão para acessar a pasta ACD
if (!checkACDPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Verificar se o usuário é administrador ou gestor
if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

try {
    // RANKING DE PAs - PROPOSTAS (incluindo PAs com zero propostas)
    $stmt = $pdo->query("
        SELECT 
            pa.nome as pa_nome,
            COALESCE(COUNT(f.id), 0) as total_propostas,
            COALESCE(SUM(CASE WHEN f.acao = 'submeter' THEN 1 ELSE 0 END), 0) as aprovadas,
            COALESCE(SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END), 0) as devolvidas,
            CASE 
                WHEN COUNT(f.id) > 0 THEN ROUND((SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(f.id)) * 100, 1)
                ELSE 0
            END as taxa_devolucao
        FROM pontos_atendimento pa
        LEFT JOIN acd_formularios f ON pa.nome COLLATE utf8mb4_general_ci = f.pa COLLATE utf8mb4_general_ci
            AND YEARWEEK(f.data_criacao, 1) = YEARWEEK(CURDATE(), 1)
            AND f.pa != '0' AND f.pa IS NOT NULL
        WHERE pa.nome != '0' AND pa.nome IS NOT NULL AND pa.nome != 'UAD'
        GROUP BY pa.nome
        ORDER BY total_propostas DESC, taxa_devolucao ASC
    ");
    $ranking_propostas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // RANKING DE PAs - MENOR DEVOLUÇÃO (incluindo PAs com zero propostas)
    $stmt = $pdo->query("
        SELECT 
            pa.nome as pa_nome,
            COALESCE(COUNT(f.id), 0) as total_propostas,
            COALESCE(SUM(CASE WHEN f.acao = 'submeter' THEN 1 ELSE 0 END), 0) as aprovadas,
            COALESCE(SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END), 0) as devolvidas,
            CASE 
                WHEN COUNT(f.id) > 0 THEN ROUND((SUM(CASE WHEN f.acao = 'devolver' THEN 1 ELSE 0 END) / COUNT(f.id)) * 100, 1)
                ELSE 0
            END as taxa_devolucao
        FROM pontos_atendimento pa
        LEFT JOIN acd_formularios f ON pa.nome COLLATE utf8mb4_general_ci = f.pa COLLATE utf8mb4_general_ci
            AND YEARWEEK(f.data_criacao, 1) = YEARWEEK(CURDATE(), 1)
            AND f.pa != '0' AND f.pa IS NOT NULL
        WHERE pa.nome != '0' AND pa.nome IS NOT NULL AND pa.nome != 'UAD'
        GROUP BY pa.nome
        ORDER BY taxa_devolucao ASC, total_propostas DESC
    ");
    $ranking_devolucao = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    error_log("Erro ao buscar rankings: " . $e->getMessage());
    $ranking_propostas = [];
    $ranking_devolucao = [];
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rankings de PAs - ACD</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #003641;
            --secondary-color: #00AE9D;
            --text-light: #666666;
            --text-dark: #333333;
            --bg-light: #f5f5f5;
            --bg-white: #ffffff;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: var(--bg-light);
            margin: 0;
            padding: 0;
        }

        .wrapper {
            display: flex;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
            padding: 1rem;
            margin-left: 250px;
            height: 100vh;
            overflow: hidden;
        }



        .rankings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
            margin: 0;
            height: calc(100vh - 40px);
            padding: 0.5rem;
        }

        .ranking-card {
            background: var(--bg-white);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid #e0e0e0;
        }

        .ranking-title {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 0.35rem;
            text-align: center;
            font-size: 0.75rem;
            font-weight: 600;
            border-radius: 6px 6px 0 0;
        }

        .ranking-content {
            padding: 0.4rem;
            height: calc(100vh - 120px);
            overflow: hidden;
        }

        .ranking-list {
            display: flex;
            flex-direction: column;
            gap: 0.15rem;
            height: 100%;
        }

        .ranking-item {
            display: flex;
            align-items: center;
            gap: 0.4rem;
            padding: 0.25rem 0.4rem;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 174, 157, 0.2);
            transition: all 0.2s ease;
            min-height: 26px;
            flex-shrink: 0;
        }

        .ranking-item:hover {
            background: rgba(0, 174, 157, 0.1);
            border-color: var(--secondary-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 174, 157, 0.2);
        }

        .ranking-position-1 {
            background: linear-gradient(135deg, rgba(255, 215, 0, 0.15), rgba(255, 215, 0, 0.05));
            border-color: #FFD700;
        }

        .ranking-position-2 {
            background: linear-gradient(135deg, rgba(192, 192, 192, 0.15), rgba(192, 192, 192, 0.05));
            border-color: #C0C0C0;
        }

        .ranking-position-3 {
            background: linear-gradient(135deg, rgba(205, 127, 50, 0.15), rgba(205, 127, 50, 0.05));
            border-color: #CD7F32;
        }

        .ranking-position-4 {
            background: linear-gradient(135deg, rgba(220, 220, 220, 0.15), rgba(240, 240, 240, 0.05));
            border-color: #E0E0E0;
        }

        .ranking-position {
            font-size: 0.7rem;
            font-weight: 700;
            color: var(--primary-color);
            min-width: 20px;
            text-align: center;
        }

        .ranking-info {
            flex: 1;
            min-width: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .ranking-name {
            font-size: 0.65rem;
            font-weight: 600;
            color: var(--text-dark);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            min-width: 70px;
            max-width: 100px;
        }

        .ranking-metrics {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            font-size: 0.6rem;
            color: var(--text-light);
            white-space: nowrap;
        }

        .metric-propostas {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 0.6rem;
        }

        .metric-devolucao {
            font-weight: 600;
            color: #e74c3c;
            font-size: 0.6rem;
        }

        .metric-zero {
            color: #95a5a6;
            font-style: italic;
            font-size: 0.55rem;
        }

        .metric-separator {
            color: var(--secondary-color);
            font-weight: 700;
            font-size: 0.5rem;
        }

        .no-ranking {
            text-align: center;
            color: var(--text-light);
            padding: 3rem;
            font-style: italic;
        }

        .no-ranking i {
            margin-right: 0.5rem;
        }

        /* Responsividade */
        @media (max-width: 1200px) {
            .main-content {
                margin-left: 0;
                padding: 0.25rem;
                height: 100vh;
            }

            .rankings-grid {
                grid-template-columns: 1fr;
                gap: 0.4rem;
                height: calc(100vh - 20px);
                padding: 0.25rem;
            }

            .ranking-content {
                height: calc(50vh - 40px);
            }
        }

        @media (max-width: 768px) {
            .ranking-content {
                height: calc(45vh - 35px);
                padding: 0.3rem;
            }

            .ranking-item {
                padding: 0.2rem 0.3rem;
                gap: 0.2rem;
                min-height: 22px;
            }

            .ranking-name {
                font-size: 0.6rem;
                min-width: 45px;
                max-width: 80px;
            }

            .ranking-metrics {
                font-size: 0.55rem;
                gap: 0.2rem;
            }

            .ranking-position {
                font-size: 0.6rem;
                min-width: 16px;
            }

            .ranking-title {
                font-size: 0.7rem;
                padding: 0.3rem;
            }

            .rankings-grid {
                gap: 0.3rem;
                padding: 0.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'components/sidebar.php'; ?>
        
        <div class="main-content">
            <div class="rankings-grid">
                <!-- Ranking por Propostas -->
                <div class="ranking-card">
                    <div class="ranking-title">📊 Ranking por Propostas</div>
                    <div class="ranking-content">
                        <?php if (!empty($ranking_propostas)): ?>
                            <div class="ranking-list">
                                <?php foreach ($ranking_propostas as $index => $pa): ?>
                                    <div class="ranking-item ranking-position-<?php echo ($index + 1) <= 3 ? ($index + 1) : '4'; ?>">
                                        <div class="ranking-position"><?php echo $index + 1; ?>º</div>
                                        <div class="ranking-info">
                                            <div class="ranking-name"><?php echo htmlspecialchars($pa['pa_nome']); ?></div>
                                            <div class="ranking-metrics">
                                                <?php if ($pa['total_propostas'] > 0): ?>
                                                    <span class="metric-propostas"><?php echo $pa['total_propostas']; ?> propostas</span>
                                                    <span class="metric-separator">•</span>
                                                    <span class="metric-devolucao"><?php echo $pa['taxa_devolucao']; ?>% devolução</span>
                                                <?php else: ?>
                                                    <span class="metric-zero">Nenhuma proposta analisada</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="no-ranking">
                                <i class="fas fa-info-circle"></i>
                                Nenhum PA encontrado
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Ranking por Menor Devolução -->
                <div class="ranking-card">
                    <div class="ranking-title">🏆 Ranking por Menor Devolução</div>
                    <div class="ranking-content">
                        <?php if (!empty($ranking_devolucao)): ?>
                            <div class="ranking-list">
                                <?php foreach ($ranking_devolucao as $index => $pa): ?>
                                    <div class="ranking-item ranking-position-<?php echo ($index + 1) <= 3 ? ($index + 1) : '4'; ?>">
                                        <div class="ranking-position"><?php echo $index + 1; ?>º</div>
                                        <div class="ranking-info">
                                            <div class="ranking-name"><?php echo htmlspecialchars($pa['pa_nome']); ?></div>
                                            <div class="ranking-metrics">
                                                <?php if ($pa['total_propostas'] > 0): ?>
                                                    <span class="metric-propostas"><?php echo $pa['total_propostas']; ?> propostas</span>
                                                    <span class="metric-separator">•</span>
                                                    <span class="metric-devolucao"><?php echo $pa['taxa_devolucao']; ?>% devolução</span>
                                                <?php else: ?>
                                                    <span class="metric-zero">Nenhuma proposta analisada</span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="no-ranking">
                                <i class="fas fa-info-circle"></i>
                                Nenhum PA encontrado
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
