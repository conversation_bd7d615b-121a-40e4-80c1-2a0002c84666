<?php
/**
 * Funções para estrutura antiga da tabela acd_usuario_pa
 * Onde usuario_id e pa_id referenciam dados do banco local
 */

/**
 * Buscar vínculos ativos - Estrutura Antiga
 */
function buscarVinculosAtivos() {
    global $pdo;
    
    try {
        $sql = "
            SELECT 
                v.id,
                v.usuario_id,
                v.pa_id,
                COALESCE(v.data_inicio, DATE(v.data_vinculo), CURDATE()) as data_inicio,
                u.nome_completo as usuario_nome,
                u.email as usuario_email,
                p.nome as pa_nome,
                p.numero as pa_numero
            FROM acd_usuario_pa v
            INNER JOIN usuarios u ON v.usuario_id = u.id
            INNER JOIN pontos_atendimento p ON v.pa_id = p.id
            WHERE (v.status = 'ativo' OR v.status = 1)
            ORDER BY p.nome, u.nome_completo
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Erro em buscarVinculosAtivos: " . $e->getMessage());
        return [];
    }
}

/**
 * Buscar PAs sem responsável - Estrutura Antiga
 */
function buscarPAsSemResponsavel() {
    global $pdo;
    
    try {
        $sql = "
            SELECT
                pa.id,
                pa.numero,
                pa.nome
            FROM pontos_atendimento pa
            LEFT JOIN acd_usuario_pa v ON (
                pa.id = v.pa_id
                AND (v.status = 'ativo' OR v.status = 1)
            )
            WHERE v.id IS NULL
            ORDER BY pa.numero
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("Erro em buscarPAsSemResponsavel: " . $e->getMessage());
        return [];
    }
}

/**
 * Criar vínculo - Estrutura Antiga
 */
function criarVinculo($usuario_id, $pa_id, $data_inicio = null, $criado_por = null, $observacoes = null) {
    global $pdo;
    
    try {
        $pdo->beginTransaction();
        
        // Verificar se o usuário existe na tabela local
        $stmt = $pdo->prepare("SELECT id, nome_completo FROM usuarios WHERE id = ?");
        $stmt->execute([$usuario_id]);
        $usuario = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$usuario) {
            $pdo->rollBack();
            return [
                'sucesso' => false,
                'erro' => 'Usuário não encontrado na tabela local (ID: ' . $usuario_id . ')'
            ];
        }
        
        // Verificar se o PA existe
        $stmt = $pdo->prepare("SELECT id, nome FROM pontos_atendimento WHERE id = ?");
        $stmt->execute([$pa_id]);
        $pa = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$pa) {
            $pdo->rollBack();
            return [
                'sucesso' => false,
                'erro' => 'Ponto de Atendimento não encontrado (ID: ' . $pa_id . ')'
            ];
        }
        
        // Verificar se já existe vínculo ativo para este PA
        $stmt = $pdo->prepare("
            SELECT v.id, u.nome_completo 
            FROM acd_usuario_pa v
            INNER JOIN usuarios u ON v.usuario_id = u.id
            WHERE v.pa_id = ? 
            AND (v.status = 'ativo' OR v.status = 1)
        ");
        $stmt->execute([$pa_id]);
        $vinculo_existente = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($vinculo_existente && $vinculo_existente['id'] != $usuario_id) {
            // Desativar vínculo anterior
            $stmt = $pdo->prepare("
                UPDATE acd_usuario_pa 
                SET status = 'inativo' 
                WHERE pa_id = ? 
                AND (status = 'ativo' OR status = 1)
            ");
            $stmt->execute([$pa_id]);
        }
        
        // Verificar estrutura da tabela para saber quais campos usar
        $stmt = $pdo->query("DESCRIBE acd_usuario_pa");
        $colunas = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $campos = array_column($colunas, 'Field');
        
        // Preparar SQL baseado na estrutura
        $campos_insert = ['usuario_id', 'pa_id'];
        $valores = [$usuario_id, $pa_id];
        $placeholders = ['?', '?'];
        
        if (in_array('data_inicio', $campos)) {
            $campos_insert[] = 'data_inicio';
            $valores[] = $data_inicio ?? date('Y-m-d');
            $placeholders[] = '?';
        } elseif (in_array('data_vinculo', $campos)) {
            $campos_insert[] = 'data_vinculo';
            $valores[] = date('Y-m-d H:i:s');
            $placeholders[] = '?';
        }
        
        if (in_array('status', $campos)) {
            $campos_insert[] = 'status';
            $valores[] = 'ativo';
            $placeholders[] = '?';
        }
        
        if (in_array('criado_por', $campos)) {
            $campos_insert[] = 'criado_por';
            $valores[] = $criado_por ?? $_SESSION['user_id'] ?? 1;
            $placeholders[] = '?';
        } elseif (in_array('usuario_vinculo', $campos)) {
            $campos_insert[] = 'usuario_vinculo';
            $valores[] = $criado_por ?? $_SESSION['user_id'] ?? 1;
            $placeholders[] = '?';
        }
        
        if (in_array('observacoes', $campos) && $observacoes) {
            $campos_insert[] = 'observacoes';
            $valores[] = $observacoes;
            $placeholders[] = '?';
        }
        
        $sql = "INSERT INTO acd_usuario_pa (" . implode(', ', $campos_insert) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($valores);
        
        $novo_vinculo_id = $pdo->lastInsertId();
        
        $pdo->commit();
        
        return [
            'sucesso' => true,
            'vinculo_id' => $novo_vinculo_id,
            'mensagem' => "Vínculo criado com sucesso: {$usuario['nome_completo']} → {$pa['nome']}"
        ];
        
    } catch (Exception $e) {
        $pdo->rollBack();
        return [
            'sucesso' => false,
            'erro' => $e->getMessage()
        ];
    }
}

/**
 * Desativar vínculo - Estrutura Antiga
 */
function desativarVinculo($vinculo_id, $data_fim = null, $desativado_por = null, $observacoes = null) {
    global $pdo;
    
    try {
        // Verificar estrutura da tabela
        $stmt = $pdo->query("DESCRIBE acd_usuario_pa");
        $colunas = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $campos = array_column($colunas, 'Field');
        
        $campos_update = ['status = ?'];
        $valores = ['inativo'];
        
        if (in_array('data_fim', $campos)) {
            $campos_update[] = 'data_fim = ?';
            $valores[] = $data_fim ?? date('Y-m-d');
        }
        
        if (in_array('desativado_por', $campos)) {
            $campos_update[] = 'desativado_por = ?';
            $valores[] = $desativado_por ?? $_SESSION['user_id'] ?? 1;
        }
        
        if (in_array('desativado_em', $campos)) {
            $campos_update[] = 'desativado_em = NOW()';
        }
        
        if (in_array('observacoes', $campos) && $observacoes) {
            $campos_update[] = 'observacoes = CONCAT(IFNULL(observacoes, \'\'), \' | \', ?)';
            $valores[] = $observacoes;
        }
        
        $valores[] = $vinculo_id; // WHERE id = ?
        
        $sql = "UPDATE acd_usuario_pa SET " . implode(', ', $campos_update) . " WHERE id = ?";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($valores);
        
        if ($stmt->rowCount() > 0) {
            return [
                'sucesso' => true,
                'mensagem' => 'Vínculo desativado com sucesso'
            ];
        } else {
            return [
                'sucesso' => false,
                'erro' => 'Vínculo não encontrado ou já inativo'
            ];
        }
        
    } catch (Exception $e) {
        return [
            'sucesso' => false,
            'erro' => $e->getMessage()
        ];
    }
}

/**
 * Verificar se usuário tem vínculo ativo com PA
 */
function verificarVinculoAtivo($usuario_id, $pa_id, $data = null) {
    global $pdo;

    try {
        $sql = "
            SELECT COUNT(*)
            FROM acd_usuario_pa
            WHERE usuario_id = ?
            AND pa_id = ?
            AND (status = 'ativo' OR status = 1)
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([$usuario_id, $pa_id]);

        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Buscar histórico de vínculos de um usuário
 */
function buscarHistoricoUsuario($usuario_id) {
    global $pdo;
    
    try {
        $sql = "
            SELECT 
                v.*,
                p.nome as pa_nome,
                p.numero as pa_numero,
                u.nome_completo as usuario_nome
            FROM acd_usuario_pa v
            INNER JOIN pontos_atendimento p ON v.pa_id = p.id
            INNER JOIN usuarios u ON v.usuario_id = u.id
            WHERE v.usuario_id = ?
            ORDER BY COALESCE(v.data_inicio, v.data_vinculo) DESC
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$usuario_id]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Erro em buscarHistoricoUsuario: " . $e->getMessage());
        return [];
    }
}

/**
 * Buscar histórico de vínculos de um PA
 */
function buscarHistoricoPA($pa_id) {
    global $pdo;
    
    try {
        $sql = "
            SELECT 
                v.*,
                u.nome_completo as usuario_nome,
                u.email as usuario_email,
                p.nome as pa_nome,
                p.numero as pa_numero
            FROM acd_usuario_pa v
            INNER JOIN usuarios u ON v.usuario_id = u.id
            INNER JOIN pontos_atendimento p ON v.pa_id = p.id
            WHERE v.pa_id = ?
            ORDER BY COALESCE(v.data_inicio, v.data_vinculo) DESC
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$pa_id]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Erro em buscarHistoricoPA: " . $e->getMessage());
        return [];
    }
}
?>
