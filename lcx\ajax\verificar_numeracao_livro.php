<?php
session_start();
require_once '../../config/database.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit;
}

// Verificar se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit;
}

// Obter dados da requisição
$input = json_decode(file_get_contents('php://input'), true);

if (!isset($input['ponto_atendimento_id']) || empty($input['ponto_atendimento_id'])) {
    echo json_encode(['success' => false, 'message' => 'ID do ponto de atendimento é obrigatório']);
    exit;
}

$ponto_atendimento_id = (int)$input['ponto_atendimento_id'];

try {
    // Verificar se o PA existe
    $stmt = $pdo->prepare("SELECT nome, numero FROM pontos_atendimento WHERE id = ? AND ativo = 1");
    $stmt->execute([$ponto_atendimento_id]);
    $pa = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$pa) {
        echo json_encode(['success' => false, 'message' => 'Ponto de atendimento não encontrado']);
        exit;
    }
    
    // Buscar todos os livros do PA ordenados por data de criação
    $stmt = $pdo->prepare("
        SELECT nome, created_at 
        FROM lcx_livros_caixa 
        WHERE ponto_atendimento_id = ? 
        ORDER BY created_at ASC
    ");
    $stmt->execute([$ponto_atendimento_id]);
    $livros = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($livros)) {
        // Primeiro livro do PA
        echo json_encode([
            'success' => true,
            'primeiro_livro' => true,
            'pa_nome' => $pa['nome'],
            'pa_numero' => $pa['numero'],
            'total_livros' => 0
        ]);
        exit;
    }
    
    // Analisar padrão de numeração dos livros existentes
    $padrao_numeracao = analisarPadraoNumeracao($livros);
    
    if ($padrao_numeracao['tem_padrao']) {
        // Há um padrão identificado - sugerir próximo número
        $proximo_numero = $padrao_numeracao['proximo_numero'];
        $nome_base = $padrao_numeracao['nome_base'];
        $nome_sugerido = $nome_base . ' ' . $proximo_numero;
        
        echo json_encode([
            'success' => true,
            'primeiro_livro' => false,
            'nome_sugerido' => $nome_sugerido,
            'proximo_numero' => $proximo_numero,
            'padrao_identificado' => $padrao_numeracao['padrao'],
            'total_livros' => count($livros),
            'pa_nome' => $pa['nome'],
            'pa_numero' => $pa['numero']
        ]);
    } else {
        // Não há padrão claro - sugerir baseado no total + 1
        $proximo_numero = count($livros) + 1;
        $nome_sugerido = "Livro Caixa " . $proximo_numero;
        
        echo json_encode([
            'success' => true,
            'primeiro_livro' => false,
            'nome_sugerido' => $nome_sugerido,
            'proximo_numero' => $proximo_numero,
            'padrao_identificado' => 'Sequencial simples',
            'total_livros' => count($livros),
            'pa_nome' => $pa['nome'],
            'pa_numero' => $pa['numero']
        ]);
    }
    
} catch (Exception $e) {
    error_log("Erro ao verificar numeração do livro: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erro interno do servidor']);
}

/**
 * Analisa o padrão de numeração dos livros existentes
 */
function analisarPadraoNumeracao($livros) {
    if (empty($livros)) {
        return ['tem_padrao' => false];
    }
    
    $numeros_encontrados = [];
    $nome_base_comum = '';
    
    foreach ($livros as $livro) {
        $nome = $livro['nome'];
        
        // Tentar extrair número do final do nome
        if (preg_match('/^(.+?)\s+(\d+)$/', $nome, $matches)) {
            $base = trim($matches[1]);
            $numero = (int)$matches[2];
            
            $numeros_encontrados[] = [
                'base' => $base,
                'numero' => $numero,
                'nome_completo' => $nome
            ];
            
            // Definir nome base mais comum
            if (empty($nome_base_comum)) {
                $nome_base_comum = $base;
            }
        }
    }
    
    if (empty($numeros_encontrados)) {
        return ['tem_padrao' => false];
    }
    
    // Verificar se há consistência na base do nome
    $bases_unicas = array_unique(array_column($numeros_encontrados, 'base'));
    
    if (count($bases_unicas) === 1) {
        // Base consistente - calcular próximo número
        $numeros = array_column($numeros_encontrados, 'numero');
        $maior_numero = max($numeros);
        
        return [
            'tem_padrao' => true,
            'nome_base' => $nome_base_comum,
            'proximo_numero' => $maior_numero + 1,
            'padrao' => "Base: '{$nome_base_comum}' + número sequencial"
        ];
    } else {
        // Bases inconsistentes - usar padrão simples
        return ['tem_padrao' => false];
    }
}
?>
