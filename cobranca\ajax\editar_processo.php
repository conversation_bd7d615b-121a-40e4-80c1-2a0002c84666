<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

$processo_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

// Buscar informações do processo
$stmt = $pdo->prepare("
    SELECT p.*, m.nome as modalidade_nome, a.nome as advogado_nome
    FROM cbp_processos_judiciais p
    LEFT JOIN cbp_modalidades_processo m ON p.modalidade_id = m.id
    LEFT JOIN cbp_advogados a ON p.advogado_id = a.id
    WHERE p.id = ?
");
$stmt->execute([$processo_id]);
$processo = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$processo) {
    echo '<div class="alert alert-danger">Processo não encontrado.</div>';
    exit;
}

// Buscar modalidades
$stmt = $pdo->query("SELECT id, nome FROM cbp_modalidades_processo ORDER BY nome");
$modalidades = $stmt->fetchAll();

// Buscar advogados
$stmt = $pdo->query("SELECT id, nome FROM cbp_advogados WHERE ativo = 1 ORDER BY nome");
$advogados = $stmt->fetchAll();

// Buscar alvarás do processo
$stmt = $pdo->prepare("
    SELECT * FROM cbp_alvaras 
    WHERE processo_id = ? 
    ORDER BY data_recebimento DESC
");
$stmt->execute([$processo_id]);
$alvaras = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Verificar se o status atual do processo é "RETOMADO" (ID 4) ou "ACORDO JUDICIAL" (ID 2)
$retomado = $processo['status_id'] == 4;
$pode_alterar_retomado = $processo['status_id'] == 2 || $processo['status_id'] == 4;

// Verificar se existe acordo ativo
$stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_acordos WHERE processo_id = ? AND ativo = 1");
$stmt->execute([$processo_id]);
$tem_acordo_ativo = $stmt->fetchColumn() > 0;

// Verificar se tem acordos (ativos ou inativos)
$stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_acordos WHERE processo_id = ?");
$stmt->execute([$processo_id]);
$tem_acordos = $stmt->fetchColumn() > 0;

// Verificar se tem alvarás
$stmt = $pdo->prepare("SELECT COUNT(*) FROM cbp_alvaras WHERE processo_id = ?");
$stmt->execute([$processo_id]);
$tem_alvaras = $stmt->fetchColumn() > 0;

// Verificar se pode editar ajuizamento
$pode_editar_ajuizamento = !($tem_acordos || $tem_alvaras);

// Buscar contratos vinculados
$stmt = $pdo->prepare("
    SELECT 
        c.*,
        m.nome as modalidade_nome,
        pc.created_at as data_vinculo,
        CASE 
            WHEN LENGTH(c.associado_documento) = 11 THEN 
                CONCAT(
                    SUBSTRING(c.associado_documento, 1, 3), '.',
                    SUBSTRING(c.associado_documento, 4, 3), '.',
                    SUBSTRING(c.associado_documento, 7, 3), '-',
                    SUBSTRING(c.associado_documento, 10, 2)
                )
            ELSE 
                CONCAT(
                    SUBSTRING(c.associado_documento, 1, 2), '.',
                    SUBSTRING(c.associado_documento, 3, 3), '.',
                    SUBSTRING(c.associado_documento, 6, 3), '/',
                    SUBSTRING(c.associado_documento, 9, 4), '-',
                    SUBSTRING(c.associado_documento, 13, 2)
                )
        END as associado_documento_formatado
    FROM cbp_contratos c
    INNER JOIN cbp_modalidades_processo m ON m.id = c.modalidade_id
    INNER JOIN cbp_processos_contratos pc ON pc.contrato_id = c.id
    WHERE pc.processo_id = ?
    ORDER BY c.associado_nome ASC
");
$stmt->execute([$processo_id]);
$contratos = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<form id="formEditarProcesso" class="needs-validation" novalidate>
    <input type="hidden" name="id" value="<?php echo $processo['id']; ?>">
    <input type="hidden" name="pa_id" value="<?php echo $processo['pa_id']; ?>">
    
    <div class="row g-3">
        <div class="col-md-6">
            <label class="form-label">Nome</label>
            <input type="text" name="nome" class="form-control" value="<?php echo $processo['nome']; ?>" required>
        </div>
        <div class="col-md-6">
            <label class="form-label">CPF/CNPJ</label>
            <input type="text" name="cpf_cnpj" class="form-control cpf-cnpj" value="<?php echo $processo['cpf_cnpj']; ?>" required>
        </div>
        <div class="col-md-6">
            <label class="form-label">Número do Processo</label>
            <input type="text" name="numero_processo" class="form-control" value="<?php echo $processo['numero_processo']; ?>" required>
        </div>
        <div class="col-md-6">
            <label class="form-label">Número do Contrato</label>
            <input type="text" name="numero_contrato" class="form-control" value="<?php echo $processo['numero_contrato']; ?>" required>
        </div>
        
        <!-- Seção de Ajuizamento -->
        <div class="col-12">
            <div class="card shadow mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Informações de Ajuizamento</h5>
                    <button type="button" class="btn btn-success" onclick="abrirModalAjuizamento(<?php echo $processo['id']; ?>)" 
                        <?php echo $pode_editar_ajuizamento ? '' : 'disabled'; ?> 
                        title="<?php echo $pode_editar_ajuizamento ? '' : 'Não é possível editar as informações de ajuizamento pois este processo possui acordos ou alvarás'; ?>"
                        data-bs-toggle="<?php echo $pode_editar_ajuizamento ? '' : 'tooltip'; ?>"
                        data-pode-editar-ajuizamento="<?php echo $pode_editar_ajuizamento ? '1' : '0'; ?>">
                        <i class="fas fa-gavel me-2"></i><?php echo $processo['data_ajuizamento'] ? 'Editar Ajuizamento' : 'Incluir Ajuizamento'; ?>
                    </button>
                </div>
                <div class="card-body">
                    <?php if (!$processo['data_ajuizamento'] || !$processo['valor_ajuizado']): ?>
                        <div class="alert alert-warning mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>Nenhuma informação de ajuizamento registrada
                        </div>
                    <?php else: ?>
                        <?php if (!$pode_editar_ajuizamento): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>Não é possível editar as informações de ajuizamento pois este processo possui acordos ou alvarás cadastrados.
                            </div>
                        <?php endif; ?>
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="font-weight-bold">Data do Ajuizamento</h6>
                                <p class="h5"><?php echo date('d/m/Y', strtotime($processo['data_ajuizamento'])); ?></p>
                            </div>
                            <div class="col-md-6">
                                <h6 class="font-weight-bold">Valor Ajuizado</h6>
                                <p class="h5 text-success">R$ <?php echo number_format($processo['valor_ajuizado'], 2, ',', '.'); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Seção de Status do Processo -->
        <div class="col-12">
            <div class="card shadow mb-3">
                <div class="card-header">
                    <h5 class="mb-0">Status do Processo</h5>
                </div>
                <div class="card-body">
                    <div class="card border-left-warning shadow py-2">
                        <div class="card-body">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="retomado" name="retomado" value="1" 
                                    <?php echo $retomado ? 'checked' : ''; ?> 
                                    <?php echo $pode_alterar_retomado ? '' : 'disabled'; ?>>
                                <label class="form-check-label fw-bold text-warning" for="retomado">RETOMADO</label>
                                <?php if (!$pode_alterar_retomado): ?>
                                <small class="text-muted d-block mt-2">
                                    O status RETOMADO só pode ser alterado quando o processo está em ACORDO JUDICIAL.
                                </small>
                                <?php endif; ?>
                                <?php if ($tem_acordo_ativo): ?>
                                <div class="alert alert-info py-1 px-2 mt-2 mb-0">
                                    <i class="fas fa-info-circle me-1"></i> Este processo possui um acordo ativo. Marcando esta opção o acordo atual será inativado.
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Seção de Contratos -->
        <div class="col-12 mt-4">
            <div class="card shadow mb-3">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Contratos Vinculados</h5>
                    <button type="button" class="btn btn-success" onclick="abrirModalVincularContrato(<?php echo $processo['id']; ?>)">
                        <i class="fas fa-plus me-2"></i>Vincular Contrato
                    </button>
                </div>
                <div class="card-body">
                    <?php if (empty($contratos)): ?>
                        <div class="alert alert-warning mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>Nenhum contrato vinculado
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Nome</th>
                                        <th>CPF/CNPJ</th>
                                        <th>Número do Contrato</th>
                                        <th>Modalidade</th>
                                        <th>Data de Vínculo</th>
                                        <th>Ações</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($contratos as $contrato): ?>
                                        <tr>
                                            <td><?php echo $contrato['associado_nome']; ?></td>
                                            <td><?php echo $contrato['associado_documento_formatado']; ?></td>
                                            <td><?php echo $contrato['numero_contrato']; ?></td>
                                            <td><?php echo $contrato['modalidade_nome']; ?></td>
                                            <td><?php echo date('d/m/Y H:i:s', strtotime($contrato['data_vinculo'])); ?></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="desvincularContrato(<?php echo $processo['id']; ?>, <?php echo $contrato['id']; ?>)">
                                                    <i class="fas fa-unlink"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <label class="form-label">Modalidade</label>
            <select name="modalidade_id" class="form-select select2" required>
                <option value="">Selecione...</option>
                <?php foreach ($modalidades as $modalidade): ?>
                    <option value="<?php echo $modalidade['id']; ?>" <?php echo $modalidade['id'] == $processo['modalidade_id'] ? 'selected' : ''; ?>>
                        <?php echo $modalidade['nome']; ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="col-md-6">
            <label class="form-label">Advogado</label>
            <select name="advogado_id" class="form-select select2" required>
                <option value="">Selecione...</option>
                <?php foreach ($advogados as $advogado): ?>
                    <option value="<?php echo $advogado['id']; ?>" <?php echo $advogado['id'] == $processo['advogado_id'] ? 'selected' : ''; ?>>
                        <?php echo $advogado['nome']; ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="col-12">
            <label class="form-label">Observações</label>
            <textarea name="observacoes" class="form-control" rows="3"><?php echo $processo['observacoes']; ?></textarea>
        </div>
    </div>

    <!-- Modal Alvará -->
    <div class="modal fade" id="modalAlvara" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Incluir Alvará</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="conteudoAlvara">
                    <!-- Conteúdo será carregado via AJAX -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Ajuizamento -->
    <div class="modal fade" id="modalAjuizamento" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Informações de Ajuizamento</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="conteudoAjuizamento">
                    <!-- Conteúdo será carregado via AJAX -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Vincular Contrato -->
    <div class="modal fade" id="modalVincularContrato" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Vincular Contrato</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Selecione o Contrato</label>
                        <select id="contrato_id" class="form-select select2-contratos" style="width: 100%"></select>
                    </div>
                    <div class="d-flex justify-content-end gap-2">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-primary" onclick="vincularContrato(<?php echo $processo['id']; ?>)">Vincular</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-end gap-2 mt-4">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
        <button type="button" class="btn btn-primary" onclick="window.parent.salvarEdicao()">Salvar Alterações</button>
    </div>
</form>

<script>
function abrirModalAlvara(processoId) {
    $.get('ajax/form_alvara.php', { 
        processo_id: processoId
    }, function(response) {
        $('#conteudoAlvara').html(response);
        $('#modalAlvara').modal('show');

        // Inicializar máscaras após carregar o conteúdo do modal
        $('#modalAlvara .money').inputmask({
            alias: 'currency',
            radixPoint: ',',
            groupSeparator: '.',
            allowMinus: false,
            prefix: 'R$ ',
            digits: 2,
            digitsOptional: false,
            rightAlign: false,
            unmaskAsNumber: true,
            autoUnmask: true,
            removeMaskOnSubmit: true
        });
    }).fail(function() {
        Swal.fire('Erro!', 'Não foi possível carregar o formulário de alvará.', 'error');
    });
}

// Quando o modal de alvará for fechado, recarregar o formulário de edição para atualizar a lista de alvarás
$('#modalAlvara').on('hidden.bs.modal', function () {
    editarProcesso(<?php echo $processo_id; ?>);
});

// Função para confirmar exclusão de alvará
function confirmarExclusaoAlvara(alvaraId) {
    Swal.fire({
        title: 'Tem certeza?',
        text: "Esta ação não poderá ser revertida!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Sim, excluir!',
        cancelButtonText: 'Cancelar'
    }).then((result) => {
        if (result.isConfirmed) {
            excluirAlvara(alvaraId);
        }
    });
}

// Função para excluir alvará
function excluirAlvara(alvaraId) {
    $.ajax({
        url: 'ajax/excluir_alvara.php',
        type: 'POST',
        data: { alvara_id: alvaraId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                Swal.fire('Sucesso!', response.message, 'success').then(() => {
                    // Recarregar o formulário para atualizar a lista de alvarás
                    editarProcesso(<?php echo $processo_id; ?>);
                });
            } else {
                Swal.fire('Erro!', response.message, 'error');
            }
        },
        error: function() {
            Swal.fire('Erro!', 'Não foi possível excluir o alvará.', 'error');
        }
    });
}

function abrirModalAjuizamento(processoId) {
    $.get('ajax/form_ajuizamento.php', { 
        processo_id: processoId
    }, function(response) {
        $('#conteudoAjuizamento').html(response);
        $('#modalAjuizamento').modal('show');
    }).fail(function() {
        Swal.fire('Erro!', 'Não foi possível carregar o formulário de ajuizamento.', 'error');
    });
}

// Quando o modal de ajuizamento for fechado, recarregar o formulário de edição para atualizar os dados
$('#modalAjuizamento').on('hidden.bs.modal', function () {
    editarProcesso(<?php echo $processo_id; ?>);
});

function abrirModalVincularContrato(processoId) {
    $('#modalVincularContrato').modal('show');
    
    // Inicializar select2 para busca de contratos
    $('.select2-contratos').select2({
        dropdownParent: $('#modalVincularContrato'),
        width: '100%',
        ajax: {
            url: 'ajax/buscar_contratos.php',
            dataType: 'json',
            delay: 250,
            data: function(params) {
                return {
                    search: params.term,
                    processo_id: processoId
                };
            },
            processResults: function(data) {
                return {
                    results: data.results
                };
            },
            cache: true
        },
        placeholder: 'Digite o nome, CPF/CNPJ ou número do contrato',
        minimumInputLength: 3,
        language: {
            inputTooShort: function() {
                return 'Digite pelo menos 3 caracteres para buscar';
            },
            noResults: function() {
                return 'Nenhum contrato encontrado';
            },
            searching: function() {
                return 'Buscando...';
            }
        }
    });
}

function vincularContrato(processoId) {
    const contratoId = $('#contrato_id').val();
    
    if (!contratoId) {
        Swal.fire('Atenção!', 'Selecione um contrato para vincular.', 'warning');
        return;
    }
    
    $.ajax({
        url: 'ajax/vincular_contrato.php',
        type: 'POST',
        data: {
            processo_id: processoId,
            contrato_id: contratoId,
            acao: 'vincular'
        },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                Swal.fire('Sucesso!', response.message, 'success').then(() => {
                    $('#modalVincularContrato').modal('hide');
                    editarProcesso(processoId);
                });
            } else {
                Swal.fire('Erro!', response.message, 'error');
            }
        },
        error: function() {
            Swal.fire('Erro!', 'Não foi possível vincular o contrato.', 'error');
        }
    });
}

function desvincularContrato(processoId, contratoId) {
    Swal.fire({
        title: 'Confirmar Desvinculação',
        text: 'Tem certeza que deseja desvincular este contrato do processo?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: 'Sim',
        cancelButtonText: 'Não'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: 'ajax/vincular_contrato.php',
                type: 'POST',
                data: {
                    processo_id: processoId,
                    contrato_id: contratoId,
                    acao: 'desvincular'
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        Swal.fire('Sucesso!', response.message, 'success').then(() => {
                            editarProcesso(processoId);
                        });
                    } else {
                        Swal.fire('Erro!', response.message, 'error');
                    }
                },
                error: function() {
                    Swal.fire('Erro!', 'Não foi possível desvincular o contrato.', 'error');
                }
            });
        }
    });
}
</script> 