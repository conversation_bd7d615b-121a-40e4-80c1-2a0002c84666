<?php
require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';
session_start();

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se o usuário tem permissão para acessar a pasta ACD
if (!checkACDPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Verificar se o usuário é administrador ou gestor
if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

$mensagem = '';
$tipo_mensagem = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validar se todos os campos necessários foram enviados
    $campos_obrigatorios = ['arquivo', 'data_inicio', 'data_fim'];
    $campos_faltantes = array_filter($campos_obrigatorios, function($campo) {
        return !isset($_POST[$campo]) && !isset($_FILES[$campo]);
    });

    if (!empty($campos_faltantes)) {
        $mensagem = 'Por favor, preencha todos os campos obrigatórios.';
        $tipo_mensagem = 'warning';
    } else {
        $arquivo = $_FILES['arquivo'];
        $data_inicio = $_POST['data_inicio'];
        $data_fim = $_POST['data_fim'];

        // Validar datas
        if (strtotime($data_inicio) > strtotime($data_fim)) {
            $mensagem = 'A data inicial não pode ser maior que a data final.';
            $tipo_mensagem = 'danger';
        } else {
            // Validar arquivo
            $extensao = strtolower(pathinfo($arquivo['name'], PATHINFO_EXTENSION));
            if (!in_array($extensao, ['xls', 'xlsx', 'csv'])) {
                $mensagem = 'Por favor, envie apenas arquivos Excel (.xls, .xlsx) ou CSV (.csv).';
                $tipo_mensagem = 'danger';
            } else {
                // Validar se houve erro no upload
                if ($arquivo['error'] !== UPLOAD_ERR_OK) {
                    $mensagens_erro = [
                        UPLOAD_ERR_INI_SIZE => 'O arquivo excede o tamanho máximo permitido pelo PHP.',
                        UPLOAD_ERR_FORM_SIZE => 'O arquivo excede o tamanho máximo permitido pelo formulário.',
                        UPLOAD_ERR_PARTIAL => 'O upload do arquivo foi feito parcialmente.',
                        UPLOAD_ERR_NO_FILE => 'Nenhum arquivo foi enviado.',
                        UPLOAD_ERR_NO_TMP_DIR => 'Pasta temporária ausente.',
                        UPLOAD_ERR_CANT_WRITE => 'Falha ao escrever o arquivo em disco.',
                        UPLOAD_ERR_EXTENSION => 'Uma extensão PHP interrompeu o upload do arquivo.'
                    ];
                    $mensagem = $mensagens_erro[$arquivo['error']] ?? 'Erro desconhecido no upload do arquivo.';
                    $tipo_mensagem = 'danger';
                } else {
                    $registros_validos = [];
                    $linha_atual = 0;

                    try {
                        if ($extensao === 'csv') {
                            // Processar arquivo CSV
                            if (($handle = fopen($arquivo['tmp_name'], "r")) !== FALSE) {
                                // Detectar o delimitador
                                $primeira_linha = fgets($handle);
                                rewind($handle);

                                // Verificar se é ponto e vírgula ou vírgula
                                $delimitador = (strpos($primeira_linha, ';') !== false) ? ';' : ',';

                                // Log para debug
                                error_log("Delimitador detectado: " . $delimitador);
                                error_log("Primeira linha do arquivo: " . $primeira_linha);

                                // Função para converter notação científica
                                function converterNotacaoCientifica($valor) {
                                    if (empty($valor)) return '';

                                    // Se contém E+ ou e+, é notação científica
                                    if (preg_match('/(\d+[,.]?\d*)[Ee]\+(\d+)/', $valor, $matches)) {
                                        $numero = str_replace(',', '.', $matches[1]);
                                        $expoente = (int)$matches[2];
                                        $resultado = number_format($numero * pow(10, $expoente), 0, '', '');
                                        return $resultado;
                                    }

                                    return $valor;
                                }

                                // Função para verificar se é uma linha de dados válida
                                function isLinhaValida($row) {
                                    if (empty($row) || count($row) < 6) return false;

                                    $primeira_coluna = trim($row[0] ?? '');

                                    // Verificar se a primeira coluna contém um número (proposta)
                                    // Pode ser notação científica (ex: 2,52541E+11) ou número normal
                                    if (preg_match('/^\d+([,.]?\d*)?([Ee]\+\d+)?$/', $primeira_coluna)) {
                                        // Verificar se tem CPF/CNPJ na coluna 4 (índice 4)
                                        $cpf_cnpj = trim($row[4] ?? '');
                                        if (!empty($cpf_cnpj) && preg_match('/[\d\.\-\/]/', $cpf_cnpj)) {
                                            return true;
                                        }
                                    }

                                    return false;
                                }

                                // Ler todas as linhas do arquivo
                                $linhas_processadas = 0;
                                $linhas_invalidas = 0;
                                $ultima_proposta = '';
                                $linha_numero = 0;

                                while (($row = fgetcsv($handle, 0, $delimitador)) !== FALSE) {
                                    $linha_numero++;

                                    // Verificar se é uma linha válida de dados
                                    if (isLinhaValida($row)) {
                                        $linhas_processadas++;

                                        // Log para debug das primeiras linhas válidas
                                        if ($linhas_processadas <= 5) {
                                            error_log("Linha válida " . $linha_numero . ": " . print_r($row, true));
                                        }

                                        // Extrair dados da linha
                                        $numero_proposta = converterNotacaoCientifica(trim($row[0]));
                                        $cpf_cnpj = preg_replace('/[^0-9]/', '', trim($row[4] ?? ''));
                                        $nome = trim($row[9] ?? '');
                                        $valor_bruto = trim($row[13] ?? '0');
                                        $prazo_dia = (int)trim($row[16] ?? 0);
                                        $situacao = trim($row[17] ?? '');

                                        // Limpar e converter valor
                                        $valor = str_replace(['R$', '.', ','], ['', '', '.'], $valor_bruto);
                                        $valor = preg_replace('/[^\d,.]/', '', $valor);
                                        if (strpos($valor, ',') !== false && strpos($valor, '.') === false) {
                                            $valor = str_replace(',', '.', $valor);
                                        }
                                        $valor = (float)$valor;

                                        // Validar dados essenciais
                                        if (!empty($numero_proposta) && !empty($cpf_cnpj) && !empty($nome) && $valor > 0) {
                                            $registros_validos[] = [
                                                'numero_proposta' => $numero_proposta,
                                                'cpf_cnpj' => $cpf_cnpj,
                                                'nome' => $nome,
                                                'valor' => $valor,
                                                'prazo_dia' => $prazo_dia,
                                                'situacao' => $situacao
                                            ];
                                            $ultima_proposta = $numero_proposta;

                                            // Log detalhado das primeiras 3 propostas válidas
                                            if (count($registros_validos) <= 3) {
                                                error_log("Proposta " . count($registros_validos) . " extraída:");
                                                error_log("  Número: " . $numero_proposta);
                                                error_log("  CPF/CNPJ: " . $cpf_cnpj);
                                                error_log("  Nome: " . $nome);
                                                error_log("  Valor: " . $valor);
                                                error_log("  Prazo: " . $prazo_dia);
                                                error_log("  Situação: " . $situacao);
                                            }
                                        } else {
                                            $linhas_invalidas++;
                                            if ($linhas_invalidas <= 3) {
                                                error_log("Linha com dados incompletos " . $linha_numero . ":");
                                                error_log("  Proposta: " . $numero_proposta);
                                                error_log("  CPF/CNPJ: " . $cpf_cnpj);
                                                error_log("  Nome: " . $nome);
                                                error_log("  Valor: " . $valor);
                                            }
                                        }
                                    }
                                }
                                fclose($handle);

                                // Log final
                                error_log("Total de linhas no arquivo: " . $linha_numero);
                                error_log("Total de linhas válidas processadas: " . $linhas_processadas);
                                error_log("Total de linhas com dados incompletos: " . $linhas_invalidas);
                                error_log("Total de registros válidos extraídos: " . count($registros_validos));
                                if (!empty($ultima_proposta)) {
                                    error_log("Última proposta válida encontrada: " . $ultima_proposta);
                                }
                            }
                        } else {
                            // Processar arquivo Excel (mesmo código do importar_limites.php)
                            require 'vendor/autoload.php';
                            $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load($arquivo['tmp_name']);
                            $worksheet = $spreadsheet->getActiveSheet();

                            // Obter o intervalo de células usado
                            $highestRow = $worksheet->getHighestRow();
                            $highestColumn = $worksheet->getHighestColumn();

                            // Log para debug
                            error_log("Total de linhas no Excel: " . $highestRow);
                            error_log("Última coluna: " . $highestColumn);

                            $linhas_processadas = 0;
                            $linhas_invalidas = 0;
                            $ultima_proposta = '';

                            // Ler cada linha individualmente
                            for ($row = 6; $row <= $highestRow; $row++) { // Começa da linha 6 (após cabeçalhos)
                                $linhas_processadas++;

                                // Ler o valor exato da célula da proposta (coluna A)
                                $numero_proposta = trim($worksheet->getCellByColumnAndRow(1, $row)->getValue());

                                // Log para debug das primeiras linhas
                                if ($linhas_processadas <= 3) {
                                    error_log("Lendo linha " . $row . ":");
                                    error_log("Proposta: " . $numero_proposta);
                                    error_log("CPF: " . $worksheet->getCellByColumnAndRow(2, $row)->getValue());
                                    error_log("Nome: " . $worksheet->getCellByColumnAndRow(3, $row)->getValue());
                                    error_log("Valor: " . $worksheet->getCellByColumnAndRow(4, $row)->getValue());
                                    error_log("Prazo: " . $worksheet->getCellByColumnAndRow(5, $row)->getValue());
                                    error_log("Situação: " . $worksheet->getCellByColumnAndRow(6, $row)->getValue());
                                }

                                if (!empty($numero_proposta)) {
                                    // Ler valores exatos de cada célula
                                    $registros_validos[] = [
                                        'numero_proposta' => $numero_proposta,
                                        'cpf_cnpj' => preg_replace('/[^0-9]/', '', $worksheet->getCellByColumnAndRow(2, $row)->getValue() ?? ''),
                                        'nome' => trim($worksheet->getCellByColumnAndRow(3, $row)->getValue() ?? ''),
                                        'valor' => str_replace(
                                            ['R$', '.', ','],
                                            ['', '', '.'],
                                            $worksheet->getCellByColumnAndRow(4, $row)->getValue() ?? '0'
                                        ),
                                        'prazo_dia' => (int)($worksheet->getCellByColumnAndRow(5, $row)->getValue() ?? 0),
                                        'situacao' => trim($worksheet->getCellByColumnAndRow(6, $row)->getValue() ?? '')
                                    ];
                                    $ultima_proposta = $numero_proposta;
                                } else {
                                    $linhas_invalidas++;
                                    if ($linhas_invalidas <= 3) {
                                        error_log("Linha inválida " . $row . ": Número da proposta não encontrado");
                                        // Log dos valores das células para debug
                                        error_log("Valores da linha " . $row . ":");
                                        for ($col = 1; $col <= 6; $col++) {
                                            error_log("Coluna " . $col . ": " . $worksheet->getCellByColumnAndRow($col, $row)->getValue());
                                        }
                                    }
                                }
                            }

                            // Log final
                            error_log("Total de linhas Excel processadas: " . $linhas_processadas);
                            error_log("Total de linhas Excel inválidas: " . $linhas_invalidas);
                            error_log("Total de registros Excel válidos: " . count($registros_validos));
                            if (!empty($ultima_proposta)) {
                                error_log("Última proposta válida encontrada no Excel: " . $ultima_proposta);
                            }
                        }

                        if (empty($registros_validos)) {
                            $mensagem = 'Nenhum registro válido encontrado no arquivo. ';
                            if ($linhas_processadas > 0) {
                                $mensagem .= "Foram processadas {$linhas_processadas} linhas, mas nenhuma continha um número de proposta válido. ";
                                if ($linhas_invalidas > 0) {
                                    $mensagem .= "Verifique se o número da proposta está na primeira coluna.";
                                }
                            } else {
                                $mensagem .= "O arquivo parece estar vazio ou no formato incorreto.";
                            }
                            $tipo_mensagem = 'warning';
                        } else {
                            // Iniciar transação
                            $pdo->beginTransaction();

                            try {
                                $stmt = $pdo->prepare("INSERT INTO acd_creditorural
                                    (numero_proposta, cpf_cnpj, nome, valor, prazo_dia, situacao,
                                    data_inicio, data_fim, importado_por)
                                    VALUES
                                    (:numero_proposta, :cpf_cnpj, :nome, :valor, :prazo_dia, :situacao,
                                    :data_inicio, :data_fim, :importado_por)");

                                foreach ($registros_validos as $registro) {
                                    $stmt->execute([
                                        'numero_proposta' => $registro['numero_proposta'],
                                        'cpf_cnpj' => $registro['cpf_cnpj'],
                                        'nome' => $registro['nome'],
                                        'valor' => $registro['valor'],
                                        'prazo_dia' => $registro['prazo_dia'],
                                        'situacao' => $registro['situacao'],
                                        'data_inicio' => $data_inicio,
                                        'data_fim' => $data_fim,
                                        'importado_por' => $_SESSION['user_id']
                                    ]);
                                }

                                $pdo->commit();
                                $mensagem = count($registros_validos) . ' registros de crédito rural importados com sucesso!';
                                $tipo_mensagem = 'success';

                            } catch (Exception $e) {
                                $pdo->rollBack();
                                $mensagem = 'Erro ao importar registros: ' . $e->getMessage();
                                $tipo_mensagem = 'danger';
                            }
                        }

                    } catch (Exception $e) {
                        $mensagem = 'Erro ao processar arquivo: ' . $e->getMessage();
                        $tipo_mensagem = 'danger';
                    }
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="../assets/images/Sicoob.ico">
    <title>Importar Crédito Rural - Análise de Crédito</title>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <style>
        :root {
            --primary-color: #003641;
            --secondary-color: #00AE9D;
            --text-light: #ffffff;
            --text-dark: #333333;
            --bg-light: #f5f5f5;
            --bg-white: #ffffff;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: var(--bg-light);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .wrapper {
            display: flex;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 1.5rem;
            transition: all 0.3s ease;
        }

        .card {
            background: var(--bg-white);
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
        }

        .card-header {
            background: var(--primary-color);
            color: var(--text-light);
            padding: 1rem;
            border-radius: 8px 8px 0 0;
        }

        .card-body {
            padding: 1.5rem;
        }

        .form-label {
            font-weight: 600;
            color: var(--primary-color);
        }

        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .alert {
            margin-bottom: 1rem;
        }

        .help-text {
            font-size: 0.875rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
        }

        .file-input-wrapper input[type=file] {
            position: absolute;
            left: 0;
            top: 0;
            opacity: 0;
            cursor: pointer;
        }

        .file-input-trigger {
            display: inline-block;
            padding: 0.5rem 1rem;
            background: var(--bg-light);
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }

        .file-name {
            margin-left: 0.5rem;
            font-size: 0.875rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'components/sidebar.php'; ?>

        <div class="main-content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h4 class="mb-0">Importar Crédito Rural</h4>
                            </div>
                            <div class="card-body">
                                <?php if ($mensagem): ?>
                                    <div class="alert alert-<?php echo $tipo_mensagem; ?> alert-dismissible fade show" role="alert">
                                        <?php echo $mensagem; ?>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                    </div>
                                <?php endif; ?>

                                <form method="POST" enctype="multipart/form-data">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="data_inicio" class="form-label">Data Inicial</label>
                                            <input type="date" class="form-control" id="data_inicio" name="data_inicio" required>
                                            <div class="help-text">Data inicial do período do crédito rural</div>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="data_fim" class="form-label">Data Final</label>
                                            <input type="date" class="form-control" id="data_fim" name="data_fim" required>
                                            <div class="help-text">Data final do período do crédito rural</div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="arquivo" class="form-label">Arquivo</label>
                                        <div class="file-input-wrapper">
                                            <button type="button" class="file-input-trigger">
                                                <i class="fas fa-file-excel"></i> Selecionar Arquivo
                                            </button>
                                            <input type="file" class="form-control" id="arquivo" name="arquivo" accept=".xls,.xlsx,.csv" required>
                                            <span class="file-name">Nenhum arquivo selecionado</span>
                                        </div>
                                        <div class="help-text">
                                            Selecione um arquivo Excel (.xls, .xlsx) ou CSV (.csv) contendo o crédito rural.<br>
                                            O arquivo deve conter as colunas: Nº Proposta, CPF/CNPJ, Nome, Valor, Prazo/Dia e Situação.<br>
                                            Para arquivos CSV, use ponto e vírgula (;) ou vírgula (,) como delimitador.
                                        </div>
                                    </div>

                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-upload"></i> Importar
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // Atualizar nome do arquivo selecionado
            $('#arquivo').on('change', function(e) {
                const fileName = this.files[0]?.name || 'Nenhum arquivo selecionado';
                $('.file-name').text(fileName);
            });

            // Validar datas
            $('form').on('submit', function(e) {
                const dataInicio = new Date($('#data_inicio').val());
                const dataFim = new Date($('#data_fim').val());

                if (dataInicio > dataFim) {
                    e.preventDefault();
                    alert('A data inicial não pode ser maior que a data final.');
                }
            });

            // Prevenir loop infinito no input file
            $('.file-input-trigger').on('click', function(e) {
                e.preventDefault();
                $('#arquivo').trigger('click');
            });
        });
    </script>
</body>
</html> 