<?php
/**
 * Funções do dashboard que consideram o histórico de vínculos
 */

/**
 * Buscar responsável por um PA em uma data específica para relatórios
 */
function buscarResponsavelPAParaRelatorio($pa_nome, $data_proposta) {
    global $pdo;
    
    // Primeiro, buscar o ID do PA pelo nome
    $stmt = $pdo->prepare("SELECT id FROM pontos_atendimento WHERE nome = ? LIMIT 1");
    $stmt->execute([$pa_nome]);
    $pa = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$pa) {
        return null; // PA não encontrado
    }
    
    $pa_id = $pa['id'];
    
    // Buscar quem era responsável pelo PA na data da proposta
    $sql = "
        SELECT 
            v.usuario_id,
            u.nome_completo as usuario_nome,
            u.email as usuario_email,
            v.data_inicio,
            v.data_fim
        FROM acd_usuario_pa v
        INNER JOIN usuarios u ON v.usuario_id = u.id
        WHERE v.pa_id = ?
        AND v.data_inicio <= ?
        AND (v.data_fim IS NULL OR v.data_fim > ?)
        AND v.status = 'ativo'
        ORDER BY v.data_inicio DESC
        LIMIT 1
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$pa_id, $data_proposta, $data_proposta]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * Buscar propostas com responsável correto baseado na data
 */
function buscarPropostasComResponsavel($periodo_inicio, $periodo_fim, $pa_filtro = null) {
    global $pdo;
    
    $where_pa = $pa_filtro ? "AND f.pa = ?" : "";
    $params = [$periodo_inicio, $periodo_fim];
    if ($pa_filtro) {
        $params[] = $pa_filtro;
    }
    
    $sql = "
        SELECT 
            f.*,
            v.usuario_id as responsavel_id,
            u.nome_completo as responsavel_nome,
            u.email as responsavel_email
        FROM acd_formularios f
        LEFT JOIN pontos_atendimento pa ON f.pa = pa.nome
        LEFT JOIN acd_usuario_pa v ON (
            pa.id = v.pa_id 
            AND v.data_inicio <= f.data_proposta
            AND (v.data_fim IS NULL OR v.data_fim > f.data_proposta)
            AND v.status = 'ativo'
        )
        LEFT JOIN usuarios u ON v.usuario_id = u.id
        WHERE f.data_proposta BETWEEN ? AND ?
        $where_pa
        ORDER BY f.data_proposta DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Buscar estatísticas por PA considerando histórico
 */
function buscarEstatisticasPorPA($periodo_inicio, $periodo_fim) {
    global $pdo;
    
    $sql = "
        SELECT 
            f.pa,
            COUNT(*) as total_propostas,
            SUM(CASE WHEN f.status_final = 'aprovado' THEN 1 ELSE 0 END) as aprovadas,
            SUM(CASE WHEN f.status_final = 'rejeitado' THEN 1 ELSE 0 END) as rejeitadas,
            v.usuario_id as responsavel_id,
            u.nome_completo as responsavel_nome
        FROM acd_formularios f
        LEFT JOIN pontos_atendimento pa ON f.pa = pa.nome
        LEFT JOIN acd_usuario_pa v ON (
            pa.id = v.pa_id 
            AND v.data_inicio <= f.data_proposta
            AND (v.data_fim IS NULL OR v.data_fim > f.data_proposta)
            AND v.status = 'ativo'
        )
        LEFT JOIN usuarios u ON v.usuario_id = u.id
        WHERE f.data_proposta BETWEEN ? AND ?
        GROUP BY f.pa, v.usuario_id, u.nome_completo
        ORDER BY total_propostas DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$periodo_inicio, $periodo_fim]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Buscar estatísticas por usuário considerando histórico
 */
function buscarEstatisticasPorUsuario($periodo_inicio, $periodo_fim) {
    global $pdo;
    
    $sql = "
        SELECT 
            v.usuario_id,
            u.nome_completo as usuario_nome,
            u.email as usuario_email,
            COUNT(f.id) as total_propostas,
            SUM(CASE WHEN f.status_final = 'aprovado' THEN 1 ELSE 0 END) as aprovadas,
            SUM(CASE WHEN f.status_final = 'rejeitado' THEN 1 ELSE 0 END) as rejeitadas,
            ROUND(
                (SUM(CASE WHEN f.status_final = 'aprovado' THEN 1 ELSE 0 END) * 100.0) / 
                NULLIF(COUNT(f.id), 0), 2
            ) as taxa_aprovacao,
            GROUP_CONCAT(DISTINCT f.pa SEPARATOR ', ') as pas_responsavel
        FROM acd_usuario_pa v
        INNER JOIN usuarios u ON v.usuario_id = u.id
        LEFT JOIN pontos_atendimento pa ON v.pa_id = pa.id
        LEFT JOIN acd_formularios f ON (
            f.pa = pa.nome
            AND f.data_proposta BETWEEN v.data_inicio AND IFNULL(v.data_fim, CURDATE())
            AND f.data_proposta BETWEEN ? AND ?
        )
        WHERE v.status = 'ativo'
        AND (
            (v.data_inicio <= ? AND (v.data_fim IS NULL OR v.data_fim >= ?))
        )
        GROUP BY v.usuario_id, u.nome_completo, u.email
        HAVING total_propostas > 0
        ORDER BY total_propostas DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$periodo_inicio, $periodo_fim, $periodo_fim, $periodo_inicio]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Buscar desempenho individual considerando histórico
 */
function buscarDesempenhoIndividual($usuario_id, $periodo_inicio, $periodo_fim) {
    global $pdo;
    
    $sql = "
        SELECT 
            f.*,
            pa.nome as pa_nome,
            pa.numero as pa_numero
        FROM acd_formularios f
        INNER JOIN pontos_atendimento pa ON f.pa = pa.nome
        INNER JOIN acd_usuario_pa v ON (
            pa.id = v.pa_id 
            AND v.usuario_id = ?
            AND v.data_inicio <= f.data_proposta
            AND (v.data_fim IS NULL OR v.data_fim > f.data_proposta)
            AND v.status = 'ativo'
        )
        WHERE f.data_proposta BETWEEN ? AND ?
        ORDER BY f.data_proposta DESC
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$usuario_id, $periodo_inicio, $periodo_fim]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Verificar se usuário era responsável por PA em determinada data
 */
function verificarResponsabilidadeNaData($usuario_id, $pa_nome, $data) {
    global $pdo;
    
    $sql = "
        SELECT COUNT(*) 
        FROM acd_usuario_pa v
        INNER JOIN pontos_atendimento pa ON v.pa_id = pa.id
        WHERE v.usuario_id = ?
        AND pa.nome = ?
        AND v.data_inicio <= ?
        AND (v.data_fim IS NULL OR v.data_fim > ?)
        AND v.status = 'ativo'
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$usuario_id, $pa_nome, $data, $data]);
    
    return $stmt->fetchColumn() > 0;
}

/**
 * Buscar PAs que um usuário foi responsável em um período
 */
function buscarPAsResponsavelNoPeriodo($usuario_id, $periodo_inicio, $periodo_fim) {
    global $pdo;
    
    $sql = "
        SELECT DISTINCT
            pa.id,
            pa.nome,
            pa.numero,
            v.data_inicio,
            v.data_fim
        FROM acd_usuario_pa v
        INNER JOIN pontos_atendimento pa ON v.pa_id = pa.id
        WHERE v.usuario_id = ?
        AND v.status = 'ativo'
        AND (
            (v.data_inicio <= ? AND (v.data_fim IS NULL OR v.data_fim >= ?))
            OR (v.data_inicio BETWEEN ? AND ?)
        )
        ORDER BY pa.nome
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$usuario_id, $periodo_fim, $periodo_inicio, $periodo_inicio, $periodo_fim]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * Buscar ranking de PAs considerando responsável na data
 */
function buscarRankingPAsComHistorico($periodo_inicio, $periodo_fim, $tipo_ranking = 'propostas') {
    global $pdo;
    
    $order_by = '';
    switch ($tipo_ranking) {
        case 'aprovacao':
            $order_by = 'taxa_aprovacao DESC, total_propostas DESC';
            break;
        case 'rejeicao':
            $order_by = 'taxa_rejeicao DESC, total_propostas DESC';
            break;
        default:
            $order_by = 'total_propostas DESC';
    }
    
    $sql = "
        SELECT 
            f.pa,
            pa.numero as pa_numero,
            COUNT(f.id) as total_propostas,
            SUM(CASE WHEN f.status_final = 'aprovado' THEN 1 ELSE 0 END) as aprovadas,
            SUM(CASE WHEN f.status_final = 'rejeitado' THEN 1 ELSE 0 END) as rejeitadas,
            ROUND(
                (SUM(CASE WHEN f.status_final = 'aprovado' THEN 1 ELSE 0 END) * 100.0) / 
                NULLIF(COUNT(f.id), 0), 2
            ) as taxa_aprovacao,
            ROUND(
                (SUM(CASE WHEN f.status_final = 'rejeitado' THEN 1 ELSE 0 END) * 100.0) / 
                NULLIF(COUNT(f.id), 0), 2
            ) as taxa_rejeicao,
            v.usuario_id as responsavel_atual_id,
            u.nome_completo as responsavel_atual_nome
        FROM acd_formularios f
        LEFT JOIN pontos_atendimento pa ON f.pa = pa.nome
        LEFT JOIN acd_usuario_pa v ON (
            pa.id = v.pa_id 
            AND v.data_inicio <= f.data_proposta
            AND (v.data_fim IS NULL OR v.data_fim > f.data_proposta)
            AND v.status = 'ativo'
        )
        LEFT JOIN usuarios u ON v.usuario_id = u.id
        WHERE f.data_proposta BETWEEN ? AND ?
        GROUP BY f.pa, pa.numero, v.usuario_id, u.nome_completo
        HAVING total_propostas > 0
        ORDER BY $order_by
        LIMIT 20
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$periodo_inicio, $periodo_fim]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>
