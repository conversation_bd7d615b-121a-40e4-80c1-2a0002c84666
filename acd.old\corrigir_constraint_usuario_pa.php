<?php
/**
 * <PERSON>ript para corrigir o erro de constraint na tabela acd_usuario_pa
 * SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row:
 * a foreign key constraint fails (`sicoob_access_control`.`acd_usuario_pa`,
 * CONSTRAINT `acd_usuario_pa_ibfk_3` FOREIGN KEY (`usuario_vinculo`) REFERENCES `usuarios` (`id`))
 */

require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

// Verificar se o usuário está logado
session_start();
if (!isset($_SESSION['user_id'])) {
    die("Acesso negado. Faça login para continuar.");
}

// Verificar se o usuário tem permissão para acessar a pasta ACD
if (!checkACDPermission($_SESSION['user_id'])) {
    die("Acesso negado. Você não tem permissão para acessar o sistema ACD.");
}

// Verificar se o usuário é administrador ou gestor
if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    die("Acesso negado. Apenas administradores e gestores podem executar esta correção.");
}

echo "<h2>🔧 Correção de Constraint - acd_usuario_pa</h2>";
echo "<p>Este script irá corrigir o erro de foreign key constraint na tabela acd_usuario_pa.</p>";

try {
    echo "<h3>📋 Diagnóstico do Problema</h3>";
    
    // Verificar se a tabela existe
    $stmt = $pdo->query("SHOW TABLES LIKE 'acd_usuario_pa'");
    if ($stmt->rowCount() == 0) {
        echo "<p>❌ Tabela acd_usuario_pa não existe!</p>";
        exit;
    }
    
    // Verificar estrutura atual
    echo "<h4>🏗️ Estrutura Atual:</h4>";
    $stmt = $pdo->query("DESCRIBE acd_usuario_pa");
    $colunas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $tem_usuario_vinculo = false;
    $tem_criado_por = false;
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($colunas as $coluna) {
        echo "<tr>";
        echo "<td>{$coluna['Field']}</td>";
        echo "<td>{$coluna['Type']}</td>";
        echo "<td>{$coluna['Null']}</td>";
        echo "<td>{$coluna['Key']}</td>";
        echo "<td>{$coluna['Default']}</td>";
        echo "</tr>";
        
        if ($coluna['Field'] == 'usuario_vinculo') $tem_usuario_vinculo = true;
        if ($coluna['Field'] == 'criado_por') $tem_criado_por = true;
    }
    echo "</table>";
    
    // Verificar constraints
    echo "<h4>🔗 Constraints Atuais:</h4>";
    $stmt = $pdo->query("
        SELECT 
            CONSTRAINT_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'acd_usuario_pa' 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    $constraints = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($constraints)) {
        echo "<p>Nenhuma constraint encontrada.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Nome</th><th>Coluna</th><th>Tabela Referenciada</th><th>Coluna Referenciada</th></tr>";
        foreach ($constraints as $constraint) {
            echo "<tr>";
            echo "<td>{$constraint['CONSTRAINT_NAME']}</td>";
            echo "<td>{$constraint['COLUMN_NAME']}</td>";
            echo "<td>{$constraint['REFERENCED_TABLE_NAME']}</td>";
            echo "<td>{$constraint['REFERENCED_COLUMN_NAME']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>🔧 Aplicando Correção</h3>";
    
    if ($tem_usuario_vinculo && !$tem_criado_por) {
        echo "<p>🔄 Detectada estrutura antiga com campo 'usuario_vinculo'.</p>";
        echo "<p>⚠️ <strong>RECOMENDAÇÃO:</strong> Execute o script de migração completa: <a href='migrar_tabela_usuario_pa.php'>migrar_tabela_usuario_pa.php</a></p>";
        
        // Solução temporária: remover a constraint problemática
        echo "<p>🩹 Aplicando correção temporária...</p>";
        
        try {
            // Remover constraint problemática
            $pdo->exec("ALTER TABLE acd_usuario_pa DROP FOREIGN KEY acd_usuario_pa_ibfk_3");
            echo "<p>✅ Constraint problemática removida.</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ Constraint já foi removida ou não existe: " . $e->getMessage() . "</p>";
        }
        
        // Verificar dados órfãos
        echo "<p>🔍 Verificando dados órfãos...</p>";
        $stmt = $pdo->query("
            SELECT COUNT(*) as total
            FROM acd_usuario_pa 
            WHERE usuario_vinculo IS NOT NULL 
            AND usuario_vinculo NOT IN (SELECT id FROM usuarios)
        ");
        $orfaos = $stmt->fetchColumn();
        
        if ($orfaos > 0) {
            echo "<p>⚠️ Encontrados $orfaos registros com usuario_vinculo inválido.</p>";
            echo "<p>🔧 Corrigindo dados órfãos...</p>";
            
            // Atualizar registros órfãos para usar ID 1 (admin)
            $stmt = $pdo->prepare("
                UPDATE acd_usuario_pa 
                SET usuario_vinculo = 1 
                WHERE usuario_vinculo IS NOT NULL 
                AND usuario_vinculo NOT IN (SELECT id FROM usuarios)
            ");
            $stmt->execute();
            $corrigidos = $stmt->rowCount();
            echo "<p>✅ $corrigidos registros corrigidos (usuario_vinculo definido como 1).</p>";
        } else {
            echo "<p>✅ Nenhum dado órfão encontrado.</p>";
        }
        
        // Recriar constraint com dados válidos
        echo "<p>🔗 Recriando constraint...</p>";
        try {
            $pdo->exec("
                ALTER TABLE acd_usuario_pa 
                ADD CONSTRAINT fk_acd_usuario_pa_vinculo 
                FOREIGN KEY (usuario_vinculo) REFERENCES usuarios(id)
            ");
            echo "<p>✅ Constraint recriada com sucesso.</p>";
        } catch (Exception $e) {
            echo "<p>❌ Erro ao recriar constraint: " . $e->getMessage() . "</p>";
        }
        
    } elseif ($tem_criado_por) {
        echo "<p>✅ Estrutura nova detectada. Tabela já está atualizada!</p>";
    } else {
        echo "<p>❓ Estrutura não reconhecida.</p>";
    }
    
    echo "<h3>📊 Verificação Final</h3>";
    
    // Testar criação de vínculo
    echo "<p>🧪 Testando criação de vínculo...</p>";
    
    // Buscar primeiro usuário e primeiro PA para teste
    $stmt = $pdo->query("SELECT id FROM usuarios LIMIT 1");
    $usuario_teste = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT id FROM pontos_atendimento LIMIT 1");
    $pa_teste = $stmt->fetchColumn();
    
    if ($usuario_teste && $pa_teste) {
        try {
            // Tentar inserir um registro de teste
            if ($tem_criado_por) {
                $stmt = $pdo->prepare("
                    INSERT INTO acd_usuario_pa (usuario_id, pa_id, data_inicio, criado_por, observacoes) 
                    VALUES (?, ?, CURDATE(), ?, 'Teste de constraint')
                ");
                $stmt->execute([$usuario_teste, $pa_teste, $_SESSION['user_id']]);
            } else {
                $stmt = $pdo->prepare("
                    INSERT INTO acd_usuario_pa (usuario_id, pa_id, data_vinculo, usuario_vinculo) 
                    VALUES (?, ?, NOW(), ?)
                ");
                $stmt->execute([$usuario_teste, $pa_teste, $_SESSION['user_id']]);
            }
            
            $teste_id = $pdo->lastInsertId();
            echo "<p>✅ Teste de inserção bem-sucedido (ID: $teste_id).</p>";
            
            // Remover registro de teste
            $pdo->prepare("DELETE FROM acd_usuario_pa WHERE id = ?")->execute([$teste_id]);
            echo "<p>🗑️ Registro de teste removido.</p>";
            
        } catch (Exception $e) {
            echo "<p>❌ Erro no teste: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>⚠️ Não foi possível realizar teste (sem usuários ou PAs disponíveis).</p>";
    }
    
    echo "<h3>✅ Correção Concluída</h3>";
    echo "<p>A tabela acd_usuario_pa deve estar funcionando corretamente agora.</p>";
    
    if ($tem_usuario_vinculo && !$tem_criado_por) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>⚠️ Recomendação Importante</h4>";
        echo "<p>Sua tabela ainda usa a estrutura antiga. Para ter acesso completo ao sistema de histórico de vínculos, execute a migração completa:</p>";
        echo "<p><a href='migrar_tabela_usuario_pa.php' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🔄 Executar Migração Completa</a></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Erro durante a correção:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "<br><a href='gerenciar_vinculos.php'>← Voltar para Gerenciar Vínculos</a>";
?>
