<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    // Validar se ID foi informado
    if (!isset($_GET['id']) || empty($_GET['id'])) {
        throw new Exception('ID do alvará não informado');
    }

    $id = intval($_GET['id']);

    // Buscar dados do alvará
    $stmt = $pdo->prepare("
        SELECT *,
               DATE_FORMAT(data_recebimento, '%Y-%m-%d') as data_recebimento_formatada
        FROM cbp_alvaras
        WHERE id = ?
    ");
    $stmt->execute([$id]);
    $alvara = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$alvara) {
        throw new Exception('Alvará não encontrado');
    }

    echo json_encode([
        'success' => true,
        'data' => $alvara
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao buscar alvará: ' . $e->getMessage()
    ]);
    error_log($e->getMessage());
} 