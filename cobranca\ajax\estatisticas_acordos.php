<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

// Buscar estatísticas
$sql = "
    SELECT
        COUNT(*) as total,
        SUM(CASE 
            WHEN EXISTS (
                SELECT 1
                FROM cbp_parcelas_acordo pa
                WHERE pa.acordo_id = a.id
                AND pa.status = 'PENDENTE'
            )
            AND NOT EXISTS (
                SELECT 1
                FROM cbp_parcelas_acordo pa
                WHERE pa.acordo_id = a.id
                AND pa.status = 'PENDENTE'
                AND pa.data_vencimento < CURRENT_DATE
            )
            AND a.ativo = 1 THEN 1 ELSE 0 END
        ) as vigentes,
        SUM(CASE 
            WHEN NOT EXISTS (
                SELECT 1
                FROM cbp_parcelas_acordo pa
                WHERE pa.acordo_id = a.id
                AND pa.status != 'PAGO'
            )
            AND a.ativo = 1 THEN 1 ELSE 0 END
        ) as quitados,
        SUM(CASE 
            WHEN EXISTS (
                SELECT 1
                FROM cbp_parcelas_acordo pa
                WHERE pa.acordo_id = a.id
                AND pa.status = 'PENDENTE'
                AND pa.data_vencimento < CURRENT_DATE
            )
            AND a.ativo = 1 THEN 1 ELSE 0 END
        ) as inadimplentes
    FROM cbp_acordos a
";

$stmt = $pdo->query($sql);
$estatisticas = $stmt->fetch(PDO::FETCH_ASSOC);

// Retornar JSON
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'total' => $estatisticas['total'],
    'vigentes' => $estatisticas['vigentes'],
    'quitados' => $estatisticas['quitados'],
    'inadimplentes' => $estatisticas['inadimplentes']
]); 