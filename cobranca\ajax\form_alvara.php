<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

$processo_id = isset($_GET['processo_id']) ? intval($_GET['processo_id']) : 0;

// Buscar informações do processo
$stmt = $pdo->prepare("
    SELECT p.*, m.nome as modalidade_nome 
    FROM cbp_processos_judiciais p
    LEFT JOIN cbp_modalidades_processo m ON p.modalidade_id = m.id
    WHERE p.id = ?
");
$stmt->execute([$processo_id]);
$processo = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$processo) {
    echo '<div class="alert alert-danger">Processo não encontrado.</div>';
    exit;
}

// Buscar alvarás existentes
$stmt = $pdo->prepare("
    SELECT * FROM cbp_alvaras 
    WHERE processo_id = ? 
    ORDER BY data_recebimento DESC
");
$stmt->execute([$processo_id]);
$alvaras = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<form id="form-alvara" class="needs-validation" novalidate>
    <input type="hidden" name="processo_id" value="<?php echo $processo_id; ?>">
    
    <!-- Informações do Processo -->
    <div class="card mb-3">
        <div class="card-header">
            <h5 class="mb-0">Informações do Processo</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Nome:</strong> <?php echo $processo['nome']; ?></p>
                    <p><strong>CPF/CNPJ:</strong> <?php echo $processo['cpf_cnpj']; ?></p>
                    <p><strong>Número do Processo:</strong> <?php echo $processo['numero_processo']; ?></p>
                </div>
                <div class="col-md-6">
                    <p><strong>Modalidade:</strong> <?php echo $processo['modalidade_nome']; ?></p>
                    <p><strong>Número do Contrato:</strong> <?php echo $processo['numero_contrato']; ?></p>
                    <p><strong>Valor Ajuizado:</strong> R$ <?php echo number_format($processo['valor_ajuizado'], 2, ',', '.'); ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Novo Alvará -->
    <div class="card mb-3">
        <div class="card-header">
            <h5 class="mb-0">Novo Alvará</h5>
        </div>
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-6">
                    <label class="form-label">Data do Recebimento</label>
                    <input type="date" name="data_recebimento" class="form-control" required>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Valor do Alvará</label>
                    <input type="text" name="valor" class="form-control money" required>
                </div>
                <div class="col-12">
                    <label class="form-label">Observações</label>
                    <textarea name="observacoes" class="form-control" rows="3"></textarea>
                </div>
            </div>
        </div>
    </div>

    <!-- Lista de Alvarás -->
    <?php if (!empty($alvaras)): ?>
    <div class="card mb-3">
        <div class="card-header">
            <h5 class="mb-0">Alvarás Recebidos</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Data</th>
                            <th>Valor</th>
                            <th>Observações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($alvaras as $alvara): ?>
                        <tr>
                            <td><?php echo date('d/m/Y', strtotime($alvara['data_recebimento'])); ?></td>
                            <td>R$ <?php echo number_format($alvara['valor'], 2, ',', '.'); ?></td>
                            <td><?php echo $alvara['observacoes']; ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="d-flex justify-content-end gap-2">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
        <button type="submit" class="btn btn-primary">Salvar Alvará</button>
    </div>
</form>

<script>
$(document).ready(function() {
    // Máscara para valores monetários
    $('.money').inputmask({
        alias: 'currency',
        radixPoint: ',',
        groupSeparator: '.',
        allowMinus: false,
        prefix: 'R$ ',
        digits: 2,
        digitsOptional: false,
        rightAlign: false,
        unmaskAsNumber: true
    });

    // Submeter formulário
    $('#form-alvara').on('submit', function(e) {
        e.preventDefault();

        if (!this.checkValidity()) {
            e.stopPropagation();
            $(this).addClass('was-validated');
            return;
        }

        const form = $(this);
        const submitButton = form.find('button[type="submit"]');
        const originalText = submitButton.html();

        // Obter o valor sem máscara
        const valorInput = form.find('[name="valor"]');
        const valorOriginal = valorInput.val();
        const valorLimpo = valorOriginal.replace(/[^\d,]/g, '').replace(',', '.');

        // Criar FormData com os valores corretos
        const formData = new FormData();
        formData.append('processo_id', form.find('[name="processo_id"]').val());
        formData.append('data_recebimento', form.find('[name="data_recebimento"]').val());
        formData.append('valor', valorLimpo);
        formData.append('observacoes', form.find('[name="observacoes"]').val());

        // Desabilitar botão e mostrar loading
        submitButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Aguarde...');

        $.ajax({
            url: 'ajax/salvar_alvara.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Sucesso!',
                        text: response.message
                    }).then(() => {
                        // Fechar o modal e recarregar a página
                        $('#modalAlvara').modal('hide');
                        location.reload();
                    });
                } else {
                    Swal.fire('Erro!', response.message || 'Erro ao registrar alvará.', 'error');
                }
            },
            error: function(xhr) {
                let errorMessage = 'Erro ao registrar alvará.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                Swal.fire('Erro!', errorMessage, 'error');
            },
            complete: function() {
                // Restaurar botão
                submitButton.prop('disabled', false).html(originalText);
            }
        });
    });
});
</script> 
</script> 
