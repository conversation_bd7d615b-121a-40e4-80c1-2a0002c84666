-- C<PERSON>r tabela de relacionamento entre usuários e pontos de atendimento
CREATE TABLE IF NOT EXISTS acd_usuario_pa (
    id INT AUTO_INCREMENT PRIMARY KEY,
    usuario_id INT NOT NULL,
    pa_id INT NOT NULL,
    data_vinculo DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    usuario_vinculo INT NOT NULL,
    status TINYINT(1) NOT NULL DEFAULT 1,
    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
    FOREIGN KEY (pa_id) REFERENCES pontos_atendimento(id) ON DELETE CASCADE,
    FOREIGN KEY (usuario_vinculo) REFERENCES usuarios(id),
    UNIQUE KEY unique_usuario_pa (usuario_id, pa_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- <PERSON><PERSON>r índices para melhor performance
CREATE INDEX idx_acd_usuario_pa_usuario ON acd_usuario_pa(usuario_id);
CREATE INDEX idx_acd_usuario_pa_pa ON acd_usuario_pa(pa_id);
CREATE INDEX idx_acd_usuario_pa_status ON acd_usuario_pa(status); 