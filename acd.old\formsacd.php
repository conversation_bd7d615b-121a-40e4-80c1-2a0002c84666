<?php
require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';
session_start();

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se o usuário tem permissão para acessar esta página
if (!checkACDPermission($_SESSION['user_id'], 'formsacd.php')) {
    header('Location: unauthorized.php');
    exit;
}

// Buscar dados do usuário logado
$user_id = $_SESSION['user_id'];
$stmt = $pdo->prepare('SELECT u.id, u.nome_completo, u.email, u.username, pa.numero as pa_numero 
    FROM usuarios u
    LEFT JOIN usuario_pa upa ON u.id = upa.usuario_id
    LEFT JOIN pontos_atendimento pa ON upa.pa_id = pa.id
    WHERE u.id = ?');
$stmt->execute([$user_id]);
$usuario = $stmt->fetch(PDO::FETCH_ASSOC);

// Função para buscar usuários do PA via API
function buscarTodosUsuarios() {
    $apiFields = [
        'api_user' => 'UFL7GXZ14LU9NOR',
        'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
        'api_module' => 'Usuarios',
        'api_action' => 'listarUsuarios'
    ];

    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => http_build_query($apiFields),
    ));
    
    $response = curl_exec($curl);
    
    if (curl_errno($curl)) {
        error_log('Erro na API: ' . curl_error($curl));
        curl_close($curl);
        return [];
    }
    
    curl_close($curl);
    
    $usuarios = json_decode($response, true);
    
    if (!is_array($usuarios)) {
        error_log('Resposta da API inválida: ' . $response);
        return [];
    }
    
    $usuarios_ativos = [];
    
    foreach ($usuarios as $user) {
        if (isset($user['status']) && $user['status'] == 1 && 
            isset($user['bloqueado']) && $user['bloqueado'] == 0) {
            $usuarios_ativos[] = [
                'id' => $user['id'] ?? '',
                'nome' => $user['nome'] ?? '',
                'pa' => $user['nomeAgencia'] ?? '',
                'login' => $user['loginAD'] ?? $user['loginSISBR'] ?? ''
            ];
        }
    }
    
    // Ordenar por nome
    usort($usuarios_ativos, function($a, $b) {
        return strcmp($a['nome'], $b['nome']);
    });
    
    return $usuarios_ativos;
}

// Função para buscar CPF/CNPJ na base
function buscarDocumento($documento) {
    global $pdo;
    
    $documento = preg_replace('/[^0-9]/', '', $documento);
    
    if (strlen($documento) < 11) {
        return [];
    }
    
    try {
        $sql = "SELECT DISTINCT 
                    CASE 
                        WHEN LENGTH(cpf_cnpj) = 11 THEN cpf_cnpj 
                        WHEN LENGTH(cpf_cnpj) = 14 THEN cpf_cnpj 
                        ELSE NULL 
                    END as documento,
                    nome_cliente as nome 
                FROM parecer_base 
                WHERE cpf_cnpj LIKE ? 
                AND LENGTH(cpf_cnpj) >= 11
                LIMIT 5";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$documento . '%']);
        $resultados = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Formatar os documentos encontrados
        foreach ($resultados as &$resultado) {
            $doc = $resultado['documento'];
            if (strlen($doc) === 11) {
                $resultado['documento_formatado'] = substr($doc, 0, 3) . '.' . 
                                                   substr($doc, 3, 3) . '.' . 
                                                   substr($doc, 6, 3) . '-' . 
                                                   substr($doc, 9, 2);
            } else if (strlen($doc) === 14) {
                $resultado['documento_formatado'] = substr($doc, 0, 2) . '.' . 
                                                   substr($doc, 2, 3) . '.' . 
                                                   substr($doc, 5, 3) . '/' . 
                                                   substr($doc, 8, 4) . '-' . 
                                                   substr($doc, 12, 2);
            }
        }
        
        return $resultados;
    } catch (PDOException $e) {
        error_log('Erro ao buscar documento: ' . $e->getMessage());
        return [];
    }
}

// Processar o formulário quando enviado
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $documento = limparCPF($_POST['documento']); // A função limparCPF já remove caracteres não numéricos
    $nome = $_POST['nome'];
    $mesa = $_POST['mesa'];
    $usuario_pa = $_POST['usuario_pa'];
    $pa = $_POST['pa'];
    $acao = $_POST['acao'];
    $motivo_devolucao = $acao === 'devolver' ? $_POST['motivo_devolucao'] : null;
    
    $sql = "INSERT INTO acd_formularios (usuario_id, documento, nome, mesa, usuario_pa, pa, acao, motivo_devolucao, data_criacao) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";
    
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$user_id, $documento, $nome, $mesa, $usuario_pa, $pa, $acao, $motivo_devolucao]);
        
        // Registrar log da ação
        $stmt = $pdo->prepare("
            INSERT INTO logs (usuario_id, acao, detalhes, data_hora) 
            VALUES (?, 'cadastro_acd', ?, NOW())
        ");
        $detalhes = "Cadastro de formulário ACD - Documento: $documento, Nome: $nome, Mesa: $mesa, Ação: $acao";
        $stmt->execute([$user_id, $detalhes]);
        
        $mensagem = "Formulário salvo com sucesso!";
        $tipo_mensagem = "success";
    } catch (PDOException $e) {
        $mensagem = "Erro ao salvar formulário: " . $e->getMessage();
        $tipo_mensagem = "danger";
    }
}

// Buscar todos os usuários ativos
$usuarios = buscarTodosUsuarios();
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="../assets/images/Sicoob.ico">
    <title>ACD - Formulário</title>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <style>
        :root {
            /* Cores principais do Sicoob */
            --sicoob-turquesa: #00AE9D;
            --sicoob-verde-escuro: #003641;
            --sicoob-branco: #FFFFFF;
            /* Cores de apoio */
            --sicoob-verde-claro: #C9D200;
            --sicoob-verde-medio: #7DB61C;
            --sicoob-roxo: #49479D;
            /* Cores de texto */
            --text-light: var(--sicoob-branco);
            --text-dark: var(--sicoob-verde-escuro);
            --text-muted: #666666;
            /* Cores de fundo */
            --bg-light: #f5f5f5;
            --bg-white: var(--sicoob-branco);
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: var(--bg-light);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            color: var(--text-dark);
        }

        .wrapper {
            display: flex;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
            margin-left: 250px;
            padding: 2rem;
            transition: all 0.3s ease;
        }

        .form-container {
            background: var(--bg-white);
            border-radius: 8px;
            padding: 2rem;
            box-shadow: 0 2px 8px rgba(0, 54, 65, 0.1);
            border-top: 4px solid var(--sicoob-turquesa);
        }

        .form-title {
            color: var(--sicoob-verde-escuro);
            margin-bottom: 2rem;
            font-weight: 600;
            position: relative;
            padding-bottom: 1rem;
        }

        .form-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: var(--sicoob-turquesa);
        }

        .form-label {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 0.5rem 0.75rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--sicoob-turquesa);
            box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.25);
        }

        .btn-primary {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
            padding: 0.5rem 1.5rem;
            font-weight: 600;
            transition: all 0.2s ease-in-out;
        }

        .btn-primary:hover {
            background-color: var(--sicoob-verde-escuro);
            border-color: var(--sicoob-verde-escuro);
        }

        .alert {
            border-radius: 4px;
            border: none;
            padding: 1rem;
        }

        .alert-success {
            background-color: rgba(125, 182, 28, 0.1);
            color: var(--sicoob-verde-medio);
        }

        .alert-danger {
            background-color: rgba(201, 210, 0, 0.1);
            color: var(--sicoob-verde-claro);
        }

        .select2-container--default .select2-selection--single {
            height: 38px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }

        .select2-container--default .select2-selection--single:focus {
            border-color: var(--sicoob-turquesa);
            box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.25);
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 38px;
            color: var(--text-dark);
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
        }

        .select2-container--default .select2-results__option--highlighted[aria-selected] {
            background-color: var(--sicoob-turquesa);
        }

        .documento-search-results {
            position: absolute;
            width: 100%;
            max-height: 300px;
            overflow-y: auto;
            background: var(--bg-white);
            border: 1px solid #ced4da;
            border-radius: 4px;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 6px rgba(0, 54, 65, 0.1);
        }

        .documento-result-item {
            padding: 12px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
            transition: background-color 0.2s ease;
        }

        .documento-result-item:hover {
            background-color: rgba(0, 174, 157, 0.05);
        }

        .documento-result-item .documento {
            font-weight: 600;
            color: var(--sicoob-verde-escuro);
            margin-bottom: 4px;
        }

        .documento-result-item .nome {
            color: var(--text-muted);
            font-size: 0.9em;
        }

        .documento-result-item .tipo {
            background: #e9ecef;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 600;
            margin-left: 10px;
        }

        .documento-result-item .tipo.cpf {
            background: rgba(0, 174, 157, 0.1);
            color: var(--sicoob-turquesa);
        }

        .documento-result-item .tipo.cnpj {
            background: rgba(125, 182, 28, 0.1);
            color: var(--sicoob-verde-medio);
        }

        .loading-indicator {
            padding: 12px;
            text-align: center;
            color: var(--text-muted);
            font-style: italic;
        }

        .form-check-input:checked {
            background-color: var(--sicoob-turquesa);
            border-color: var(--sicoob-turquesa);
        }

        .form-check-input:focus {
            border-color: var(--sicoob-turquesa);
            box-shadow: 0 0 0 0.2rem rgba(0, 174, 157, 0.25);
        }

        /* Estilo para campos readonly */
        .form-control[readonly] {
            background-color: #f8f9fa;
            border-color: #ced4da;
        }

        /* Estilo para o grupo de radio buttons */
        .form-check-inline {
            margin-right: 1.5rem;
        }

        .form-check-inline .form-check-input {
            margin-right: 0.5rem;
        }

        /* Estilo para o textarea */
        textarea.form-control {
            min-height: 100px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'components/sidebar.php'; ?>

        <div class="main-content">
            <div class="form-container">
                <h2 class="form-title">Formulário Análise de Crédito</h2>

                <?php if (isset($mensagem)): ?>
                    <div class="alert alert-<?php echo $tipo_mensagem; ?> alert-dismissible fade show" role="alert">
                        <i class="fas <?php echo $tipo_mensagem === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> me-2"></i>
                        <?php echo $mensagem; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <form method="POST" id="sacdForm">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="documento" class="form-label">CPF/CNPJ</label>
                                <div class="position-relative">
                                    <input type="text" class="form-control" id="documento" name="documento" required>
                                    <div id="documentoSearchResults" class="documento-search-results"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="nome" class="form-label">Nome</label>
                                <input type="text" class="form-control" id="nome" name="nome" required>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="mesa" class="form-label">Mesa</label>
                                <select class="form-select" id="mesa" name="mesa" required>
                                    <option value="">Selecione...</option>
                                    <option value="limite">Limite</option>
                                    <option value="emprestimo">Empréstimo</option>
                                    <option value="rural">Rural</option>
                                    <option value="cartao">Cartão</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="usuario_pa" class="form-label">Usuário do PA</label>
                                <select class="form-select" id="usuario_pa" name="usuario_pa" required>
                                    <option value="">Selecione...</option>
                                    <?php foreach ($usuarios as $user): ?>
                                        <option value="<?php echo $user['id']; ?>" 
                                                data-pa="<?php echo $user['pa']; ?>"
                                                data-login="<?php echo $user['login']; ?>">
                                            <?php echo $user['nome']; ?> (<?php echo $user['pa']; ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="pa" class="form-label">PA</label>
                                <input type="text" class="form-control" id="pa" name="pa" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label d-block">Ação</label>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="acao" id="acaoSubmeter" value="submeter" checked>
                                    <label class="form-check-label" for="acaoSubmeter">Submeter</label>
                                </div>
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" type="radio" name="acao" id="acaoDevolver" value="devolver">
                                    <label class="form-check-label" for="acaoDevolver">Devolver</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-3" id="motivoDevolucaoGroup">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="motivo_devolucao" class="form-label">Motivo da Devolução</label>
                                <textarea class="form-control" id="motivo_devolucao" name="motivo_devolucao" rows="3"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">Salvar</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.mask/1.14.16/jquery.mask.min.js"></script>
    <script>
        $(document).ready(function() {
            let searchTimeout;
            let isSearching = false;

            // Máscara dinâmica para CPF/CNPJ
            function aplicarMascara(input) {
                let value = $(input).val().replace(/\D/g, '');
                if (value.length <= 11) {
                    $(input).mask('000.000.000-00');
                } else {
                    $(input).mask('00.000.000/0000-00');
                }
            }

            // Aplicar máscara inicial
            aplicarMascara('#documento');

            // Atualizar máscara ao digitar
            $('#documento').on('input', function() {
                aplicarMascara(this);
                
                clearTimeout(searchTimeout);
                const documento = $(this).val().replace(/\D/g, '');
                
                if (documento.length >= 3) {
                    const results = $('#documentoSearchResults');
                    results.html('<div class="loading-indicator">Buscando...</div>').show();
                    
                    searchTimeout = setTimeout(function() {
                        if (!isSearching) {
                            isSearching = true;
                            $.ajax({
                                url: 'buscar_documento.php',
                                method: 'POST',
                                data: { documento: documento },
                                success: function(response) {
                                    results.empty();
                                    
                                    if (response.error) {
                                        console.error('Erro na busca:', response.error);
                                        results.html('<div class="loading-indicator text-danger">Erro: ' + response.error + '</div>');
                                        return;
                                    }
                                    
                                    if (response.length > 0) {
                                        response.forEach(function(item) {
                                            const tipoClass = item.tipo.toLowerCase();
                                            results.append(
                                                $('<div>')
                                                    .addClass('documento-result-item')
                                                    .html(`
                                                        <div class="info">
                                                            <div class="documento">${item.documento_formatado}</div>
                                                            <div class="nome">${item.nome}</div>
                                                        </div>
                                                        <span class="tipo ${tipoClass}">${item.tipo}</span>
                                                    `)
                                                    .data('documento', item.documento)
                                                    .data('nome', item.nome)
                                                    .data('tipo', item.tipo)
                                            );
                                        });
                                    } else {
                                        results.html('<div class="loading-indicator">Nenhum resultado encontrado</div>');
                                    }
                                    results.show();
                                },
                                error: function(xhr, status, error) {
                                    console.error('Erro na requisição:', status, error);
                                    console.error('Resposta:', xhr.responseText);
                                    results.html('<div class="loading-indicator text-danger">Erro ao buscar resultados. Detalhes no console.</div>');
                                },
                                complete: function() {
                                    isSearching = false;
                                }
                            });
                        }
                    }, 300);
                } else {
                    $('#documentoSearchResults').hide();
                }
            });

            // Preencher campos ao selecionar resultado
            $(document).on('click', '.documento-result-item', function() {
                const documento = $(this).data('documento');
                const nome = $(this).data('nome');
                const tipo = $(this).data('tipo');
                
                // Aplicar máscara apropriada
                if (tipo === 'CPF') {
                    $('#documento').mask('000.000.000-00');
                } else {
                    $('#documento').mask('00.000.000/0000-00');
                }
                
                $('#documento').val(documento);
                $('#nome').val(nome);
                $('#documentoSearchResults').hide();
            });

            // Esconder resultados ao clicar fora
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#documento, #documentoSearchResults').length) {
                    $('#documentoSearchResults').hide();
                }
            });

            // Inicializar Select2 para usuário do PA
            $('#usuario_pa').select2({
                theme: 'bootstrap-5',
                width: '100%',
                placeholder: 'Selecione um usuário...',
                allowClear: true,
                language: {
                    noResults: function() {
                        return "Nenhum usuário encontrado";
                    }
                }
            });

            // Atualizar PA quando selecionar usuário
            $('#usuario_pa').on('change', function() {
                const pa = $(this).find(':selected').data('pa');
                const login = $(this).find(':selected').data('login');
                $('#pa').val(pa);
            });

            // Mostrar/ocultar campo de motivo de devolução
            $('input[name="acao"]').on('change', function() {
                if ($(this).val() === 'devolver') {
                    $('#motivoDevolucaoGroup').show();
                    $('#motivo_devolucao').prop('required', true);
                } else {
                    $('#motivoDevolucaoGroup').hide();
                    $('#motivo_devolucao').prop('required', false);
                }
            });
        });
    </script>
</body>
</html> 