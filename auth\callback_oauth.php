<?php
require_once __DIR__ . '/../config/msgraph_oauth.php';
require_once __DIR__ . '/../vendor/autoload.php';

use GuzzleHttp\Client;

function get_redirect_uri() {
    $host = $_SERVER['HTTP_HOST'];
    $path = OAUTH_CALLBACK_PATH;
    if ($host === 'localhost') {
        $protocol = 'http';
    } else {
        $protocol = 'https';
    }
    return $protocol . '://' . $host . $path;
}

function msgraph_save_refresh_token($refresh_token) {
    $file = __DIR__ . '/../config/msgraph_refresh_token.txt';
    file_put_contents($file, $refresh_token, LOCK_EX);
}

if (!isset($_GET['code'])) {
    echo 'Código de autorização não encontrado.';
    exit;
}

$code = $_GET['code'];
$redirect_uri = get_redirect_uri();
$token_url = 'https://login.microsoftonline.com/' . OAUTH_TENANT_ID . '/oauth2/v2.0/token';

$client = new Client();
try {
    $response = $client->post($token_url, [
        'form_params' => [
            'client_id' => OAUTH_CLIENT_ID,
            'scope' => OAUTH_SCOPES,
            'code' => $code,
            'redirect_uri' => $redirect_uri,
            'grant_type' => 'authorization_code',
            'client_secret' => OAUTH_CLIENT_SECRET,
        ]
    ]);
    $data = json_decode($response->getBody(), true);
    if (isset($data['refresh_token'])) {
        msgraph_save_refresh_token($data['refresh_token']);
        echo '<h2>Autorização concluída com sucesso!</h2><p>Refresh token salvo com sucesso. O sistema já pode enviar e-mails via Microsoft Graph.</p>';
    } else {
        echo '<h2>Erro ao obter refresh token.</h2><pre>' . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT)) . '</pre>';
    }
} catch (Exception $e) {
    echo '<h2>Erro ao trocar o code por token:</h2><pre>' . htmlspecialchars($e->getMessage()) . '</pre>';
} 