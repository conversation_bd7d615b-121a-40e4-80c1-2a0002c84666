<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    // Validar se recebeu o termo de busca
    if (!isset($_GET['search'])) {
        throw new Exception('Termo de busca não fornecido');
    }

    $search = trim($_GET['search']);

    // Validar tamanho mínimo do termo de busca
    if (strlen($search) < 3) {
        throw new Exception('Digite pelo menos 3 caracteres para buscar');
    }

    // Preparar o termo de busca para LIKE
    $search_like = "%{$search}%";

    // Buscar associados que correspondem ao termo de busca
        $stmt = $pdo->prepare("
            SELECT 
                a.id,
                a.nome,
                a.cpf_cnpj,
            a.pa_id,
                pa.nome as pa_nome,
            pa.numero as pa_numero,
            CASE 
                WHEN LENGTH(a.cpf_cnpj) = 11 THEN 
                    CONCAT(
                        SUBSTRING(a.cpf_cnpj, 1, 3), '.',
                        SUBSTRING(a.cpf_cnpj, 4, 3), '.',
                        SUBSTRING(a.cpf_cnpj, 7, 3), '-',
                        SUBSTRING(a.cpf_cnpj, 10, 2)
                    )
                ELSE 
                    CONCAT(
                        SUBSTRING(a.cpf_cnpj, 1, 2), '.',
                        SUBSTRING(a.cpf_cnpj, 3, 3), '.',
                        SUBSTRING(a.cpf_cnpj, 6, 3), '/',
                        SUBSTRING(a.cpf_cnpj, 9, 4), '-',
                        SUBSTRING(a.cpf_cnpj, 13, 2)
                    )
            END as cpf_cnpj_formatado
            FROM cbp_associados a
            LEFT JOIN pontos_atendimento pa ON a.pa_id = pa.id
        WHERE 
            LOWER(a.nome) LIKE LOWER(:nome)
            OR a.nome LIKE :nome_exato
            OR REPLACE(REPLACE(REPLACE(a.cpf_cnpj, '.', ''), '-', ''), '/', '') LIKE :documento
            OR a.cpf_cnpj LIKE :documento_exato
        ORDER BY 
            CASE 
                WHEN LOWER(a.nome) = LOWER(:search_exact) THEN 1
                WHEN LOWER(a.nome) LIKE LOWER(CONCAT(:search_exact, '%')) THEN 2
                WHEN LOWER(a.nome) LIKE LOWER(CONCAT('%', :search_exact, '%')) THEN 3
                ELSE 4
            END,
            a.nome
            LIMIT 10
        ");
        
    $stmt->execute([
        'nome' => $search_like,
        'nome_exato' => $search,
        'documento' => $search_like,
        'documento_exato' => $search,
        'search_exact' => $search
    ]);
    
    $associados = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
        'results' => $associados
        ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 