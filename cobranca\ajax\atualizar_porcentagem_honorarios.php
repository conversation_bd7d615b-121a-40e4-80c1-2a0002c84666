<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

// Verificar se a requisição é POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método não permitido.']);
    exit;
}

// Validar o parâmetro
if (!isset($_POST['porcentagem']) || $_POST['porcentagem'] === '') {
    echo json_encode(['success' => false, 'message' => 'Porcentagem não informada.']);
    exit;
}

// Converter e validar a porcentagem
$porcentagem = str_replace(['%', ','], ['', '.'], $_POST['porcentagem']);
if (!is_numeric($porcentagem) || $porcentagem < 0) {
    echo json_encode(['success' => false, 'message' => 'Porcentagem inválida.']);
    exit;
}

try {
    // Verificar se já existe configuração
    $stmt = $pdo->prepare("SELECT id FROM cbp_configuracoes WHERE chave = 'porcentagem_padrao_honorarios'");
    $stmt->execute();
    $config_id = $stmt->fetchColumn();
    
    if ($config_id) {
        // Atualizar configuração existente
        $stmt = $pdo->prepare("UPDATE cbp_configuracoes SET valor = ?, updated_at = NOW() WHERE chave = 'porcentagem_padrao_honorarios'");
        $stmt->execute([$porcentagem]);
    } else {
        // Criar nova configuração
        $stmt = $pdo->prepare("INSERT INTO cbp_configuracoes (chave, valor, descricao) VALUES ('porcentagem_padrao_honorarios', ?, 'Porcentagem padrão aplicada aos honorários de advogados')");
        $stmt->execute([$porcentagem]);
    }
    
    echo json_encode(['success' => true, 'message' => 'Porcentagem atualizada com sucesso.']);
} catch (PDOException $e) {
    error_log('Erro ao atualizar porcentagem de honorários: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erro ao processar a requisição.']);
} 