<?php
/**
 * Script para reverter para a estrutura antiga da tabela acd_usuario_pa
 * Onde usuario_id referencia usuários locais do banco, não da API
 */

require_once '../config/database.php';
require_once '../config/funcoes.php';
require_once 'check_acd_permission.php';

session_start();
if (!isset($_SESSION['user_id'])) {
    die("Acesso negado. Faça login para continuar.");
}

if (!checkACDPermission($_SESSION['user_id'])) {
    die("Acesso negado. Você não tem permissão para acessar o sistema ACD.");
}

if (!checkAdminOrManagerPermission($_SESSION['user_id'])) {
    die("Acesso negado. Apenas administradores e gestores podem executar esta operação.");
}

echo "<h2>🔄 Reverter para Estrutura Antiga</h2>";
echo "<p>Este script reverte a tabela acd_usuario_pa para a estrutura antiga com usuários locais.</p>";

try {
    echo "<h3>📋 Verificando Estrutura Atual</h3>";
    
    // Verificar estrutura atual
    $stmt = $pdo->query("DESCRIBE acd_usuario_pa");
    $colunas = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $campos = array_column($colunas, 'Field');
    
    echo "<p>Campos atuais: " . implode(', ', $campos) . "</p>";
    
    $tem_usuario_api_id = in_array('usuario_api_id', $campos);
    $tem_usuario_id = in_array('usuario_id', $campos);
    
    if (!$tem_usuario_api_id) {
        echo "<p>ℹ️ A tabela já parece estar na estrutura antiga.</p>";
        if ($tem_usuario_id) {
            echo "<p>✅ Campo usuario_id encontrado.</p>";
            echo "<p><a href='aplicar_correcao_estrutura_antiga.php'>🔧 Aplicar funções para estrutura antiga</a></p>";
        }
        exit;
    }
    
    echo "<p>⚠️ Estrutura nova detectada (com usuario_api_id). Iniciando reversão...</p>";
    
    // Verificar dados existentes
    echo "<h3>📊 Verificando Dados Existentes</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) FROM acd_usuario_pa");
    $total_vinculos = $stmt->fetchColumn();
    echo "<p>Total de vínculos existentes: $total_vinculos</p>";
    
    if ($total_vinculos > 0) {
        echo "<p>⚠️ <strong>ATENÇÃO:</strong> Existem dados na tabela. A reversão pode causar perda de dados.</p>";
        
        // Mostrar alguns exemplos
        $stmt = $pdo->query("SELECT * FROM acd_usuario_pa LIMIT 3");
        $exemplos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($exemplos)) {
            echo "<p><strong>Exemplos de dados existentes:</strong></p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            foreach (array_keys($exemplos[0]) as $campo) {
                echo "<th>$campo</th>";
            }
            echo "</tr>";
            
            foreach ($exemplos as $exemplo) {
                echo "<tr>";
                foreach ($exemplo as $valor) {
                    echo "<td>" . htmlspecialchars($valor ?? '') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    echo "<h3>🔄 Opções de Reversão</h3>";
    
    if (isset($_POST['opcao_reversao'])) {
        $opcao = $_POST['opcao_reversao'];

        try {
            if ($opcao === 'backup_e_recriar') {
                echo "<p>📦 Opção: Fazer backup e recriar tabela</p>";
                
                // Fazer backup
                $backup_table = 'acd_usuario_pa_backup_' . date('Y_m_d_H_i_s');
                $pdo->exec("CREATE TABLE $backup_table AS SELECT * FROM acd_usuario_pa");
                echo "<p>✅ Backup criado: $backup_table</p>";
                
                // Remover tabela atual
                $pdo->exec("DROP TABLE acd_usuario_pa");
                echo "<p>🗑️ Tabela atual removida</p>";
                
                // Criar estrutura antiga
                $sql_estrutura_antiga = "
                CREATE TABLE acd_usuario_pa (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    usuario_id INT NOT NULL COMMENT 'ID do usuário local',
                    pa_id INT NOT NULL COMMENT 'ID do PA',
                    data_vinculo DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '1=ativo, 0=inativo',
                    
                    INDEX idx_usuario_id (usuario_id),
                    INDEX idx_pa_id (pa_id),
                    INDEX idx_status (status),
                    
                    FOREIGN KEY (usuario_id) REFERENCES usuarios(id) ON DELETE CASCADE,
                    FOREIGN KEY (pa_id) REFERENCES pontos_atendimento(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                COMMENT='Vínculos entre usuários locais e PAs - Estrutura Antiga'";
                
                $pdo->exec($sql_estrutura_antiga);
                echo "<p>✅ Estrutura antiga criada</p>";
                
            } elseif ($opcao === 'alterar_estrutura') {
                echo "<p>🔧 Opção: Alterar estrutura mantendo dados possíveis</p>";

                // Fazer backup
                $backup_table = 'acd_usuario_pa_backup_' . date('Y_m_d_H_i_s');
                $pdo->exec("CREATE TABLE $backup_table AS SELECT * FROM acd_usuario_pa");
                echo "<p>✅ Backup criado: $backup_table</p>";

                // Adicionar coluna usuario_id
                $pdo->exec("ALTER TABLE acd_usuario_pa ADD COLUMN usuario_id INT NULL AFTER id");
                echo "<p>✅ Coluna usuario_id adicionada</p>";

                // Tentar mapear alguns dados (se possível)
                echo "<p>🔄 Tentando mapear dados existentes...</p>";

                // Aqui você poderia tentar mapear usuario_api_id para usuario_id se tivesse uma relação
                // Por enquanto, vamos apenas marcar como NULL

                // Remover colunas da estrutura nova
                $colunas_remover = ['usuario_api_id', 'data_inicio', 'data_fim', 'criado_por', 'desativado_por', 'criado_em', 'desativado_em', 'observacoes'];

                foreach ($colunas_remover as $coluna) {
                    if (in_array($coluna, $campos)) {
                        try {
                            $pdo->exec("ALTER TABLE acd_usuario_pa DROP COLUMN $coluna");
                            echo "<p>✅ Coluna $coluna removida</p>";
                        } catch (Exception $e) {
                            echo "<p>⚠️ Erro ao remover $coluna: " . $e->getMessage() . "</p>";
                        }
                    }
                }

                // Adicionar data_vinculo se não existir
                if (!in_array('data_vinculo', $campos)) {
                    $pdo->exec("ALTER TABLE acd_usuario_pa ADD COLUMN data_vinculo DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP");
                    echo "<p>✅ Coluna data_vinculo adicionada</p>";
                }

                // Alterar status para TINYINT se necessário
                $pdo->exec("ALTER TABLE acd_usuario_pa MODIFY COLUMN status TINYINT(1) NOT NULL DEFAULT 1");
                echo "<p>✅ Coluna status ajustada</p>";
            }
            
            echo "<h3>✅ Reversão Concluída!</h3>";
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
            echo "<h4>🎉 Sucesso!</h4>";
            echo "<p>A tabela foi revertida para a estrutura antiga:</p>";
            echo "<ul>";
            echo "<li>✅ Estrutura antiga restaurada</li>";
            echo "<li>✅ Backup dos dados criado</li>";
            echo "<li>✅ Pronta para usar com usuários locais</li>";
            echo "</ul>";
            echo "</div>";
            
            echo "<h3>📋 Próximos Passos</h3>";
            echo "<ol>";
            echo "<li><a href='aplicar_correcao_estrutura_antiga.php'>🔧 Aplicar funções para estrutura antiga</a></li>";
            echo "<li><a href='gerenciar_vinculos.php'>🔗 Testar gerenciamento de vínculos</a></li>";
            echo "</ol>";
            
        } catch (Exception $e) {
            echo "<h3>❌ Erro durante a operação:</h3>";
            echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
        }
        
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>⚠️ Escolha uma Opção de Reversão</h4>";
        echo "<p>Como você deseja reverter para a estrutura antiga?</p>";
        echo "</div>";
        
        echo "<form method='POST'>";
        
        echo "<div style='margin: 20px 0;'>";
        echo "<input type='radio' name='opcao_reversao' value='backup_e_recriar' id='opcao1' checked>";
        echo "<label for='opcao1' style='margin-left: 10px;'>";
        echo "<strong>🔄 Backup e Recriar (Recomendado)</strong><br>";
        echo "<small>Faz backup dos dados atuais e recria a tabela com estrutura antiga limpa.</small>";
        echo "</label>";
        echo "</div>";
        
        echo "<div style='margin: 20px 0;'>";
        echo "<input type='radio' name='opcao_reversao' value='alterar_estrutura' id='opcao2'>";
        echo "<label for='opcao2' style='margin-left: 10px;'>";
        echo "<strong>🔧 Alterar Estrutura</strong><br>";
        echo "<small>Tenta manter alguns dados alterando a estrutura atual.</small>";
        echo "</label>";
        echo "</div>";
        
        echo "<div style='margin: 20px 0;'>";
        echo "<button type='submit' class='btn btn-primary'>🔄 Executar Reversão</button>";
        echo "</div>";
        
        echo "</form>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Erro geral:</h3>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}

echo "<br><a href='gerenciar_vinculos.php'>← Voltar para Gerenciar Vínculos</a>";
?>

<style>
.btn { 
    display: inline-block; 
    padding: 10px 15px; 
    color: white; 
    text-decoration: none; 
    border-radius: 4px; 
    border: none;
    cursor: pointer;
    margin: 5px;
}
.btn-primary { background: #007bff; }
.btn:hover { opacity: 0.8; }
table { font-size: 12px; }
th, td { padding: 8px; text-align: left; }
input[type="radio"] { margin-right: 5px; }
label { cursor: pointer; }
</style>
