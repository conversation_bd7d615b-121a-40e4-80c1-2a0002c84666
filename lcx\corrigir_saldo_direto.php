<?php
session_start();
require_once '../config/database.php';

// Verificar se o usuário está logado e é admin
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

echo "<h2>Correção Direta do Saldo Master</h2>";

try {
    // Buscar livro master
    $stmt = $pdo->query("SELECT id, nome, saldo_inicial, saldo_atual FROM lcx_livros_caixa WHERE tipo = 'master' ORDER BY id DESC LIMIT 1");
    $livro_master = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$livro_master) {
        echo "<p style='color: red;'>Livro master não encontrado!</p>";
        exit;
    }
    
    echo "<h3>Livro Master: {$livro_master['nome']} (ID: {$livro_master['id']})</h3>";
    echo "<p><strong>Saldo Inicial:</strong> R$ " . number_format($livro_master['saldo_inicial'], 2, ',', '.') . "</p>";
    echo "<p><strong>Saldo Atual (antes):</strong> R$ " . number_format($livro_master['saldo_atual'], 2, ',', '.') . "</p>";
    
    // Buscar movimentações do PA 9 especificamente
    $stmt = $pdo->prepare("
        SELECT id, tipo, valor, saldo_anterior, saldo_posterior, data_competencia, data_movimentacao, categoria
        FROM lcx_movimentacoes 
        WHERE livro_caixa_id = ? 
        AND categoria LIKE '%PA: 9%'
        ORDER BY data_competencia ASC, data_movimentacao ASC, id ASC
    ");
    $stmt->execute([$livro_master['id']]);
    $movimentacoes_pa9 = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Movimentações PA 9 no Master:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th>ID</th><th>Data</th><th>Tipo</th><th>Valor</th>";
    echo "<th>Saldo Anterior</th><th>Saldo Posterior</th><th>Saldo Calculado</th>";
    echo "</tr>";
    
    $saldo_calculado = floatval($livro_master['saldo_inicial']);
    $movimentacoes_corrigidas = [];
    
    // Primeiro, buscar TODAS as movimentações para calcular o contexto correto
    $stmt = $pdo->prepare("
        SELECT id, tipo, valor, data_competencia, data_movimentacao
        FROM lcx_movimentacoes 
        WHERE livro_caixa_id = ? 
        ORDER BY data_competencia ASC, data_movimentacao ASC, id ASC
    ");
    $stmt->execute([$livro_master['id']]);
    $todas_movimentacoes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Recalcular saldo considerando TODAS as movimentações
    $saldo_acumulado = floatval($livro_master['saldo_inicial']);
    $saldos_corretos = [];
    
    foreach ($todas_movimentacoes as $mov) {
        $saldo_anterior = $saldo_acumulado;
        
        if ($mov['tipo'] === 'entrada') {
            $saldo_acumulado += floatval($mov['valor']);
        } else {
            $saldo_acumulado -= floatval($mov['valor']);
        }
        
        $saldos_corretos[$mov['id']] = [
            'saldo_anterior' => $saldo_anterior,
            'saldo_posterior' => $saldo_acumulado
        ];
    }
    
    // Mostrar apenas as do PA 9 com saldos corretos
    foreach ($movimentacoes_pa9 as $mov) {
        $saldo_correto = $saldos_corretos[$mov['id']];
        
        echo "<tr>";
        echo "<td>{$mov['id']}</td>";
        echo "<td>{$mov['data_competencia']}</td>";
        echo "<td>{$mov['tipo']}</td>";
        echo "<td>R$ " . number_format($mov['valor'], 2, ',', '.') . "</td>";
        echo "<td>R$ " . number_format($mov['saldo_anterior'], 2, ',', '.') . "</td>";
        echo "<td>R$ " . number_format($mov['saldo_posterior'], 2, ',', '.') . "</td>";
        echo "<td>R$ " . number_format($saldo_correto['saldo_posterior'], 2, ',', '.') . "</td>";
        echo "</tr>";
        
        if (abs($mov['saldo_posterior'] - $saldo_correto['saldo_posterior']) > 0.01) {
            $movimentacoes_corrigidas[] = [
                'id' => $mov['id'],
                'saldo_anterior_atual' => $mov['saldo_anterior'],
                'saldo_posterior_atual' => $mov['saldo_posterior'],
                'saldo_anterior_correto' => $saldo_correto['saldo_anterior'],
                'saldo_posterior_correto' => $saldo_correto['saldo_posterior']
            ];
        }
    }
    
    echo "</table>";
    
    echo "<h3>Saldo Final Calculado: R$ " . number_format($saldo_acumulado, 2, ',', '.') . "</h3>";
    
    if (!empty($movimentacoes_corrigidas)) {
        echo "<h3>Movimentações que precisam de correção:</h3>";
        foreach ($movimentacoes_corrigidas as $correcao) {
            echo "<p>ID {$correcao['id']}: Saldo posterior {$correcao['saldo_posterior_atual']} → {$correcao['saldo_posterior_correto']}</p>";
        }
        
        if (isset($_GET['executar']) && $_GET['executar'] === 'sim') {
            echo "<h3>Executando correção...</h3>";
            
            $pdo->beginTransaction();
            
            try {
                // Recalcular e atualizar TODAS as movimentações
                $saldo_recalculado = floatval($livro_master['saldo_inicial']);
                
                foreach ($todas_movimentacoes as $mov) {
                    $saldo_anterior = $saldo_recalculado;
                    
                    if ($mov['tipo'] === 'entrada') {
                        $saldo_recalculado += floatval($mov['valor']);
                    } else {
                        $saldo_recalculado -= floatval($mov['valor']);
                    }
                    
                    // Atualizar movimentação
                    $stmt = $pdo->prepare("
                        UPDATE lcx_movimentacoes 
                        SET saldo_anterior = ?, saldo_posterior = ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([$saldo_anterior, $saldo_recalculado, $mov['id']]);
                }
                
                // Atualizar saldo atual do livro
                $stmt = $pdo->prepare("UPDATE lcx_livros_caixa SET saldo_atual = ? WHERE id = ?");
                $stmt->execute([$saldo_recalculado, $livro_master['id']]);
                
                $pdo->commit();
                
                echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
                echo "<h4 style='color: #155724; margin-top: 0;'>✅ Correção Executada!</h4>";
                echo "<p style='color: #155724;'><strong>Novo saldo do livro master:</strong> R$ " . number_format($saldo_recalculado, 2, ',', '.') . "</p>";
                echo "</div>";
                
                echo "<p><a href='corrigir_saldo_direto.php'>Verificar novamente</a></p>";
                
            } catch (Exception $e) {
                $pdo->rollback();
                echo "<p style='color: red;'>Erro: " . $e->getMessage() . "</p>";
            }
            
        } else {
            echo "<p><a href='?executar=sim' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Executar Correção</a></p>";
        }
    } else {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h4 style='color: #155724; margin-top: 0;'>✅ Saldos Corretos!</h4>";
        echo "<p style='color: #155724; margin-bottom: 0;'>Todas as movimentações estão com saldos corretos.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Erro:</strong> " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; font-size: 12px; }
th, td { padding: 4px 8px; text-align: left; }
th { background: #f8f9fa; }
</style>
