<?php
// Desabilitar exibição de erros
error_reporting(0);
ini_set('display_errors', 0);

// Definir que a resposta será um JSON
header('Content-Type: application/json');

require_once '../../auth_check.php';
require_once '../../config/database.php';

try {
    // Validar método da requisição
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Método não permitido');
    }

    // Validar campos obrigatórios
    $required_fields = ['processo_id', 'numero_processo', 'data_ajuizamento', 'valor_ajuizado'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            throw new Exception("Campo {$field} é obrigatório");
        }
    }

    // Formatar valor
    $valor_ajuizado = str_replace(['R$', '.', ','], ['', '', '.'], $_POST['valor_ajuizado']);
    if (!is_numeric($valor_ajuizado)) {
        throw new Exception('Valor ajuizado inválido');
    }

    // Verificar se o processo existe
    $stmt = $pdo->prepare("SELECT id FROM cbp_processos_judiciais WHERE id = ?");
    $stmt->execute([$_POST['processo_id']]);
    if (!$stmt->fetch()) {
        throw new Exception('Processo não encontrado');
    }

    // Iniciar transação
    $pdo->beginTransaction();

    // Atualizar processo
    $stmt = $pdo->prepare("
        UPDATE cbp_processos_judiciais 
        SET data_ajuizamento = ?, 
            numero_processo = ?, 
            valor_ajuizado = ?,
            updated_at = NOW() 
        WHERE id = ?
    ");

    $stmt->execute([
        $_POST['data_ajuizamento'],
        $_POST['numero_processo'],
        $valor_ajuizado,
        $_POST['processo_id']
    ]);

    // Registrar no log
    $detalhes = "Ajuizamento de processo - ID: " . $_POST['processo_id'] . 
                " - Nº Processo: " . $_POST['numero_processo'] . 
                " - Data: " . date('d/m/Y', strtotime($_POST['data_ajuizamento'])) . 
                " - Valor: R$ " . number_format($valor_ajuizado, 2, ',', '.');
    
    $stmt = $pdo->prepare("INSERT INTO logs (usuario_id, acao, detalhes, data_hora) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$_SESSION['user_id'], 'Ajuizamento de Processo', $detalhes]);

    // Commit da transação
    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Ajuizamento salvo com sucesso'
    ]);

} catch (Exception $e) {
    // Rollback em caso de erro
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 