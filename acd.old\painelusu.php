<?php
session_start();
require_once '../config/database.php';
require_once 'check_acd_permission.php';
require_once 'vinculos_functions.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se o usuário tem permissão para acessar a pasta ACD
if (!checkACDPermission($_SESSION['user_id'])) {
    header('Location: unauthorized.php');
    exit;
}

// Função para buscar usuários da API
function buscarTodosUsuarios() {
    $apiFields = [
        'api_user' => 'UFL7GXZ14LU9NOR',
        'api_token' => '20OMTS-DQFG03-9NX1R9-LL65PP-G5LF4R-XMNH4G-XEC336-X6AKPG',
        'api_module' => 'Usuarios',
        'api_action' => 'listarUsuarios'
    ];

    $curl = curl_init();
    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://intranet.sicoobcredilivre.com.br/api',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => http_build_query($apiFields),
    ));
    
    $response = curl_exec($curl);
    
    if (curl_errno($curl)) {
        curl_close($curl);
        return [];
    }
    
    curl_close($curl);
    
    $usuarios = json_decode($response, true);
    
    if (!is_array($usuarios)) {
        return [];
    }
    
    $usuarios_mapeados = [];
    foreach ($usuarios as $user) {
        if (isset($user['status']) && $user['status'] == 1 && 
            isset($user['bloqueado']) && $user['bloqueado'] == 0) {
            $usuarios_mapeados[$user['id']] = [
                'id' => $user['id'],
                'nome' => $user['nome'] ?? '',
                'pa' => $user['nomeAgencia'] ?? '',
                'login' => $user['loginAD'] ?? $user['loginSISBR'] ?? ''
            ];
        }
    }
    
    return $usuarios_mapeados;
}

try {
    // Buscar dados do usuário logado
    $user_id = $_SESSION['user_id'];
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
    $stmt->execute([$user_id]);
    $usuario = $stmt->fetch(PDO::FETCH_ASSOC);

    // Buscar usuários da API
    $usuarios_api = buscarTodosUsuarios();

    // Buscar histórico completo de PAs pelos quais o usuário é/foi responsável
    $stmt = $pdo->prepare("
        SELECT
            v.id,
            v.pa_id,
            v.data_inicio,
            v.data_fim,
            v.status,
            v.observacoes,
            pa.id as pa_id_real,
            pa.nome as pa_nome,
            pa.numero as pa_numero
        FROM acd_usuario_pa v
        INNER JOIN pontos_atendimento pa ON v.pa_id = pa.id
        WHERE v.usuario_id = ?
        ORDER BY v.data_inicio DESC
    ");
    $stmt->execute([$user_id]);
    $historico_vinculos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Separar PAs ativos e histórico
    $pas_responsavel_ativos = [];
    $pas_responsavel_historico = [];

    foreach ($historico_vinculos as $vinculo) {
        $is_ativo = ($vinculo['status'] == 'ativo' || $vinculo['status'] == 1) &&
                   (is_null($vinculo['data_fim']) || $vinculo['data_fim'] > date('Y-m-d'));

        if ($is_ativo) {
            $pas_responsavel_ativos[] = $vinculo;
        }
        $pas_responsavel_historico[] = $vinculo;
    }
    
    // Métricas do usuário (propostas feitas por ele)
    $metricas_usuario = [
        'hoje' => ['total' => 0, 'submeter' => 0, 'devolver' => 0],
        'semana' => ['total' => 0, 'submeter' => 0, 'devolver' => 0],
        'mes' => ['total' => 0, 'submeter' => 0, 'devolver' => 0],
        'ano' => ['total' => 0, 'submeter' => 0, 'devolver' => 0]
    ];
    
    // Buscar métricas do usuário por período
    $periodos = [
        'hoje' => "DATE(data_criacao) = CURDATE()",
        'semana' => "YEARWEEK(data_criacao, 1) = YEARWEEK(CURDATE(), 1)",
        'mes' => "YEAR(data_criacao) = YEAR(CURDATE()) AND MONTH(data_criacao) = MONTH(CURDATE())",
        'ano' => "YEAR(data_criacao) = YEAR(CURDATE())"
    ];
    
    foreach ($periodos as $periodo => $condicao) {
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) as submeter,
                SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolver
            FROM acd_formularios 
            WHERE usuario_id = ? AND $condicao
        ");
        $stmt->execute([$user_id]);
        $resultado = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $metricas_usuario[$periodo] = [
            'total' => intval($resultado['total']),
            'submeter' => intval($resultado['submeter']),
            'devolver' => intval($resultado['devolver'])
        ];
    }
    
    // Métricas dos PAs pelos quais é/foi responsável (considerando datas dos vínculos)
    $metricas_pas = [];

    if (!empty($historico_vinculos)) {
        foreach ($periodos as $periodo => $condicao) {
            $metricas_pas[$periodo] = [];

            foreach ($historico_vinculos as $vinculo) {
                $pa_nome = $vinculo['pa_nome'];

                // Determinar período de responsabilidade
                $data_inicio_vinculo = $vinculo['data_inicio'];
                $data_fim_vinculo = $vinculo['data_fim'] ?? date('Y-m-d'); // Se não tem fim, usa hoje

                // Construir condição SQL que considera o período do vínculo
                $condicao_com_vinculo = "($condicao) AND DATE(f.data_criacao) >= ? AND DATE(f.data_criacao) <= ?";

                $stmt = $pdo->prepare("
                    SELECT
                        COUNT(*) as total,
                        SUM(CASE WHEN acao = 'submeter' THEN 1 ELSE 0 END) as submeter,
                        SUM(CASE WHEN acao = 'devolver' THEN 1 ELSE 0 END) as devolver
                    FROM acd_formularios f
                    WHERE f.pa = ? AND $condicao_com_vinculo
                ");

                $stmt->execute([$pa_nome, $data_inicio_vinculo, $data_fim_vinculo]);
                $resultado = $stmt->fetch(PDO::FETCH_ASSOC);

                // Se já existe dados para este PA no período, somar
                if (isset($metricas_pas[$periodo][$pa_nome])) {
                    $metricas_pas[$periodo][$pa_nome]['total'] += intval($resultado['total']);
                    $metricas_pas[$periodo][$pa_nome]['submeter'] += intval($resultado['submeter']);
                    $metricas_pas[$periodo][$pa_nome]['devolver'] += intval($resultado['devolver']);
                } else {
                    $metricas_pas[$periodo][$pa_nome] = [
                        'total' => intval($resultado['total']),
                        'submeter' => intval($resultado['submeter']),
                        'devolver' => intval($resultado['devolver']),
                        'vinculo_inicio' => $data_inicio_vinculo,
                        'vinculo_fim' => $vinculo['data_fim'],
                        'vinculo_ativo' => ($vinculo['status'] == 'ativo' || $vinculo['status'] == 1) &&
                                         (is_null($vinculo['data_fim']) || $vinculo['data_fim'] > date('Y-m-d'))
                    ];
                }
            }
        }
    }
    
} catch (Exception $e) {
    error_log("Erro ao buscar métricas do usuário: " . $e->getMessage());
    $usuario = [];
    $pas_responsavel = [];
    $metricas_usuario = [];
    $metricas_pas = [];
    $usuarios_api = [];
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Painel do Usuário - ACD</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #003641;
            --secondary-color: #00AE9D;
            --text-light: #666666;
            --text-dark: #333333;
            --bg-light: #f5f5f5;
            --bg-white: #ffffff;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: var(--bg-light);
            margin: 0;
            padding: 0;
        }

        .wrapper {
            display: flex;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
            padding: 1.5rem;
            margin-left: 250px;
        }

        .page-title {
            color: var(--primary-color);
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .user-info {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .user-name {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .user-details {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .metrics-section {
            margin-bottom: 2rem;
        }

        .section-title {
            color: var(--primary-color);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .metric-card {
            background: var(--bg-white);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .metric-title {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--text-light);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .metric-breakdown {
            display: flex;
            gap: 1rem;
            font-size: 0.85rem;
        }

        .metric-submeter {
            color: #28a745;
            font-weight: 600;
        }

        .metric-devolver {
            color: #dc3545;
            font-weight: 600;
        }

        .pas-section {
            background: var(--bg-white);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            border: 1px solid #e0e0e0;
        }

        .pa-item {
            border-bottom: 1px solid #e0e0e0;
            padding: 1rem 0;
        }

        .pa-item:last-child {
            border-bottom: none;
        }

        .pa-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .pa-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .pa-metric {
            background: #f8f9fa;
            padding: 0.75rem;
            border-radius: 8px;
            text-align: center;
        }

        .pa-metric-period {
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--text-light);
            margin-bottom: 0.25rem;
        }

        .pa-metric-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .pa-metric-breakdown {
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        .no-data {
            text-align: center;
            color: var(--text-light);
            font-style: italic;
            padding: 2rem;
        }

        /* Responsividade */
        @media (max-width: 1200px) {
            .main-content {
                margin-left: 0;
                padding: 1rem;
            }
        }

        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .pa-metrics {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <?php include 'components/sidebar.php'; ?>
        
        <div class="main-content">
            <h1 class="page-title">
                <i class="fas fa-user-chart"></i>
                Painel do Usuário
            </h1>

            <!-- Informações do Usuário -->
            <div class="user-info">
                <div class="user-name"><?php echo htmlspecialchars($usuario['nome_completo'] ?? 'Usuário'); ?></div>
                <div class="user-details">
                    <i class="fas fa-envelope"></i> <?php echo htmlspecialchars($usuario['email'] ?? 'N/A'); ?>
                    <?php if (!empty($pas_responsavel_ativos)): ?>
                        | <i class="fas fa-building"></i>
                        <?php echo count($pas_responsavel_ativos); ?> PA(s) ativo(s)
                    <?php endif; ?>
                </div>
            </div>

            <!-- Resumo Geral -->
            <div class="metrics-section">
                <h2 class="section-title">
                    <i class="fas fa-chart-pie"></i>
                    Resumo Geral
                </h2>

                <div class="pas-section">
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-title">Propostas analisadas</div>
                            <div class="metric-value"><?php echo $metricas_usuario['ano']['total']; ?></div>
                            <div class="metric-breakdown">
                                Suas propostas analisadas este ano
                            </div>
                        </div>

                        <div class="metric-card">
                            <div class="metric-title">Meus PAs</div>
                            <div class="metric-value">
                                <?php
                                // Contar PAs ativos e inativos baseado na data de fim
                                $pas_ativos_count = 0;
                                $pas_inativos_count = 0;

                                foreach ($historico_vinculos as $vinculo) {
                                    // Se não tem data de fim = ativo, se tem data de fim = inativo
                                    $is_ativo = (is_null($vinculo['data_fim']) || $vinculo['data_fim'] === null || $vinculo['data_fim'] === '');

                                    if ($is_ativo) {
                                        $pas_ativos_count++;
                                    } else {
                                        $pas_inativos_count++;
                                    }
                                }

                                echo $pas_ativos_count + $pas_inativos_count;
                                ?>
                            </div>
                            <div class="metric-breakdown">
                                <?php echo $pas_ativos_count; ?> ativo(s) | <?php echo $pas_inativos_count; ?> inativo(s)
                            </div>
                        </div>

                        <?php
                        $taxa_geral = $metricas_usuario['ano']['total'] > 0 ?
                            round(($metricas_usuario['ano']['devolver'] / $metricas_usuario['ano']['total']) * 100, 1) : 0;
                        ?>
                        <div class="metric-card">
                            <div class="metric-title">Taxa de devolução anual</div>
                            <div class="metric-value" style="color: <?php echo $taxa_geral > 20 ? '#dc3545' : '#28a745'; ?>;">
                                <?php echo $taxa_geral; ?>%
                            </div>
                            <div class="metric-breakdown">
                                Percentual de propostas devolvidas
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Métricas do Usuário -->
            <div class="metrics-section">
                <h2 class="section-title">
                    <i class="fas fa-chart-line"></i>
                    Minhas Propostas
                </h2>

                <div class="metrics-grid">
                    <?php
                    $periodos_nomes = [
                        'hoje' => 'Hoje',
                        'semana' => 'Esta Semana',
                        'mes' => 'Este Mês',
                        'ano' => 'Este Ano'
                    ];

                    foreach ($periodos_nomes as $periodo => $nome):
                        $dados = $metricas_usuario[$periodo] ?? ['total' => 0, 'submeter' => 0, 'devolver' => 0];
                        $taxa_devolucao = $dados['total'] > 0 ? round(($dados['devolver'] / $dados['total']) * 100, 1) : 0;
                    ?>
                        <div class="metric-card">
                            <div class="metric-title"><?php echo $nome; ?></div>
                            <div class="metric-value"><?php echo $dados['total']; ?></div>
                            <div class="metric-breakdown">
                                <span class="metric-submeter">
                                    <i class="fas fa-check"></i> <?php echo $dados['submeter']; ?> Aprovadas
                                </span>
                                <span class="metric-devolver">
                                    <i class="fas fa-times"></i> <?php echo $dados['devolver']; ?> Devolvidas
                                </span>
                            </div>
                            <?php if ($dados['total'] > 0): ?>
                                <div style="margin-top: 0.5rem; font-size: 0.8rem; color: <?php echo $taxa_devolucao > 20 ? '#dc3545' : '#28a745'; ?>;">
                                    Taxa de devolução: <?php echo $taxa_devolucao; ?>%
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- PAs de Responsabilidade -->
            <div class="metrics-section">
                <h2 class="section-title">
                    <i class="fas fa-building"></i>
                    Meus PAs de Responsabilidade
                </h2>

                <?php if (!empty($pas_responsavel_ativos)): ?>
                    <div class="pas-section">
                        <h4 style="color: var(--secondary-color); margin-bottom: 1rem;">
                            <i class="fas fa-check-circle"></i> PAs Ativos
                        </h4>
                        <?php foreach ($pas_responsavel_ativos as $pa): ?>
                            <div class="pa-item">
                                <div class="pa-name">
                                    <?php echo htmlspecialchars($pa['pa_nome']); ?>
                                    <small class="text-success">
                                        <i class="fas fa-calendar-check"></i>
                                        Responsável desde <?php echo date('d/m/Y', strtotime($pa['data_inicio'])); ?>
                                    </small>
                                </div>
                                <div class="pa-metrics">
                                    <?php foreach ($periodos_nomes as $periodo => $nome_periodo): ?>
                                        <?php
                                        $dados_pa = $metricas_pas[$periodo][$pa['pa_nome']] ?? ['total' => 0, 'submeter' => 0, 'devolver' => 0];
                                        $taxa_devolucao_pa = $dados_pa['total'] > 0 ? round(($dados_pa['devolver'] / $dados_pa['total']) * 100, 1) : 0;
                                        ?>
                                        <div class="pa-metric">
                                            <div class="pa-metric-period"><?php echo $nome_periodo; ?></div>
                                            <div class="pa-metric-value"><?php echo $dados_pa['total']; ?></div>
                                            <div class="pa-metric-breakdown">
                                                <span class="metric-submeter"><?php echo $dados_pa['submeter']; ?> aprovadas</span> |
                                                <span class="metric-devolver"><?php echo $dados_pa['devolver']; ?> devolvidas</span>
                                                <?php if ($dados_pa['total'] > 0): ?>
                                                    <br><span style="color: <?php echo $taxa_devolucao_pa > 20 ? '#dc3545' : '#28a745'; ?>;">
                                                        Devolução: <?php echo $taxa_devolucao_pa; ?>%
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <?php
                // PAs históricos (não ativos)
                $pas_historicos = array_filter($historico_vinculos, function($vinculo) {
                    return !($vinculo['status'] == 'ativo' || $vinculo['status'] == 1) ||
                           (!is_null($vinculo['data_fim']) && $vinculo['data_fim'] <= date('Y-m-d'));
                });
                ?>

                <?php if (!empty($pas_historicos)): ?>
                    <div class="pas-section" style="margin-top: 1.5rem;">
                        <h4 style="color: var(--text-light); margin-bottom: 1rem;">
                            <i class="fas fa-history"></i> Histórico de Responsabilidades
                        </h4>
                        <?php foreach ($pas_historicos as $pa): ?>
                            <div class="pa-item" style="opacity: 0.8;">
                                <div class="pa-name">
                                    <?php echo htmlspecialchars($pa['pa_nome']); ?>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar"></i>
                                        <?php echo date('d/m/Y', strtotime($pa['data_inicio'])); ?>
                                        <?php if ($pa['data_fim']): ?>
                                            até <?php echo date('d/m/Y', strtotime($pa['data_fim'])); ?>
                                        <?php endif; ?>
                                        <?php if ($pa['observacoes']): ?>
                                            | <?php echo htmlspecialchars($pa['observacoes']); ?>
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <div class="pa-metrics">
                                    <?php foreach ($periodos_nomes as $periodo => $nome_periodo): ?>
                                        <?php
                                        $dados_pa = $metricas_pas[$periodo][$pa['pa_nome']] ?? ['total' => 0, 'submeter' => 0, 'devolver' => 0];
                                        $taxa_devolucao_pa = $dados_pa['total'] > 0 ? round(($dados_pa['devolver'] / $dados_pa['total']) * 100, 1) : 0;
                                        ?>
                                        <div class="pa-metric" style="background: #f1f3f4;">
                                            <div class="pa-metric-period"><?php echo $nome_periodo; ?></div>
                                            <div class="pa-metric-value" style="color: var(--text-light);"><?php echo $dados_pa['total']; ?></div>
                                            <div class="pa-metric-breakdown">
                                                <span class="metric-submeter"><?php echo $dados_pa['submeter']; ?> aprovadas</span> |
                                                <span class="metric-devolver"><?php echo $dados_pa['devolver']; ?> devolvidas</span>
                                                <?php if ($dados_pa['total'] > 0): ?>
                                                    <br><span style="color: <?php echo $taxa_devolucao_pa > 20 ? '#dc3545' : '#28a745'; ?>;">
                                                        Devolução: <?php echo $taxa_devolucao_pa; ?>%
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

                <?php if (empty($historico_vinculos)): ?>
                    <div class="pas-section">
                        <div class="no-data">
                            <i class="fas fa-info-circle" style="font-size: 2rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                            <h4>Nenhum PA sob sua responsabilidade</h4>
                            <p>Você não possui PAs vinculados à sua conta no momento.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>


        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
