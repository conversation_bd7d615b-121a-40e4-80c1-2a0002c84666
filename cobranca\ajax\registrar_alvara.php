<?php
require_once '../auth_check.php';
require_once '../config/database.php';

header('Content-Type: application/json');
error_log('Iniciando registro de alvará...');

try {
    // Validar dados recebidos
    if (empty($_POST['processo_id']) || empty($_POST['data_recebimento']) || empty($_POST['valor'])) {
        throw new Exception('Todos os campos obrigatórios devem ser preenchidos.');
    }

    $processo_id = intval($_POST['processo_id']);
    $data_recebimento = $_POST['data_recebimento'];
    $valor = floatval(str_replace(',', '.', $_POST['valor']));
    $observacoes = $_POST['observacoes'] ?? null;

    error_log("Dados do alvará - Processo: $processo_id, Data: $data_recebimento, Valor: $valor");

    // Validar se o processo existe
    $stmt = $pdo->prepare("SELECT id FROM cbp_processos_judiciais WHERE id = ?");
    $stmt->execute([$processo_id]);
    if (!$stmt->fetch()) {
        throw new Exception('Processo não encontrado.');
    }

    // Iniciar transação
    $pdo->beginTransaction();

    // Inserir alvará
    $stmt = $pdo->prepare("
        INSERT INTO cbp_alvaras (
            processo_id,
            valor,
            data_recebimento,
            observacoes,
            created_at,
            updated_at
        ) VALUES (
            :processo_id,
            :valor,
            :data_recebimento,
            :observacoes,
            NOW(),
            NOW()
        )
    ");

    $stmt->execute([
        'processo_id' => $processo_id,
        'valor' => $valor,
        'data_recebimento' => $data_recebimento,
        'observacoes' => $observacoes
    ]);

    $alvara_id = $pdo->lastInsertId();
    error_log("Alvará inserido com ID: $alvara_id");

    // Commit da transação
    $pdo->commit();
    error_log("Transação commitada com sucesso");

    // Registrar honorário usando debug_alvaras.php
    try {
        error_log("Executando arquivo debug_alvaras.php para registrar honorário");
        include_once(dirname(__DIR__) . '/debug_alvaras.php');
        error_log("Arquivo debug_alvaras.php executado com sucesso");
    } catch (Exception $e) {
        error_log("Erro ao executar debug_alvaras.php: " . $e->getMessage());
        // Não interromper o fluxo principal
    }

    echo json_encode([
        'success' => true,
        'message' => 'Alvará registrado com sucesso!'
    ]);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("Erro ao registrar alvará: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} catch (PDOException $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("Erro PDO ao registrar alvará: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao registrar alvará no banco de dados.'
    ]);
} 