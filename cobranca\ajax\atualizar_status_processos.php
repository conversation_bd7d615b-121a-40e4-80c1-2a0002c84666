<?php
require_once '../../auth_check.php';
require_once '../../config/database.php';

header('Content-Type: application/json');

try {
    // Iniciar transação
    $pdo->beginTransaction();

    // Buscar ID do status QUITADO
    $stmt = $pdo->prepare("SELECT id FROM cbp_status_processo WHERE nome = 'QUITADO'");
    $stmt->execute();
    $status_quitado_id = $stmt->fetchColumn();

    if (!$status_quitado_id) {
        throw new Exception('Status QUITADO não encontrado');
    }

    // Buscar todos os processos que têm acordos ativos
    $stmt = $pdo->prepare("
        SELECT DISTINCT p.id, p.status_id, s.nome as status_nome
        FROM cbp_processos_judiciais p
        INNER JOIN cbp_acordos a ON a.processo_id = p.id
        LEFT JOIN cbp_status_processo s ON p.status_id = s.id
        WHERE a.ativo = 1
        AND p.status_id != ?
    ");
    $stmt->execute([$status_quitado_id]);
    $processos = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $processos_atualizados = [];

    foreach ($processos as $processo) {
        // Verificar se todos os acordos ativos do processo estão quitados
        $stmt = $pdo->prepare("
            SELECT a.id 
            FROM cbp_acordos a
            WHERE a.processo_id = ?
            AND a.ativo = 1
            AND (
                EXISTS (
                    SELECT 1 
                    FROM cbp_parcelas_acordo pa 
                    WHERE pa.acordo_id = a.id 
                    AND pa.status != 'PAGO'
                )
                OR EXISTS (
                    SELECT 1 
                    FROM cbp_alvaras_acordo aa 
                    WHERE aa.acordo_id = a.id 
                    AND aa.status = 'PENDENTE'
                )
            )
        ");
        $stmt->execute([$processo['id']]);
        $acordos_pendentes = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Se não existem acordos pendentes, atualizar para QUITADO
        if (empty($acordos_pendentes)) {
            // Atualizar status do processo
            $stmt = $pdo->prepare("
                UPDATE cbp_processos_judiciais 
                SET status_id = ?,
                    updated_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$status_quitado_id, $processo['id']]);

            // Registrar no histórico
            $stmt = $pdo->prepare("
                INSERT INTO cbp_historico_status 
                (processo_id, status_id, data_alteracao, observacoes) 
                VALUES (?, ?, NOW(), ?)
            ");
            $stmt->execute([
                $processo['id'],
                $status_quitado_id,
                'Status atualizado automaticamente para QUITADO'
            ]);

            $processos_atualizados[] = $processo['id'];
        }
    }

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'atualizados' => count($processos_atualizados),
        'ids' => $processos_atualizados
    ]);

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 