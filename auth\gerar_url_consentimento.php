<?php
require_once __DIR__ . '/../config/msgraph_oauth.php';

function get_redirect_uri() {
    $host = $_SERVER['HTTP_HOST'];
    $path = OAUTH_CALLBACK_PATH;
    if ($host === 'localhost') {
        $protocol = 'http';
    } else {
        $protocol = 'https';
    }
    return $protocol . '://' . $host . $path;
}

$state = bin2hex(random_bytes(8));
$params = http_build_query([
    'client_id' => OAUTH_CLIENT_ID,
    'response_type' => 'code',
    'redirect_uri' => get_redirect_uri(),
    'response_mode' => 'query',
    'scope' => OAUTH_SCOPES,
    'state' => $state,
]);

$url = 'https://login.microsoftonline.com/' . OAUTH_TENANT_ID . '/oauth2/v2.0/authorize?' . $params;

?><!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Gerar URL de Consentimento Microsoft OAuth2</title>
</head>
<body>
    <h2>URL de Consentimento Microsoft OAuth2</h2>
    <p>Copie e acesse a URL abaixo no navegador para conceder o consentimento:</p>
    <textarea style="width:100%;height:80px;" readonly><?php echo htmlspecialchars($url); ?></textarea>
    <p><a href="<?php echo htmlspecialchars($url); ?>" target="_blank">Abrir URL de Consentimento</a></p>
    <p><strong>Redirect URI utilizado:</strong> <?php echo htmlspecialchars(get_redirect_uri()); ?></p>
</body>
</html> 