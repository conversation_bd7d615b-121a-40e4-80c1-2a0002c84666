<?php
require_once '../auth_check.php';
require_once '../config/database.php';
require_once '../includes/hooks/honorarios_hooks.php';

header('Content-Type: application/json');
error_log('Iniciando registro de acordo...');

try {
    // Validar dados recebidos
    if (!isset($_POST['processo_id']) || !isset($_POST['valor_total'])) {
        throw new Exception('Dados incompletos');
    }

    error_log('Dados do acordo: ' . print_r($_POST, true));

    // Iniciar transação
    $pdo->beginTransaction();

    // Inserir acordo
    $stmt = $pdo->prepare("
        INSERT INTO cbp_acordos (
            processo_id,
            valor_total,
            valor_entrada,
            data_entrada,
            quantidade_parcelas,
            valor_parcela,
            dia_vencimento,
            porcentagem_honorario,
            created_at,
            updated_at
        ) VALUES (
            :processo_id,
            :valor_total,
            :valor_entrada,
            :data_entrada,
            :quantidade_parcelas,
            :valor_parcela,
            :dia_vencimento,
            :porcentagem_honorario,
            NOW(),
            NOW()
        )
    ");

    $params = [
        'processo_id' => $_POST['processo_id'],
        'valor_total' => floatval(str_replace(',', '.', $_POST['valor_total'])),
        'valor_entrada' => isset($_POST['valor_entrada']) ? floatval(str_replace(',', '.', $_POST['valor_entrada'])) : 0,
        'data_entrada' => $_POST['data_entrada'] ?? null,
        'quantidade_parcelas' => $_POST['quantidade_parcelas'] ?? 1,
        'valor_parcela' => isset($_POST['valor_parcela']) ? floatval(str_replace(',', '.', $_POST['valor_parcela'])) : $_POST['valor_total'],
        'dia_vencimento' => $_POST['dia_vencimento'] ?? 10,
        'porcentagem_honorario' => isset($_POST['porcentagem_honorario']) ? floatval(str_replace(',', '.', $_POST['porcentagem_honorario'])) : null
    ];

    $stmt->execute($params);
    $acordo_id = $pdo->lastInsertId();
    error_log("Acordo inserido com ID: $acordo_id");

    // Se houver entrada, registrar honorários
    if (!empty($params['valor_entrada']) && !empty($params['data_entrada'])) {
        error_log("Registrando honorários para entrada do acordo");
        $hooks = new HonorariosHooks($pdo);
        $hooks->onEntradaPaga($acordo_id);
        
        // Executar o debug_entradas.php para garantir o registro de honorários
        try {
            // Passar o ID do acordo como contexto
            $acordo_id_para_debug = $acordo_id;
            error_log("Executando arquivo debug_entradas.php para acordo ID: $acordo_id");
            include_once(dirname(__DIR__) . '/debug_entradas.php');
            error_log("Arquivo debug_entradas.php executado com sucesso");
        } catch (Exception $e) {
            error_log("Erro ao executar debug_entradas.php: " . $e->getMessage());
            // Não interrompemos o fluxo principal se o debug falhar
        }
    }

    // Commit da transação
    $pdo->commit();
    error_log("Transação commitada com sucesso");

    echo json_encode([
        'success' => true,
        'acordo_id' => $acordo_id,
        'message' => 'Acordo registrado com sucesso!'
    ]);

} catch (Exception $e) {
    $pdo->rollBack();
    error_log("Erro ao registrar acordo: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 