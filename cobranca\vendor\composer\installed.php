<?php return array(
    'root' => array(
        'name' => 'cobranca/judicial',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '*******',
        'reference' => NULL,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'cobranca/judicial' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '*******',
            'reference' => NULL,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/pcre' => array(
            'pretty_version' => '3.3.2',
            'version' => '3.3.2.0',
            'reference' => 'b2bed4734f0cc156ee1fe9c0da2550420d99a21e',
            'type' => 'library',
            'install_path' => __DIR__ . '/./pcre',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dompdf/dompdf' => array(
            'pretty_version' => 'v3.1.0',
            'version' => '3.1.0.0',
            'reference' => 'a51bd7a063a65499446919286fb18b518177155a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/dompdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dompdf/php-font-lib' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '6137b7d4232b7f16c882c75e4ca3991dbcf6fe2d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/php-font-lib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dompdf/php-svg-lib' => array(
            'pretty_version' => '1.0.0',
            'version' => '*******',
            'reference' => 'eb045e518185298eb6ff8d80d0d0c6b17aecd9af',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/php-svg-lib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'maennchen/zipstream-php' => array(
            'pretty_version' => '3.1.2',
            'version' => '*******',
            'reference' => 'aeadcf5c412332eb426c0f9b4485f6accba2a99f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../maennchen/zipstream-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/complex' => array(
            'pretty_version' => '3.0.2',
            'version' => '*******',
            'reference' => '95c56caa1cf5c766ad6d65b6344b807c1e8405b9',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/complex',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'markbaker/matrix' => array(
            'pretty_version' => '3.0.1',
            'version' => '*******',
            'reference' => '728434227fe21be27ff6d86621a1b13107a2562c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../markbaker/matrix',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.9.0',
            'version' => '*******',
            'reference' => 'f5ac2c0b0a2eefca70b2ce32a5809992227e75a6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoffice/phpspreadsheet' => array(
            'pretty_version' => '4.4.0',
            'version' => '4.4.0.0',
            'reference' => '747ccd1b443e85e0cfc0d52acf50e4d15ef959eb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpspreadsheet',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/simple-cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => '764e0b3939f5ca87cb904f570ef9be2d78a07865',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/simple-cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sabberworm/php-css-parser' => array(
            'pretty_version' => 'v8.8.0',
            'version' => '8.8.0.0',
            'reference' => '3de493bdddfd1f051249af725c7e0d2c38fed740',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabberworm/php-css-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'setasign/fpdf' => array(
            'pretty_version' => '1.8.6',
            'version' => '1.8.6.0',
            'reference' => '0838e0ee4925716fcbbc50ad9e1799b5edfae0a0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../setasign/fpdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
