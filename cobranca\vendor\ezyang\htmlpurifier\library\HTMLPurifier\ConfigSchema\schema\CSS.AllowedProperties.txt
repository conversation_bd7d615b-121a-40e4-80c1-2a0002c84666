CSS.AllowedProperties
TYPE: lookup/null
VERSION: 3.1.0
DEFAULT: NULL
--DESCRIPTION--

<p>
    If HTML Purifier's style attributes set is unsatisfactory for your needs,
    you can overload it with your own list of tags to allow.  Note that this
    method is subtractive: it does its job by taking away from HTML Purifier
    usual feature set, so you cannot add an attribute that HTML Purifier never
    supported in the first place.
</p>
<p>
    <strong>Warning:</strong> If another directive conflicts with the
    elements here, <em>that</em> directive will win and override.
</p>
--# vim: et sw=4 sts=4
